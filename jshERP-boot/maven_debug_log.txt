Apache Maven 3.9.9 (8e8579a9e76f7d015ee5ec7bfcdc97d260186937)
Maven home: /opt/homebrew/Cellar/maven/3.9.9/libexec
Java version: 23.0.2, vendor: Homebrew, runtime: /opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home
Default locale: zh_CN_#Hans, platform encoding: UTF-8
OS name: "mac os x", version: "15.5", arch: "aarch64", family: "mac"
[DEBUG] Created new class realm maven.api
[DEBUG] Importing foreign packages into class realm maven.api
[DEBUG]   Imported: javax.annotation.* < plexus.core
[DEBUG]   Imported: javax.annotation.security.* < plexus.core
[DEBUG]   Imported: javax.inject.* < plexus.core
[DEBUG]   Imported: org.apache.maven.* < plexus.core
[DEBUG]   Imported: org.apache.maven.artifact < plexus.core
[DEBUG]   Imported: org.apache.maven.classrealm < plexus.core
[DEBUG]   Imported: org.apache.maven.cli < plexus.core
[DEBUG]   Imported: org.apache.maven.configuration < plexus.core
[DEBUG]   Imported: org.apache.maven.exception < plexus.core
[DEBUG]   Imported: org.apache.maven.execution < plexus.core
[DEBUG]   Imported: org.apache.maven.execution.scope < plexus.core
[DEBUG]   Imported: org.apache.maven.graph < plexus.core
[DEBUG]   Imported: org.apache.maven.lifecycle < plexus.core
[DEBUG]   Imported: org.apache.maven.model < plexus.core
[DEBUG]   Imported: org.apache.maven.monitor < plexus.core
[DEBUG]   Imported: org.apache.maven.plugin < plexus.core
[DEBUG]   Imported: org.apache.maven.profiles < plexus.core
[DEBUG]   Imported: org.apache.maven.project < plexus.core
[DEBUG]   Imported: org.apache.maven.reporting < plexus.core
[DEBUG]   Imported: org.apache.maven.repository < plexus.core
[DEBUG]   Imported: org.apache.maven.rtinfo < plexus.core
[DEBUG]   Imported: org.apache.maven.settings < plexus.core
[DEBUG]   Imported: org.apache.maven.toolchain < plexus.core
[DEBUG]   Imported: org.apache.maven.usability < plexus.core
[DEBUG]   Imported: org.apache.maven.wagon.* < plexus.core
[DEBUG]   Imported: org.apache.maven.wagon.authentication < plexus.core
[DEBUG]   Imported: org.apache.maven.wagon.authorization < plexus.core
[DEBUG]   Imported: org.apache.maven.wagon.events < plexus.core
[DEBUG]   Imported: org.apache.maven.wagon.observers < plexus.core
[DEBUG]   Imported: org.apache.maven.wagon.proxy < plexus.core
[DEBUG]   Imported: org.apache.maven.wagon.repository < plexus.core
[DEBUG]   Imported: org.apache.maven.wagon.resource < plexus.core
[DEBUG]   Imported: org.codehaus.classworlds < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.* < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.classworlds < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.component < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.configuration < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.container < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.context < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.lifecycle < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.logging < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.personality < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.util.xml.Xpp3Dom < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.util.xml.pull.XmlPullParser < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.util.xml.pull.XmlPullParserException < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.util.xml.pull.XmlSerializer < plexus.core
[DEBUG]   Imported: org.eclipse.aether.* < plexus.core
[DEBUG]   Imported: org.eclipse.aether.artifact < plexus.core
[DEBUG]   Imported: org.eclipse.aether.collection < plexus.core
[DEBUG]   Imported: org.eclipse.aether.deployment < plexus.core
[DEBUG]   Imported: org.eclipse.aether.graph < plexus.core
[DEBUG]   Imported: org.eclipse.aether.impl < plexus.core
[DEBUG]   Imported: org.eclipse.aether.installation < plexus.core
[DEBUG]   Imported: org.eclipse.aether.internal.impl < plexus.core
[DEBUG]   Imported: org.eclipse.aether.metadata < plexus.core
[DEBUG]   Imported: org.eclipse.aether.repository < plexus.core
[DEBUG]   Imported: org.eclipse.aether.resolution < plexus.core
[DEBUG]   Imported: org.eclipse.aether.spi < plexus.core
[DEBUG]   Imported: org.eclipse.aether.transfer < plexus.core
[DEBUG]   Imported: org.eclipse.aether.util < plexus.core
[DEBUG]   Imported: org.eclipse.aether.version < plexus.core
[DEBUG]   Imported: org.fusesource.jansi.* < plexus.core
[DEBUG]   Imported: org.slf4j.* < plexus.core
[DEBUG]   Imported: org.slf4j.event.* < plexus.core
[DEBUG]   Imported: org.slf4j.helpers.* < plexus.core
[DEBUG]   Imported: org.slf4j.spi.* < plexus.core
[DEBUG] Populating class realm maven.api
[DEBUG] Created adapter factory; available factories [file-lock, rwlock-local, semaphore-local, noop]; available name mappers [discriminating, file-gav, file-hgav, file-static, gav, static]
[INFO] Error stacktraces are turned on.
[DEBUG] Message scheme: color
[DEBUG] Message styles: debug info warning error success failure strong mojo project
[DEBUG] Reading global settings from /opt/homebrew/Cellar/maven/3.9.9/libexec/conf/settings.xml
[DEBUG] Reading user settings from /Users/<USER>/.m2/settings.xml
[DEBUG] Reading global toolchains from /opt/homebrew/Cellar/maven/3.9.9/libexec/conf/toolchains.xml
[DEBUG] Reading user toolchains from /Users/<USER>/.m2/toolchains.xml
[DEBUG] Using local repository at /Users/<USER>/.m2/repository
[DEBUG] Using manager EnhancedLocalRepositoryManager with priority 10.0 for /Users/<USER>/.m2/repository
[INFO] Scanning for projects...
[DEBUG] Creating adapter using nameMapper 'gav' and factory 'rwlock-local'
[DEBUG] Extension realms for project com.jsh:jshERP-boot:jar:3.5-SNAPSHOT: (none)
[DEBUG] Looking up lifecycle mappings for packaging jar from ClassRealm[plexus.core, parent: null]
[DEBUG] Extension realms for project org.springframework.boot:spring-boot-starter-parent:pom:2.0.0.RELEASE: (none)
[DEBUG] Looking up lifecycle mappings for packaging pom from ClassRealm[plexus.core, parent: null]
[DEBUG] Extension realms for project org.springframework.boot:spring-boot-dependencies:pom:2.0.0.RELEASE: (none)
[DEBUG] Looking up lifecycle mappings for packaging pom from ClassRealm[plexus.core, parent: null]
[DEBUG] Resolving plugin prefix spring-boot from [org.apache.maven.plugins, org.codehaus.mojo]
[DEBUG] Resolved plugin prefix spring-boot to org.springframework.boot:spring-boot-maven-plugin from POM com.jsh:jshERP-boot:jar:3.5-SNAPSHOT
[DEBUG] === REACTOR BUILD PLAN ================================================
[DEBUG] Project: com.jsh:jshERP-boot:jar:3.5-SNAPSHOT
[DEBUG] Tasks:   [spring-boot:run]
[DEBUG] Style:   Regular
[DEBUG] =======================================================================
[INFO] 
[INFO] ------------------------< com.jsh:jshERP-boot >-------------------------
[INFO] Building jshERP-boot 3.5-SNAPSHOT
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[DEBUG] Resolving plugin prefix spring-boot from [org.apache.maven.plugins, org.codehaus.mojo]
[DEBUG] Resolved plugin prefix spring-boot to org.springframework.boot:spring-boot-maven-plugin from POM com.jsh:jshERP-boot:jar:3.5-SNAPSHOT
[DEBUG] Lifecycle clean -> [pre-clean, clean, post-clean]
[DEBUG] Lifecycle default -> [validate, initialize, generate-sources, process-sources, generate-resources, process-resources, compile, process-classes, generate-test-sources, process-test-sources, generate-test-resources, process-test-resources, test-compile, process-test-classes, test, prepare-package, package, pre-integration-test, integration-test, post-integration-test, verify, install, deploy]
[DEBUG] Lifecycle site -> [pre-site, site, post-site, site-deploy]
[DEBUG] Lifecycle clean -> [pre-clean, clean, post-clean]
[DEBUG] Lifecycle default -> [validate, initialize, generate-sources, process-sources, generate-resources, process-resources, compile, process-classes, generate-test-sources, process-test-sources, generate-test-resources, process-test-resources, test-compile, process-test-classes, test, prepare-package, package, pre-integration-test, integration-test, post-integration-test, verify, install, deploy]
[DEBUG] Lifecycle site -> [pre-site, site, post-site, site-deploy]
[DEBUG] === PROJECT BUILD PLAN ================================================
[DEBUG] Project:       com.jsh:jshERP-boot:3.5-SNAPSHOT
[DEBUG] Dependencies (collect): []
[DEBUG] Dependencies (resolve): [test]
[DEBUG] Repositories (dependencies): [spring-milestone (https://repo.spring.io/milestone, default, releases), spring-snapshot (https://repo.spring.io/snapshot, default, releases+snapshots), rabbit-milestone (https://dl.bintray.com/rabbitmq/maven-milestones, default, releases), central (https://repo.maven.apache.org/maven2, default, releases)]
[DEBUG] Repositories (plugins)     : [central (https://repo.maven.apache.org/maven2, default, releases)]
[DEBUG] --- init fork of com.jsh:jshERP-boot:3.5-SNAPSHOT for org.springframework.boot:spring-boot-maven-plugin:2.0.3.RELEASE:run (default-cli) ---
[DEBUG] Dependencies (collect): []
[DEBUG] Dependencies (resolve): [compile, test]
[DEBUG] -----------------------------------------------------------------------
[DEBUG] Goal:          org.springframework.boot:spring-boot-maven-plugin:2.0.3.RELEASE:build-info (build-info)
[DEBUG] Style:         Regular
[DEBUG] Configuration: <?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <outputFile default-value="${project.build.outputDirectory}/META-INF/build-info.properties"/>
  <project default-value="${project}"/>
</configuration>
[DEBUG] -----------------------------------------------------------------------
[DEBUG] Goal:          org.apache.maven.plugins:maven-resources-plugin:3.0.1:resources (default-resources)
[DEBUG] Style:         Regular
[DEBUG] Configuration: <?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <addDefaultExcludes default-value="true"/>
  <buildFilters default-value="${project.build.filters}"/>
  <delimiters>
    <delimiter>@</delimiter>
  </delimiters>
  <encoding default-value="${project.build.sourceEncoding}"/>
  <escapeString default-value="\"/>
  <escapeWindowsPaths default-value="true"/>
  <fileNameFiltering default-value="false"/>
  <includeEmptyDirs default-value="false"/>
  <outputDirectory default-value="${project.build.outputDirectory}"/>
  <overwrite default-value="false"/>
  <project default-value="${project}"/>
  <resources default-value="${project.resources}"/>
  <session default-value="${session}"/>
  <skip default-value="false">${maven.resources.skip}</skip>
  <supportMultiLineFiltering default-value="false"/>
  <useBuildFilters default-value="true"/>
  <useDefaultDelimiters default-value="true">false</useDefaultDelimiters>
</configuration>
[DEBUG] -----------------------------------------------------------------------
[DEBUG] Goal:          org.apache.maven.plugins:maven-compiler-plugin:3.7.0:compile (default-compile)
[DEBUG] Style:         Regular
[DEBUG] Configuration: <?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <annotationProcessorPaths>
    <path>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>1.18.30</version>
    </path>
  </annotationProcessorPaths>
  <basedir default-value="${basedir}"/>
  <buildDirectory default-value="${project.build.directory}"/>
  <compilePath default-value="${project.compileClasspathElements}"/>
  <compileSourceRoots default-value="${project.compileSourceRoots}"/>
  <compilerId default-value="javac">${maven.compiler.compilerId}</compilerId>
  <compilerReuseStrategy default-value="${reuseCreated}">${maven.compiler.compilerReuseStrategy}</compilerReuseStrategy>
  <compilerVersion>${maven.compiler.compilerVersion}</compilerVersion>
  <debug default-value="true">${maven.compiler.debug}</debug>
  <debuglevel>${maven.compiler.debuglevel}</debuglevel>
  <encoding default-value="${project.build.sourceEncoding}">${encoding}</encoding>
  <executable>${maven.compiler.executable}</executable>
  <failOnError default-value="true">${maven.compiler.failOnError}</failOnError>
  <failOnWarning default-value="false">${maven.compiler.failOnWarning}</failOnWarning>
  <forceJavacCompilerUse default-value="false">${maven.compiler.forceJavacCompilerUse}</forceJavacCompilerUse>
  <fork default-value="false">${maven.compiler.fork}</fork>
  <generatedSourcesDirectory default-value="${project.build.directory}/generated-sources/annotations"/>
  <maxmem>${maven.compiler.maxmem}</maxmem>
  <meminitial>${maven.compiler.meminitial}</meminitial>
  <mojoExecution default-value="${mojoExecution}"/>
  <optimize default-value="false">${maven.compiler.optimize}</optimize>
  <outputDirectory default-value="${project.build.outputDirectory}"/>
  <parameters default-value="false">true</parameters>
  <project default-value="${project}"/>
  <projectArtifact default-value="${project.artifact}"/>
  <release>${maven.compiler.release}</release>
  <session default-value="${session}"/>
  <showDeprecation default-value="false">${maven.compiler.showDeprecation}</showDeprecation>
  <showWarnings default-value="false">${maven.compiler.showWarnings}</showWarnings>
  <skipMain>${maven.main.skip}</skipMain>
  <skipMultiThreadWarning default-value="false">${maven.compiler.skipMultiThreadWarning}</skipMultiThreadWarning>
  <source default-value="1.5">1.8</source>
  <staleMillis default-value="0">${lastModGranularityMs}</staleMillis>
  <target default-value="1.5">1.8</target>
  <useIncrementalCompilation default-value="true">${maven.compiler.useIncrementalCompilation}</useIncrementalCompilation>
  <verbose default-value="false">${maven.compiler.verbose}</verbose>
</configuration>
[DEBUG] -----------------------------------------------------------------------
[DEBUG] Goal:          org.apache.maven.plugins:maven-resources-plugin:3.0.1:testResources (default-testResources)
[DEBUG] Style:         Regular
[DEBUG] Configuration: <?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <addDefaultExcludes default-value="true"/>
  <buildFilters default-value="${project.build.filters}"/>
  <delimiters>
    <delimiter>@</delimiter>
  </delimiters>
  <encoding default-value="${project.build.sourceEncoding}"/>
  <escapeString default-value="\"/>
  <escapeWindowsPaths default-value="true"/>
  <fileNameFiltering default-value="false"/>
  <includeEmptyDirs default-value="false"/>
  <outputDirectory default-value="${project.build.testOutputDirectory}"/>
  <overwrite default-value="false"/>
  <project default-value="${project}"/>
  <resources default-value="${project.testResources}"/>
  <session default-value="${session}"/>
  <skip default-value="false">${maven.test.skip}</skip>
  <supportMultiLineFiltering default-value="false"/>
  <useBuildFilters default-value="true"/>
  <useDefaultDelimiters default-value="true">false</useDefaultDelimiters>
</configuration>
[DEBUG] -----------------------------------------------------------------------
[DEBUG] Goal:          org.apache.maven.plugins:maven-compiler-plugin:3.7.0:testCompile (default-testCompile)
[DEBUG] Style:         Regular
[DEBUG] Configuration: <?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <annotationProcessorPaths>
    <path>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>1.18.30</version>
    </path>
  </annotationProcessorPaths>
  <basedir default-value="${basedir}"/>
  <buildDirectory default-value="${project.build.directory}"/>
  <compilePath default-value="${project.compileClasspathElements}"/>
  <compileSourceRoots default-value="${project.testCompileSourceRoots}"/>
  <compilerId default-value="javac">${maven.compiler.compilerId}</compilerId>
  <compilerReuseStrategy default-value="${reuseCreated}">${maven.compiler.compilerReuseStrategy}</compilerReuseStrategy>
  <compilerVersion>${maven.compiler.compilerVersion}</compilerVersion>
  <debug default-value="true">${maven.compiler.debug}</debug>
  <debuglevel>${maven.compiler.debuglevel}</debuglevel>
  <encoding default-value="${project.build.sourceEncoding}">${encoding}</encoding>
  <executable>${maven.compiler.executable}</executable>
  <failOnError default-value="true">${maven.compiler.failOnError}</failOnError>
  <failOnWarning default-value="false">${maven.compiler.failOnWarning}</failOnWarning>
  <forceJavacCompilerUse default-value="false">${maven.compiler.forceJavacCompilerUse}</forceJavacCompilerUse>
  <fork default-value="false">${maven.compiler.fork}</fork>
  <generatedTestSourcesDirectory default-value="${project.build.directory}/generated-test-sources/test-annotations"/>
  <maxmem>${maven.compiler.maxmem}</maxmem>
  <meminitial>${maven.compiler.meminitial}</meminitial>
  <mojoExecution default-value="${mojoExecution}"/>
  <optimize default-value="false">${maven.compiler.optimize}</optimize>
  <outputDirectory default-value="${project.build.testOutputDirectory}"/>
  <parameters default-value="false">true</parameters>
  <project default-value="${project}"/>
  <release>${maven.compiler.release}</release>
  <session default-value="${session}"/>
  <showDeprecation default-value="false">${maven.compiler.showDeprecation}</showDeprecation>
  <showWarnings default-value="false">${maven.compiler.showWarnings}</showWarnings>
  <skip>${maven.test.skip}</skip>
  <skipMultiThreadWarning default-value="false">${maven.compiler.skipMultiThreadWarning}</skipMultiThreadWarning>
  <source default-value="1.5">1.8</source>
  <staleMillis default-value="0">${lastModGranularityMs}</staleMillis>
  <target default-value="1.5">1.8</target>
  <testPath default-value="${project.testClasspathElements}"/>
  <testRelease>${maven.compiler.testRelease}</testRelease>
  <testSource>${maven.compiler.testSource}</testSource>
  <testTarget>${maven.compiler.testTarget}</testTarget>
  <useIncrementalCompilation default-value="true">${maven.compiler.useIncrementalCompilation}</useIncrementalCompilation>
  <verbose default-value="false">${maven.compiler.verbose}</verbose>
</configuration>
[DEBUG] --- exit fork of com.jsh:jshERP-boot:3.5-SNAPSHOT for org.springframework.boot:spring-boot-maven-plugin:2.0.3.RELEASE:run (default-cli) ---
[DEBUG] -----------------------------------------------------------------------
[DEBUG] Goal:          org.springframework.boot:spring-boot-maven-plugin:2.0.3.RELEASE:run (default-cli)
[DEBUG] Style:         Regular
[DEBUG] Configuration: <?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <addResources default-value="false">${spring-boot.run.addResources}</addResources>
  <agent>${spring-boot.run.agent}</agent>
  <arguments>${spring-boot.run.arguments}</arguments>
  <classesDirectory default-value="${project.build.outputDirectory}"/>
  <excludeArtifactIds default-value="">${spring-boot.excludeArtifactIds}</excludeArtifactIds>
  <excludeGroupIds default-value="">${spring-boot.excludeGroupIds}</excludeGroupIds>
  <excludes>${spring-boot.excludes}</excludes>
  <folders>${spring-boot.run.folders}</folders>
  <fork>${spring-boot.run.fork}</fork>
  <includes>${spring-boot.includes}</includes>
  <jvmArguments>${spring-boot.run.jvmArguments}</jvmArguments>
  <mainClass>${start-class}</mainClass>
  <noverify>${spring-boot.run.noverify}</noverify>
  <profiles>${spring-boot.run.profiles}</profiles>
  <project default-value="${project}"/>
  <skip default-value="false">${spring-boot.run.skip}</skip>
  <useTestClasspath default-value="false">${spring-boot.run.useTestClasspath}</useTestClasspath>
  <workingDirectory>${spring-boot.run.workingDirectory}</workingDirectory>
</configuration>
[DEBUG] =======================================================================
[INFO] 
[INFO] >>> spring-boot:2.0.3.RELEASE:run (default-cli) > test-compile @ jshERP-boot >>>
[DEBUG] Using mirror maven-default-http-blocker (http://0.0.0.0/) for ow2-snapshot (http://repository.ow2.org/nexus/content/repositories/snapshots).
[DEBUG] Using mirror maven-default-http-blocker (http://0.0.0.0/) for vaadin-snapshots (http://oss.sonatype.org/content/repositories/vaadin-snapshots/).
[DEBUG] Using mirror maven-default-http-blocker (http://0.0.0.0/) for vaadin-releases (http://oss.sonatype.org/content/repositories/vaadin-releases/).
[DEBUG] Using mirror maven-default-http-blocker (http://0.0.0.0/) for apache.snapshots (http://repository.apache.org/snapshots).
[DEBUG] Using mirror maven-default-http-blocker (http://0.0.0.0/) for spring-ext (http://repo.spring.io/ext-release-local/).
[DEBUG] Using mirror maven-default-http-blocker (http://0.0.0.0/) for spring-milestones (http://repo.spring.io/milestone).
[DEBUG] Using mirror maven-default-http-blocker (http://0.0.0.0/) for spring-snapshots (http://repo.spring.io/snapshot).
[WARNING] 1 problem was encountered while building the effective model for org.javassist:javassist:jar:3.21.0-GA during dependency collection step for project. Path to offending node from root:
 -> com.jsh:jshERP-boot:jar:3.5-SNAPSHOT
 -> io.springfox:springfox-swagger2:jar:2.7.0 (compile)
 -> io.springfox:springfox-swagger-common:jar:2.7.0 (compile)
 -> io.springfox:springfox-spring-web:jar:2.7.0 (compile)
 -> org.reflections:reflections:jar:0.9.11 (compile)
 => org.javassist:javassist:jar:3.21.0-GA (compile)
Problem
* 'dependencies.dependency.systemPath' for com.sun:tools:jar refers to a non-existing file /opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/../lib/tools.jar. Please verify that you run Maven using a JDK and not just a JRE. @ org.javassist:javassist:3.21.0-GA
[DEBUG] Using mirror maven-default-http-blocker (http://0.0.0.0/) for apache.snapshots (http://people.apache.org/repo/m2-snapshot-repository).
[DEBUG] Dependency collection stats {ConflictMarker.analyzeTime=1324917, ConflictMarker.markTime=508083, ConflictMarker.nodeCount=341, ConflictIdSorter.graphTime=708083, ConflictIdSorter.topsortTime=420209, ConflictIdSorter.conflictIdCount=123, ConflictIdSorter.conflictIdCycleCount=0, ConflictResolver.totalTime=8277334, ConflictResolver.conflictItemCount=233, DfDependencyCollector.collectTime=568024542, DfDependencyCollector.transformTime=12806542}
[WARNING] The artifact org.apache.commons:commons-io:jar:1.3.2 has been relocated to commons-io:commons-io:jar:1.3.2: https://issues.sonatype.org/browse/MVNCENTRAL-244
[DEBUG] com.jsh:jshERP-boot:jar:3.5-SNAPSHOT
[DEBUG]    com.gitee.starblues:springboot-plugin-framework:jar:2.2.1-RELEASE:compile
[DEBUG]       org.pf4j:pf4j:jar:3.1.0:compile
[DEBUG]          com.github.zafarkhaja:java-semver:jar:0.9.0:compile
[DEBUG]       com.fasterxml.jackson.core:jackson-databind:jar:2.9.4:compile (version managed from 2.9.9)
[DEBUG]          com.fasterxml.jackson.core:jackson-annotations:jar:2.9.0:compile (version managed from 2.9.0)
[DEBUG]          com.fasterxml.jackson.core:jackson-core:jar:2.9.4:compile (version managed from 2.9.4)
[DEBUG]       com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.9.4:compile (version managed from 2.9.9)
[DEBUG]          org.yaml:snakeyaml:jar:1.19:compile (version managed from 1.18)
[DEBUG]    com.gitee.starblues:springboot-plugin-framework-extension-mybatis:jar:2.2.1-RELEASE:compile
[DEBUG]    org.springframework.boot:spring-boot-starter-web:jar:2.0.0.RELEASE:compile
[DEBUG]       org.springframework.boot:spring-boot-starter:jar:2.0.0.RELEASE:compile (version managed from 2.0.0.RELEASE)
[DEBUG]          org.springframework.boot:spring-boot:jar:2.0.0.RELEASE:compile (version managed from 2.0.0.RELEASE)
[DEBUG]          org.springframework.boot:spring-boot-starter-logging:jar:2.0.0.RELEASE:compile (version managed from 2.0.0.RELEASE)
[DEBUG]             ch.qos.logback:logback-classic:jar:1.2.3:compile (version managed from 1.2.3)
[DEBUG]                ch.qos.logback:logback-core:jar:1.2.3:compile (version managed from 1.2.3)
[DEBUG]          javax.annotation:javax.annotation-api:jar:1.3.2:compile (version managed from 1.3.2)
[DEBUG]       org.springframework.boot:spring-boot-starter-json:jar:2.0.0.RELEASE:compile (version managed from 2.0.0.RELEASE)
[DEBUG]          com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.9.4:compile (version managed from 2.9.4)
[DEBUG]          com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.9.4:compile (version managed from 2.9.4)
[DEBUG]          com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.9.4:compile (version managed from 2.9.4)
[DEBUG]       org.springframework.boot:spring-boot-starter-tomcat:jar:2.0.0.RELEASE:compile (version managed from 2.0.0.RELEASE)
[DEBUG]          org.apache.tomcat.embed:tomcat-embed-core:jar:8.5.28:compile (version managed from 8.5.28)
[DEBUG]          org.apache.tomcat.embed:tomcat-embed-el:jar:8.5.28:compile (version managed from 8.5.28)
[DEBUG]          org.apache.tomcat.embed:tomcat-embed-websocket:jar:8.5.28:compile (version managed from 8.5.28)
[DEBUG]       org.hibernate.validator:hibernate-validator:jar:6.0.7.Final:compile (version managed from 6.0.7.Final)
[DEBUG]          javax.validation:validation-api:jar:2.0.1.Final:compile (version managed from 2.0.1.Final)
[DEBUG]          org.jboss.logging:jboss-logging:jar:3.3.2.Final:compile (version managed from 3.3.0.Final)
[DEBUG]       org.springframework:spring-web:jar:5.0.4.RELEASE:compile (version managed from 5.0.4.RELEASE)
[DEBUG]          org.springframework:spring-beans:jar:5.0.4.RELEASE:compile (version managed from 5.0.4.RELEASE)
[DEBUG]       org.springframework:spring-webmvc:jar:5.0.4.RELEASE:compile (version managed from 5.0.4.RELEASE)
[DEBUG]          org.springframework:spring-aop:jar:5.0.4.RELEASE:compile (version managed from 5.0.4.RELEASE)
[DEBUG]          org.springframework:spring-context:jar:5.0.4.RELEASE:compile (version managed from 5.0.4.RELEASE)
[DEBUG]          org.springframework:spring-expression:jar:5.0.4.RELEASE:compile (version managed from 5.0.4.RELEASE)
[DEBUG]    org.springframework.boot:spring-boot-starter-test:jar:2.0.0.RELEASE:test
[DEBUG]       org.springframework.boot:spring-boot-test:jar:2.0.0.RELEASE:test (version managed from 2.0.0.RELEASE)
[DEBUG]       org.springframework.boot:spring-boot-test-autoconfigure:jar:2.0.0.RELEASE:test (version managed from 2.0.0.RELEASE)
[DEBUG]       com.jayway.jsonpath:json-path:jar:2.4.0:test (version managed from 2.4.0)
[DEBUG]          net.minidev:json-smart:jar:2.3:test
[DEBUG]             net.minidev:accessors-smart:jar:1.2:test
[DEBUG]                org.ow2.asm:asm:jar:5.0.4:test
[DEBUG]       junit:junit:jar:4.12:test (version managed from 4.12)
[DEBUG]       org.assertj:assertj-core:jar:3.9.1:test (version managed from 3.9.1)
[DEBUG]       org.mockito:mockito-core:jar:2.15.0:test (version managed from 2.15.0)
[DEBUG]          net.bytebuddy:byte-buddy:jar:1.7.10:compile (version managed from 1.7.9)
[DEBUG]          net.bytebuddy:byte-buddy-agent:jar:1.7.10:test (version managed from 1.7.9)
[DEBUG]          org.objenesis:objenesis:jar:2.6:test
[DEBUG]       org.hamcrest:hamcrest-core:jar:1.3:test (version managed from 1.3)
[DEBUG]       org.hamcrest:hamcrest-library:jar:1.3:test (version managed from 1.3)
[DEBUG]       org.skyscreamer:jsonassert:jar:1.5.0:test (version managed from 1.5.0)
[DEBUG]          com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:test
[DEBUG]       org.springframework:spring-core:jar:5.0.4.RELEASE:compile (version managed from 5.0.4.RELEASE)
[DEBUG]          org.springframework:spring-jcl:jar:5.0.4.RELEASE:compile (version managed from 5.0.4.RELEASE)
[DEBUG]       org.springframework:spring-test:jar:5.0.4.RELEASE:test (version managed from 5.0.4.RELEASE)
[DEBUG]       org.xmlunit:xmlunit-core:jar:2.5.1:test (version managed from 2.5.1)
[DEBUG]    com.alibaba:fastjson:jar:1.2.83:compile
[DEBUG]    mysql:mysql-connector-java:jar:8.0.28:compile
[DEBUG]       com.google.protobuf:protobuf-java:jar:3.11.4:compile
[DEBUG]    org.apache.httpcomponents:httpclient:jar:4.5.2:compile (exclusions managed from [commons-logging:commons-logging:*:*])
[DEBUG]       org.apache.httpcomponents:httpcore:jar:4.4.9:compile (version managed from 4.4.4)
[DEBUG]       commons-codec:commons-codec:jar:1.11:compile (version managed from 1.9)
[DEBUG]    net.sourceforge.jexcelapi:jxl:jar:2.6.12:compile
[DEBUG]       log4j:log4j:jar:1.2.14:compile
[DEBUG]    org.projectlombok:lombok:jar:1.18.30:compile
[DEBUG]    org.apache.logging.log4j:log4j-to-slf4j:jar:2.15.0:compile
[DEBUG]       org.slf4j:slf4j-api:jar:1.7.25:compile (version managed from 1.7.25)
[DEBUG]       org.apache.logging.log4j:log4j-api:jar:2.10.0:compile (version managed from 2.15.0)
[DEBUG]    org.slf4j:jul-to-slf4j:jar:1.7.25:compile
[DEBUG]    com.baomidou:mybatis-plus-boot-starter:jar:3.0.7.1:compile
[DEBUG]       com.baomidou:mybatis-plus:jar:3.0.7.1:compile
[DEBUG]          com.baomidou:mybatis-plus-extension:jar:3.0.7.1:compile
[DEBUG]             com.baomidou:mybatis-plus-core:jar:3.0.7.1:compile
[DEBUG]                com.baomidou:mybatis-plus-annotation:jar:3.0.7.1:compile
[DEBUG]       org.springframework.boot:spring-boot-autoconfigure:jar:2.0.0.RELEASE:compile (version managed from 2.1.0.RELEASE)
[DEBUG]       org.springframework.boot:spring-boot-starter-****************************** (version managed from 2.1.0.RELEASE)
[DEBUG]          com.zaxxer:HikariCP:jar:2.7.8:compile (version managed from 2.7.8)
[DEBUG]          org.springframework:spring-****************************** (version managed from 5.0.4.RELEASE)
[DEBUG]    org.springframework.boot:spring-boot-starter-redis:jar:1.4.1.RELEASE:compile
[DEBUG]       org.springframework.data:spring-data-redis:jar:2.0.5.RELEASE:compile (version managed from 1.7.3.RELEASE)
[DEBUG]          org.springframework.data:spring-data-keyvalue:jar:2.0.5.RELEASE:compile (version managed from 2.0.5.RELEASE)
[DEBUG]             org.springframework.data:spring-data-commons:jar:2.0.5.RELEASE:compile (version managed from 2.0.5.RELEASE)
[DEBUG]          org.springframework:spring-tx:jar:5.0.4.RELEASE:compile (version managed from 5.0.4.RELEASE)
[DEBUG]          org.springframework:spring-oxm:jar:5.0.4.RELEASE:compile (version managed from 5.0.4.RELEASE)
[DEBUG]          org.springframework:spring-context-support:jar:5.0.4.RELEASE:compile (version managed from 5.0.4.RELEASE)
[DEBUG]       redis.clients:jedis:jar:2.9.0:compile (version managed from 2.8.1)
[DEBUG]          org.apache.commons:commons-pool2:jar:2.5.0:compile (version managed from 2.4.2)
[DEBUG]    io.springfox:springfox-swagger2:jar:2.7.0:compile
[DEBUG]       io.swagger:swagger-annotations:jar:1.5.13:compile
[DEBUG]       io.swagger:swagger-models:jar:1.5.13:compile
[DEBUG]       io.springfox:springfox-spi:jar:2.7.0:compile
[DEBUG]          io.springfox:springfox-core:jar:2.7.0:compile
[DEBUG]       io.springfox:springfox-schema:jar:2.7.0:compile
[DEBUG]       io.springfox:springfox-swagger-common:jar:2.7.0:compile
[DEBUG]       io.springfox:springfox-spring-web:jar:2.7.0:compile
[DEBUG]          org.reflections:reflections:jar:0.9.11:compile
[DEBUG]             org.javassist:javassist:jar:3.21.0-GA:compile
[DEBUG]       com.google.guava:guava:jar:18.0:compile
[DEBUG]       com.fasterxml:classmate:jar:1.3.4:compile (version managed from 1.3.3)
[DEBUG]       org.springframework.plugin:spring-plugin-core:jar:1.2.0.RELEASE:compile (version managed from 1.2.0.RELEASE)
[DEBUG]       org.springframework.plugin:spring-plugin-metadata:jar:1.2.0.RELEASE:compile (version managed from 1.2.0.RELEASE)
[DEBUG]       org.mapstruct:mapstruct:jar:1.1.0.Final:compile
[DEBUG]    com.github.xiaoymin:swagger-bootstrap-ui:jar:1.6:compile
[DEBUG]    com.aliyun.oss:aliyun-sdk-oss:jar:3.10.1:compile
[DEBUG]       org.jdom:jdom:jar:1.1:compile
[DEBUG]       org.codehaus.jettison:jettison:jar:1.1:compile
[DEBUG]          stax:stax-api:jar:1.0.1:compile
[DEBUG]       com.aliyun:aliyun-java-sdk-core:jar:3.4.0:compile
[DEBUG]       com.aliyun:aliyun-java-sdk-ram:jar:3.0.0:compile
[DEBUG]       com.aliyun:aliyun-java-sdk-sts:jar:3.0.0:compile
[DEBUG]       com.aliyun:aliyun-java-sdk-ecs:jar:4.2.0:compile
[DEBUG]       com.aliyun:aliyun-java-sdk-kms:jar:2.7.0:compile
[DEBUG]          com.google.code.gson:gson:jar:2.8.2:compile (version managed from 2.8.5)
[DEBUG]    com.itextpdf:itextpdf:jar:5.5.13.1:compile
[DEBUG]    commons-io:commons-io:jar:1.3.2:compile
[DEBUG]    com.belerweb:pinyin4j:jar:2.5.1:compile
[DEBUG]    com.github.pagehelper:pagehelper-spring-boot-starter:jar:1.2.13:compile
[DEBUG]       org.mybatis.spring.boot:mybatis-spring-boot-starter:jar:2.1.1:compile
[DEBUG]          org.mybatis.spring.boot:mybatis-spring-boot-autoconfigure:jar:2.1.1:compile
[DEBUG]          org.mybatis:mybatis:jar:3.5.3:compile
[DEBUG]          org.mybatis:mybatis-spring:jar:2.0.3:compile
[DEBUG]       com.github.pagehelper:pagehelper-spring-boot-autoconfigure:jar:1.2.13:compile
[DEBUG]       com.github.pagehelper:pagehelper:jar:5.1.11:compile
[DEBUG]          com.github.jsqlparser:jsqlparser:jar:2.0:compile
[INFO] 
[INFO] --- spring-boot:2.0.3.RELEASE:build-info (build-info) @ jshERP-boot ---
[DEBUG] Using mirror maven-default-http-blocker (http://0.0.0.0/) for snapshots (http://snapshots.maven.codehaus.org/maven2).
[DEBUG] Using mirror maven-default-http-blocker (http://0.0.0.0/) for central (http://repo1.maven.org/maven2).
[DEBUG] Using mirror maven-default-http-blocker (http://0.0.0.0/) for codehaus.snapshots (http://snapshots.repository.codehaus.org).
[DEBUG] Using mirror maven-default-http-blocker (http://0.0.0.0/) for repository.jboss.org (http://repository.jboss.org/maven2).
[DEBUG] Using mirror maven-default-http-blocker (http://0.0.0.0/) for snapshots.jboss.org (http://snapshots.jboss.org/maven2).
[DEBUG] Using mirror maven-default-http-blocker (http://0.0.0.0/) for oss.sonatype.org/jboss-snapshots (http://oss.sonatype.org/content/repositories/jboss-snapshots).
[DEBUG] Using mirror maven-default-http-blocker (http://0.0.0.0/) for apache-snapshots (http://people.apache.org/repo/m2-snapshot-repository).
[DEBUG] Dependency collection stats {ConflictMarker.analyzeTime=636250, ConflictMarker.markTime=284583, ConflictMarker.nodeCount=325, ConflictIdSorter.graphTime=248083, ConflictIdSorter.topsortTime=99709, ConflictIdSorter.conflictIdCount=83, ConflictIdSorter.conflictIdCycleCount=0, ConflictResolver.totalTime=4053958, ConflictResolver.conflictItemCount=163, DfDependencyCollector.collectTime=408509375, DfDependencyCollector.transformTime=5372541}
[DEBUG] org.springframework.boot:spring-boot-maven-plugin:jar:2.0.3.RELEASE
[DEBUG]    org.springframework.boot:spring-boot-loader-tools:jar:2.0.3.RELEASE:compile
[DEBUG]       org.springframework:spring-core:jar:5.0.7.RELEASE:compile (version managed from default)
[DEBUG]          org.springframework:spring-jcl:jar:5.0.7.RELEASE:compile (version managed from default)
[DEBUG]       org.apache.commons:commons-compress:jar:1.14:compile
[DEBUG]    org.apache.maven:maven-archiver:jar:2.6:compile
[DEBUG]       org.apache.maven.shared:maven-shared-utils:jar:0.7:compile
[DEBUG]          com.google.code.findbugs:jsr305:jar:2.0.1:compile
[DEBUG]       org.codehaus.plexus:plexus-interpolation:jar:1.21:compile
[DEBUG]    org.apache.maven:maven-artifact:jar:3.1.1:compile
[DEBUG]    org.apache.maven:maven-core:jar:3.1.1:compile
[DEBUG]       org.apache.maven:maven-settings-builder:jar:3.1.1:compile
[DEBUG]       org.apache.maven:maven-repository-metadata:jar:3.1.1:compile
[DEBUG]       org.apache.maven:maven-model-builder:jar:3.1.1:compile
[DEBUG]       org.apache.maven:maven-aether-provider:jar:3.1.1:compile
[DEBUG]          org.eclipse.aether:aether-spi:jar:0.9.0.M2:compile
[DEBUG]       org.eclipse.aether:aether-impl:jar:0.9.0.M2:compile
[DEBUG]       org.eclipse.aether:aether-api:jar:0.9.0.M2:compile
[DEBUG]       org.eclipse.aether:aether-util:jar:0.9.0.M2:compile
[DEBUG]       org.eclipse.sisu:org.eclipse.sisu.plexus:jar:0.0.0.M5:compile
[DEBUG]          javax.enterprise:cdi-api:jar:1.0:compile
[DEBUG]             javax.annotation:jsr250-api:jar:1.0:compile
[DEBUG]             javax.inject:javax.inject:jar:1:compile
[DEBUG]          org.sonatype.sisu:sisu-guice:jar:no_aop:3.1.0:compile
[DEBUG]             aopalliance:aopalliance:jar:1.0:compile
[DEBUG]          org.eclipse.sisu:org.eclipse.sisu.inject:jar:0.0.0.M5:compile
[DEBUG]       org.codehaus.plexus:plexus-classworlds:jar:2.5.1:compile
[DEBUG]       org.codehaus.plexus:plexus-component-annotations:jar:1.5.5:compile
[DEBUG]       org.sonatype.plexus:plexus-sec-dispatcher:jar:1.3:compile
[DEBUG]          org.sonatype.plexus:plexus-cipher:jar:1.4:compile
[DEBUG]    org.apache.maven:maven-model:jar:3.1.1:compile
[DEBUG]    org.apache.maven:maven-plugin-api:jar:3.1.1:compile
[DEBUG]    org.apache.maven:maven-settings:jar:3.1.1:compile
[DEBUG]    org.apache.maven.shared:maven-common-artifact-filters:jar:1.4:compile
[DEBUG]       org.apache.maven:maven-project:jar:2.0.8:compile
[DEBUG]          org.apache.maven:maven-profile:jar:2.0.8:compile
[DEBUG]          org.apache.maven:maven-artifact-manager:jar:2.0.8:compile
[DEBUG]          org.apache.maven:maven-plugin-registry:jar:2.0.8:compile
[DEBUG]       org.codehaus.plexus:plexus-container-default:jar:1.5.5:compile
[DEBUG]          org.apache.xbean:xbean-reflect:jar:3.4:compile
[DEBUG]             log4j:log4j:jar:1.2.12:compile
[DEBUG]             commons-logging:commons-logging-api:jar:1.1:compile
[DEBUG]          com.google.collections:google-collections:jar:1.0:compile
[DEBUG]          junit:junit:jar:4.12:compile (version managed from default)
[DEBUG]             org.hamcrest:hamcrest-core:jar:1.3:compile (version managed from default)
[DEBUG]    org.codehaus.plexus:plexus-archiver:jar:2.8.1:compile
[DEBUG]       org.codehaus.plexus:plexus-io:jar:2.3.2:compile
[DEBUG]    org.codehaus.plexus:plexus-utils:jar:3.0.24:compile
[DEBUG]    org.sonatype.plexus:plexus-build-api:jar:0.0.7:compile
[DEBUG]    org.apache.maven.plugins:maven-shade-plugin:jar:2.2:compile (optional)
[DEBUG]       org.apache.maven:maven-compat:jar:3.0:compile (optional)
[DEBUG]          org.sonatype.sisu:sisu-inject-plexus:jar:1.4.2:compile (optional)
[DEBUG]             org.sonatype.sisu:sisu-inject-bean:jar:1.4.2:compile (optional)
[DEBUG]                org.sonatype.sisu:sisu-guice:jar:noaop:2.1.7:compile (optional)
[DEBUG]          org.apache.maven.wagon:wagon-provider-api:jar:1.0-beta-6:compile (optional)
[DEBUG]       asm:asm:jar:3.3.1:compile (optional)
[DEBUG]       asm:asm-commons:jar:3.3.1:compile (optional)
[DEBUG]          asm:asm-tree:jar:3.3.1:compile (optional)
[DEBUG]       org.jdom:jdom:jar:1.1:compile (optional)
[DEBUG]       org.apache.maven.shared:maven-dependency-tree:jar:2.1:compile (optional)
[DEBUG]       org.vafer:jdependency:jar:0.7:compile (optional)
[DEBUG]          commons-io:commons-io:jar:1.3.2:compile (optional)
[DEBUG]          asm:asm-analysis:jar:3.2:compile (optional)
[DEBUG]          asm:asm-util:jar:3.2:compile (optional)
[DEBUG]       com.google.guava:guava:jar:11.0.2:compile
[DEBUG] Created new class realm plugin>org.springframework.boot:spring-boot-maven-plugin:2.0.3.RELEASE
[DEBUG] Importing foreign packages into class realm plugin>org.springframework.boot:spring-boot-maven-plugin:2.0.3.RELEASE
[DEBUG]   Imported:  < maven.api
[DEBUG] Populating class realm plugin>org.springframework.boot:spring-boot-maven-plugin:2.0.3.RELEASE
[DEBUG]   Included: org.springframework.boot:spring-boot-maven-plugin:jar:2.0.3.RELEASE
[DEBUG]   Included: org.springframework.boot:spring-boot-loader-tools:jar:2.0.3.RELEASE
[DEBUG]   Included: org.springframework:spring-core:jar:5.0.7.RELEASE
[DEBUG]   Included: org.springframework:spring-jcl:jar:5.0.7.RELEASE
[DEBUG]   Included: org.apache.commons:commons-compress:jar:1.14
[DEBUG]   Included: org.apache.maven:maven-archiver:jar:2.6
[DEBUG]   Included: org.apache.maven.shared:maven-shared-utils:jar:0.7
[DEBUG]   Included: com.google.code.findbugs:jsr305:jar:2.0.1
[DEBUG]   Included: org.codehaus.plexus:plexus-interpolation:jar:1.21
[DEBUG]   Included: javax.enterprise:cdi-api:jar:1.0
[DEBUG]   Included: javax.annotation:jsr250-api:jar:1.0
[DEBUG]   Included: org.sonatype.sisu:sisu-guice:jar:no_aop:3.1.0
[DEBUG]   Included: aopalliance:aopalliance:jar:1.0
[DEBUG]   Included: org.eclipse.sisu:org.eclipse.sisu.inject:jar:0.0.0.M5
[DEBUG]   Included: org.codehaus.plexus:plexus-component-annotations:jar:1.5.5
[DEBUG]   Included: org.sonatype.plexus:plexus-sec-dispatcher:jar:1.3
[DEBUG]   Included: org.sonatype.plexus:plexus-cipher:jar:1.4
[DEBUG]   Included: org.apache.maven.shared:maven-common-artifact-filters:jar:1.4
[DEBUG]   Included: org.apache.xbean:xbean-reflect:jar:3.4
[DEBUG]   Included: log4j:log4j:jar:1.2.12
[DEBUG]   Included: commons-logging:commons-logging-api:jar:1.1
[DEBUG]   Included: com.google.collections:google-collections:jar:1.0
[DEBUG]   Included: junit:junit:jar:4.12
[DEBUG]   Included: org.hamcrest:hamcrest-core:jar:1.3
[DEBUG]   Included: org.codehaus.plexus:plexus-archiver:jar:2.8.1
[DEBUG]   Included: org.codehaus.plexus:plexus-io:jar:2.3.2
[DEBUG]   Included: org.codehaus.plexus:plexus-utils:jar:3.0.24
[DEBUG]   Included: org.sonatype.plexus:plexus-build-api:jar:0.0.7
[DEBUG]   Included: org.apache.maven.plugins:maven-shade-plugin:jar:2.2
[DEBUG]   Included: org.sonatype.sisu:sisu-inject-bean:jar:1.4.2
[DEBUG]   Included: org.sonatype.sisu:sisu-guice:jar:noaop:2.1.7
[DEBUG]   Included: asm:asm:jar:3.3.1
[DEBUG]   Included: asm:asm-commons:jar:3.3.1
[DEBUG]   Included: asm:asm-tree:jar:3.3.1
[DEBUG]   Included: org.jdom:jdom:jar:1.1
[DEBUG]   Included: org.apache.maven.shared:maven-dependency-tree:jar:2.1
[DEBUG]   Included: org.vafer:jdependency:jar:0.7
[DEBUG]   Included: commons-io:commons-io:jar:1.3.2
[DEBUG]   Included: asm:asm-analysis:jar:3.2
[DEBUG]   Included: asm:asm-util:jar:3.2
[DEBUG]   Included: com.google.guava:guava:jar:11.0.2
[DEBUG] Loading mojo org.springframework.boot:spring-boot-maven-plugin:2.0.3.RELEASE:build-info from plugin realm ClassRealm[plugin>org.springframework.boot:spring-boot-maven-plugin:2.0.3.RELEASE, parent: jdk.internal.loader.ClassLoaders$AppClassLoader@18ff02e4]
[DEBUG] Configuring mojo execution 'org.springframework.boot:spring-boot-maven-plugin:2.0.3.RELEASE:build-info:build-info' with basic configurator -->
[DEBUG]   (f) outputFile = /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/META-INF/build-info.properties
[DEBUG]   (f) project = MavenProject: com.jsh:jshERP-boot:3.5-SNAPSHOT @ /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/pom.xml
[DEBUG] -- end configuration --
[INFO] 
[INFO] --- resources:3.0.1:resources (default-resources) @ jshERP-boot ---
[DEBUG] Dependency collection stats {ConflictMarker.analyzeTime=156166, ConflictMarker.markTime=25750, ConflictMarker.nodeCount=69, ConflictIdSorter.graphTime=87917, ConflictIdSorter.topsortTime=30916, ConflictIdSorter.conflictIdCount=28, ConflictIdSorter.conflictIdCycleCount=0, ConflictResolver.totalTime=321833, ConflictResolver.conflictItemCount=68, DfDependencyCollector.collectTime=26088792, DfDependencyCollector.transformTime=642917}
[DEBUG] org.apache.maven.plugins:maven-resources-plugin:jar:3.0.1
[DEBUG]    org.apache.maven:maven-plugin-api:jar:3.0:compile
[DEBUG]       org.sonatype.sisu:sisu-inject-plexus:jar:1.4.2:compile
[DEBUG]          org.sonatype.sisu:sisu-inject-bean:jar:1.4.2:compile
[DEBUG]             org.sonatype.sisu:sisu-guice:jar:noaop:2.1.7:compile
[DEBUG]    org.apache.maven:maven-core:jar:3.0:compile
[DEBUG]       org.apache.maven:maven-settings-builder:jar:3.0:compile
[DEBUG]       org.apache.maven:maven-repository-metadata:jar:3.0:compile
[DEBUG]       org.apache.maven:maven-model-builder:jar:3.0:compile
[DEBUG]       org.apache.maven:maven-aether-provider:jar:3.0:runtime
[DEBUG]       org.sonatype.aether:aether-impl:jar:1.7:compile
[DEBUG]          org.sonatype.aether:aether-spi:jar:1.7:compile
[DEBUG]       org.sonatype.aether:aether-api:jar:1.7:compile
[DEBUG]       org.sonatype.aether:aether-util:jar:1.7:compile
[DEBUG]       org.codehaus.plexus:plexus-classworlds:jar:2.2.3:compile
[DEBUG]       org.codehaus.plexus:plexus-component-annotations:jar:1.6:compile (version managed from default)
[DEBUG]       org.sonatype.plexus:plexus-sec-dispatcher:jar:1.3:compile
[DEBUG]          org.sonatype.plexus:plexus-cipher:jar:1.4:compile
[DEBUG]    org.apache.maven:maven-artifact:jar:3.0:compile
[DEBUG]    org.apache.maven:maven-settings:jar:3.0:compile
[DEBUG]    org.apache.maven:maven-model:jar:3.0:compile
[DEBUG]    org.codehaus.plexus:plexus-utils:jar:3.0.24:compile
[DEBUG]    org.apache.maven.shared:maven-filtering:jar:3.1.1:compile
[DEBUG]       org.apache.maven.shared:maven-shared-utils:jar:3.0.0:compile
[DEBUG]          commons-io:commons-io:jar:2.4:compile
[DEBUG]          com.google.code.findbugs:jsr305:jar:2.0.1:compile
[DEBUG]       org.sonatype.plexus:plexus-build-api:jar:0.0.7:compile
[DEBUG]    org.codehaus.plexus:plexus-interpolation:jar:1.22:compile
[DEBUG] Created new class realm plugin>org.apache.maven.plugins:maven-resources-plugin:3.0.1
[DEBUG] Importing foreign packages into class realm plugin>org.apache.maven.plugins:maven-resources-plugin:3.0.1
[DEBUG]   Imported:  < maven.api
[DEBUG] Populating class realm plugin>org.apache.maven.plugins:maven-resources-plugin:3.0.1
[DEBUG]   Included: org.apache.maven.plugins:maven-resources-plugin:jar:3.0.1
[DEBUG]   Included: org.sonatype.sisu:sisu-inject-bean:jar:1.4.2
[DEBUG]   Included: org.sonatype.sisu:sisu-guice:jar:noaop:2.1.7
[DEBUG]   Included: org.sonatype.aether:aether-util:jar:1.7
[DEBUG]   Included: org.codehaus.plexus:plexus-component-annotations:jar:1.6
[DEBUG]   Included: org.sonatype.plexus:plexus-sec-dispatcher:jar:1.3
[DEBUG]   Included: org.sonatype.plexus:plexus-cipher:jar:1.4
[DEBUG]   Included: org.codehaus.plexus:plexus-utils:jar:3.0.24
[DEBUG]   Included: org.apache.maven.shared:maven-filtering:jar:3.1.1
[DEBUG]   Included: org.apache.maven.shared:maven-shared-utils:jar:3.0.0
[DEBUG]   Included: commons-io:commons-io:jar:2.4
[DEBUG]   Included: com.google.code.findbugs:jsr305:jar:2.0.1
[DEBUG]   Included: org.sonatype.plexus:plexus-build-api:jar:0.0.7
[DEBUG]   Included: org.codehaus.plexus:plexus-interpolation:jar:1.22
[DEBUG] Loading mojo org.apache.maven.plugins:maven-resources-plugin:3.0.1:resources from plugin realm ClassRealm[plugin>org.apache.maven.plugins:maven-resources-plugin:3.0.1, parent: jdk.internal.loader.ClassLoaders$AppClassLoader@18ff02e4]
[DEBUG] Configuring mojo execution 'org.apache.maven.plugins:maven-resources-plugin:3.0.1:resources:default-resources' with basic configurator -->
[DEBUG]   (f) addDefaultExcludes = true
[DEBUG]   (f) buildFilters = []
[DEBUG]   (s) delimiters = [@]
[DEBUG]   (f) encoding = UTF-8
[DEBUG]   (f) escapeString = \
[DEBUG]   (f) escapeWindowsPaths = true
[DEBUG]   (f) fileNameFiltering = false
[DEBUG]   (s) includeEmptyDirs = false
[DEBUG]   (s) outputDirectory = /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes
[DEBUG]   (s) overwrite = false
[DEBUG]   (f) project = MavenProject: com.jsh:jshERP-boot:3.5-SNAPSHOT @ /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/pom.xml
[DEBUG]   (s) resources = [Resource {targetPath: null, filtering: true, FileSet {directory: /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources, PatternSet [includes: {**/application*.yml, **/application*.yaml, **/application*.properties}, excludes: {}]}}, Resource {targetPath: null, filtering: false, FileSet {directory: /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources, PatternSet [includes: {}, excludes: {**/application*.yml, **/application*.yaml, **/application*.properties}]}}]
[DEBUG]   (f) session = org.apache.maven.execution.MavenSession@62765aec
[DEBUG]   (f) skip = false
[DEBUG]   (f) supportMultiLineFiltering = false
[DEBUG]   (f) useBuildFilters = true
[DEBUG]   (s) useDefaultDelimiters = false
[DEBUG] -- end configuration --
[DEBUG] properties used {spring-integration.version=5.0.3.RELEASE, webjars-hal-browser.version=3325375, jna.version=4.5.1, env.GIT_ASKPASS=/Applications/Cursor.app/Contents/Resources/app/extensions/git/dist/askpass.sh, jboss-transaction-spi.version=7.6.0.Final, java.vm.specification.vendor=Oracle Corporation, exec-maven-plugin.version=1.5.0, env.DISABLE_AUTO_UPDATE=true, env.TTY=/dev/ttys144, env.https_proxy=http://127.0.0.1:7890, java.vendor.version=Homebrew, env.CLAUDE_CODE_SSE_PORT=29467, project.baseUri=file:/Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/, hibernate.version=5.2.14.Final, nekohtml.version=1.9.22, native.encoding=UTF-8, spring-cloud-connectors.version=2.0.1.RELEASE, selenium-htmlunit.version=2.29.2, env.BUNDLED_DEBUGPY_PATH=/Users/<USER>/.cursor/extensions/ms-python.debugpy-2025.8.0-darwin-arm64/bundled/libs/debugpy, ehcache3.version=3.5.0, commons-dbcp2.version=2.2.0, mongodb.version=3.6.3, couchbase-cache-client.version=2.1.0, java.vm.specification.version=23, user.home=/Users/<USER>/opt/homebrew/Cellar, postgresql.version=42.2.1, jmustache.version=1.14, appengine-sdk.version=1.9.62, env.HOMEBREW_PREFIX=/opt/homebrew, hamcrest.version=1.3, os.version=15.5, env.M2_HOME=/usr/local/maven-mvnd-1.0.2, jaxen.version=1.1.6, spring-batch.version=4.0.0.RELEASE, couchbase-client.version=2.5.5, jest.version=5.3.3, neo4j-ogm.version=3.1.0, flatten-maven-plugin.version=1.0.0, commons-codec.version=1.11, caffeine.version=2.6.2, dom4j.version=1.6.1, unboundid-ldapsdk.version=4.0.4, javax-jaxb.version=2.3.0, httpasyncclient.version=4.1.3, javax-jms.version=2.0.1, env.__CF_USER_TEXT_ENCODING=0x1F5:0x19:0x34, env.QTERM_SESSION_ID=c1b54fa1f8ea4ae9832160be73b53366, env.SSH_AUTH_SOCK=/private/tmp/com.apple.launchd.Q49GU9K0kj/Listeners, socksProxyPort=7890, lombok.version=1.16.20, env.TMPDIR=/var/folders/x0/zwc9fkr91wx0965gbdhvmy5c0000gn/T/, library.jansi.path=/opt/homebrew/Cellar/maven/3.9.9/libexec/lib/jansi-native, socksNonProxyHosts=***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|127.0.0.1|localhost|*.localhost|local|*.local|timestamp.apple.com|*.timestamp.apple.com|sequoia.apple.com|*.sequoia.apple.com|seed-sequoia.siri.apple.com|*.seed-sequoia.siri.apple.com, http.proxyHost=127.0.0.1, env.PAGER=head -n 10000 | cat, maven-invoker-plugin.version=3.0.0, user.country=CN, resource.delimiter=@, https.proxyHost=127.0.0.1, env.npm_config_yes=true, jetty-jsp.version=2.2.0.v201112011158, maven-source-plugin.version=3.0.1, rxjava.version=1.3.6, java.runtime.name=OpenJDK Runtime Environment, env.PYDEVD_DISABLE_FILE_VALIDATION=1, maven-deploy-plugin.version=2.8.2, env.MAVEN_CMD_LINE_ARGS= spring-boot:run -X, maven-failsafe-plugin.version=2.20.1, dropwizard-metrics.version=3.2.6, socksProxyHost=127.0.0.1, env.TERM=xterm-256color, env.VSCODE_DEBUGPY_ADAPTER_ENDPOINTS=/Users/<USER>/.cursor/extensions/ms-python.debugpy-2025.8.0-darwin-arm64/.noConfigDebugAdapterEndpoints/endpoint-6b00e4447f4c9d39.txt, spring-restdocs.version=2.0.0.RELEASE, freemarker.version=2.3.27-incubating, janino.version=3.0.8, java.version.date=2025-01-21, java.home=/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home, jolokia.version=1.5.0, sun.management.compiler=HotSpot 64-Bit Tiered Compilers, env.PATH=/Users/<USER>/.npm-global/bin:/Users/<USER>/Library/Python/3.13/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Library/Frameworks/Python.framework/Versions/3.13/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin://Applications/Topaz Gigapixel AI.app/Contents/Resources/bin://Applications/Topaz Photo AI.app/Contents/Resources/bin:/Library/Apple/usr/bin:/usr/local/go/bin:/Users/<USER>/.npm-global/bin:/Users/<USER>/Library/Python/3.13/bin:/Users/<USER>/.local/bin:/usr/local/maven-mvnd-1.0.2/bin:/Users/<USER>/.cursor/extensions/ms-python.debugpy-2025.8.0-darwin-arm64/bundled/scripts/noConfigScripts:/Users/<USER>/Library/Application Support/Cursor/User/globalStorage/github.copilot-chat/debugCommand, httpclient.version=4.5.5, maven-dependency-plugin.version=3.0.1, env.ORIGINAL_XDG_CURRENT_DESKTOP=undefined, gson.version=2.8.2, sun-mail.version=1.6.1, mssql-jdbc.version=6.2.2.jre8, snakeyaml.version=1.19, lettuce.version=5.0.2.RELEASE, statsd-client.version=3.1.0, stderr.encoding=UTF-8, kotlin.version=1.2.21, wsdl4j.version=1.6.3, tomcat.version=8.5.28, spring-ws.version=3.0.0.RELEASE, commons-lang3.version=3.7, os.name=Mac OS X, junit.version=4.12, jetty-el.version=********, maven.build.timestamp=2025-06-21T09:42:20Z, env.SHELL_PID=6076, javax-money.version=1.0.1, env.SHELL=/bin/zsh, env.Q_SET_PARENT_CHECK=1, java.vm.info=mixed mode, sharing, cassandra-driver.version=3.4.0, java.class.version=67.0, env.USER=macmini, narayana.version=5.8.0.Final, sun.jnu.encoding=UTF-8, xmlunit2.version=2.5.1, env.ENABLE_IDE_INTEGRATION=true, maven.build.version=Apache Maven 3.9.9 (8e8579a9e76f7d015ee5ec7bfcdc97d260186937), maven.home=/opt/homebrew/Cellar/maven/3.9.9/libexec, maven-shade-plugin.version=2.4.3, file.separator=/, line.separator=
, spring-kafka.version=2.1.4.RELEASE, env.TERM_PROGRAM_VERSION=1.1.4, env.XPC_FLAGS=0x0, hazelcast.version=3.9.3, jdom2.version=2.0.6, spring-data-releasetrain.version=Kay-SR5, hsqldb.version=2.4.0, env.TERM_PROGRAM=vscode, env.PWD=/Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot, embedded-mongo.version=2.0.3, spring-ldap.version=2.3.2.RELEASE, java.class.path=/opt/homebrew/Cellar/maven/3.9.9/libexec/boot/plexus-classworlds-2.8.0.jar, env.HOME=/Users/<USER>//127.0.0.1:7891, jedis.version=2.9.0, maven-clean-plugin.version=3.0.0, maven-surefire-plugin.version=2.20.1, apple.awt.application.name=Launcher, jaybird.version=3.0.3, env.XPC_SERVICE_NAME=0, ftp.nonProxyHosts=***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|127.0.0.1|localhost|*.localhost|local|*.local|timestamp.apple.com|*.timestamp.apple.com|sequoia.apple.com|*.sequoia.apple.com|seed-sequoia.siri.apple.com|*.seed-sequoia.siri.apple.com, selenium.version=3.9.1, glassfish-el.version=3.0.0, project.build.sourceEncoding=UTF-8, user.dir=/Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot, ehcache.version=2.10.4, junit-jupiter.version=5.1.0, spring-session-bom.version=Apple-SR1, flyway.version=5.0.7, java.specification.version=23, java.vendor.url=https://github.com/Homebrew/homebrew-core/issues, sun.boot.library.path=/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib, sun.java.command=org.codehaus.plexus.classworlds.launcher.Launcher spring-boot:run -X, jdk.debug=release, maven.version=3.9.9, java.specification.name=Java Platform API Specification, env.HOMEBREW_REPOSITORY=/opt/homebrew, maven-install-plugin.version=2.5.2, byte-buddy.version=1.7.10, java.runtime.version=23.0.2, env.__CFBundleIdentifier=com.todesktop.230313mzl4w4u92, micrometer.version=1.0.1, rxjava-adapter.version=1.2.1, java.io.tmpdir=/var/folders/x0/zwc9fkr91wx0965gbdhvmy5c0000gn/T/, java.version=23.0.2, build-helper-maven-plugin.version=3.0.0, mockito.version=2.15.0, java.vm.specification.name=Java Virtual Machine Specification, infinispan.version=9.1.6.Final, maven-assembly-plugin.version=3.1.0, java.library.path=/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:., java.vendor=Homebrew, thymeleaf-extras-java8time.version=3.0.1.RELEASE, undertow.version=1.4.22.Final, mariadb.version=2.2.2, thymeleaf-extras-data-attribute.version=2.0.1, env.LANG=zh_CN.UTF-8, https.proxyPort=7890, maven.compiler.source=1.8, javax-transaction.version=1.2, spring.version=5.0.4.RELEASE, reactive-streams.version=1.0.2, querydsl.version=4.1.4, thymeleaf-layout-dialect.version=2.3.0, commons-pool.version=1.6, java.vm.name=OpenJDK 64-Bit Server VM, env.CURSOR_TRACE_ID=39b5d67894004439968cca52c07056a2, maven-help-plugin.version=2.2, json-path.version=2.4.0, os.arch=aarch64, env.MallocNanoZone=0, derby.version=10.14.1.0, javax-jsonb.version=1.0, junit-platform.version=1.1.0, rxjava2.version=2.1.10, maven-site-plugin.version=3.6, http.proxyPort=7890, env.VSCODE_GIT_ASKPASS_MAIN=/Applications/Cursor.app/Contents/Resources/app/extensions/git/dist/askpass-main.js, h2.version=1.4.196, env.JAVA_HOME=/opt/homebrew/opt/openjdk/libexec/openjdk.jdk/Contents/Home, java.vm.compressedOopsMode=Zero based, sqlite-jdbc.version=********, activemq.version=5.15.3, jtds.version=1.3.1, env.LOGNAME=macmini, spring-security.version=5.0.3.RELEASE, hibernate-validator.version=6.0.7.Final, assertj.version=3.9.1, influxdb-java.version=2.9, maven.compiler.target=1.8, commons-pool2.version=2.5.0, spring-retry.version=1.2.2.RELEASE, maven-antrun-plugin.version=1.8, maven.conf=/opt/homebrew/Cellar/maven/3.9.9/libexec/conf, sun.java.launcher=SUN_STANDARD, env.VSCODE_GIT_IPC_HANDLE=/var/folders/x0/zwc9fkr91wx0965gbdhvmy5c0000gn/T/vscode-git-d25f46688f.sock, javax-json.version=1.1.2, jboss-logging.version=3.3.2.Final, maven-resources-plugin.version=3.0.1, javax-validation.version=2.0.1.Final, netty.version=4.1.22.Final, env.VSCODE_GIT_ASKPASS_EXTRA_ARGS=, jetty.version=9.4.8.v20171121, elasticsearch.version=5.6.8, rest-assured.version=3.0.7, log4j2.version=2.10.0, git-commit-id-plugin.version=2.2.3, hazelcast-hibernate5.version=1.2.3, htmlunit.version=2.29, jackson.version=2.9.4, sun.arch.data.model=64, thymeleaf.version=3.0.9.RELEASE, maven-jar-plugin.version=3.0.2, maven-compiler-plugin.version=3.7.0, java.specification.vendor=Oracle Corporation, bitronix.version=2.1.4, rabbit-amqp-client.version=5.1.2, spring-amqp.version=2.0.2.RELEASE, spring-plugin.version=1.2.0.RELEASE, maven-war-plugin.version=3.1.0, thymeleaf-extras-springsecurity4.version=3.0.2.RELEASE, user.script=Hans, env.http_proxy=http://127.0.0.1:7890, jsonassert.version=1.5.0, file.encoding=UTF-8, nio-multipart-parser.version=1.1.0, env.SHLVL=1, liquibase.version=3.5.5, httpcore.version=4.4.9, classworlds.conf=/opt/homebrew/Cellar/maven/3.9.9/libexec/bin/m2.conf, sun.io.unicode.encoding=UnicodeBig, joda-time.version=2.9.9, env.COMMAND_MODE=unix2003, sendgrid.version=4.1.2, env.LaunchInstanceID=286D84D2-64D8-404A-A48E-9BAE475E597C, simple-json.version=1.1.1, env.Q_TERM=1.12.1, http.nonProxyHosts=***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|127.0.0.1|localhost|*.localhost|local|*.local|timestamp.apple.com|*.timestamp.apple.com|sequoia.apple.com|*.sequoia.apple.com|seed-sequoia.siri.apple.com|*.seed-sequoia.siri.apple.com, mongo-driver-reactivestreams.version=1.7.1, reactor-bom.version=Bismuth-SR7, maven-enforcer-plugin.version=3.0.0-M1, jstl.version=1.2, quartz.version=2.3.0, env.no_proxy=localhost,127.0.0.1,::1,*.local,*.lan, spring-hateoas.version=0.24.0.RELEASE, stdout.encoding=UTF-8, path.separator=:, maven.multiModuleProjectDirectory=/Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot, env.MAVEN_PROJECTBASEDIR=/Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot, env.VSCODE_GIT_ASKPASS_NODE=/Applications/Cursor.app/Contents/Frameworks/Cursor Helper (Plugin).app/Contents/MacOS/Cursor Helper (Plugin), xml-maven-plugin.version=1.0.1, slf4j.version=1.7.25, solr.version=6.6.2, hikaricp.version=2.7.8, jersey.version=2.26, user.name=macmini, env.PIP_NO_INPUT=true, atomikos.version=4.0.6, aspectj.version=1.8.13, webjars-locator-core.version=0.35, project.reporting.outputEncoding=UTF-8, xml-apis.version=1.4.01, env.INFOPATH=/opt/homebrew/share/info:/opt/homebrew/share/info:, env.OLDPWD=/Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot, johnzon-jsonb.version=1.1.6, java.vm.vendor=Homebrew, maven-javadoc-plugin.version=3.0.0-M1, artemis.version=2.4.0, sun.cpu.endian=little, versions-maven-plugin.version=2.3, user.language=zh, javax-mail.version=1.6.1, javax-annotation.version=1.3.2, mysql.version=5.1.45, classmate.version=1.3.4, antlr2.version=2.7.7, env.COMPOSER_NO_INTERACTION=1, jooq.version=3.10.5, java.vendor.url.bug=https://github.com/Homebrew/homebrew-core/issues, java.vm.version=23.0.2, maven-eclipse-plugin.version=2.10}
[INFO] Using 'UTF-8' encoding to copy filtered resources.
[DEBUG] resource with targetPath null
directory /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources
excludes []
includes [**/application*.yml, **/application*.yaml, **/application*.properties]
[DEBUG] ignoreDelta true
[INFO] Copying 3 resources
[DEBUG] Copying file application-local.properties
[DEBUG] file application-local.properties has a filtered file extension
[DEBUG] filtering /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/application-local.properties to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/application-local.properties
[DEBUG] Copying file application-docker.properties
[DEBUG] file application-docker.properties has a filtered file extension
[DEBUG] filtering /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/application-docker.properties to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/application-docker.properties
[DEBUG] Copying file application.properties
[DEBUG] file application.properties has a filtered file extension
[DEBUG] filtering /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/application.properties to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/application.properties
[DEBUG] resource with targetPath null
directory /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources
excludes [**/application*.yml, **/application*.yaml, **/application*.properties]
includes []
[DEBUG] ignoreDelta true
[INFO] Copying 61 resources
[DEBUG] Copying file mapper_xml/MaterialExtendMapperEx.xml
[DEBUG] file MaterialExtendMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialExtendMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/MaterialExtendMapperEx.xml
[DEBUG] Copying file mapper_xml/FunctionMapperEx.xml
[DEBUG] file FunctionMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/FunctionMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/FunctionMapperEx.xml
[DEBUG] Copying file mapper_xml/MaterialExtendMapper.xml
[DEBUG] file MaterialExtendMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialExtendMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/MaterialExtendMapper.xml
[DEBUG] Copying file mapper_xml/DepotItemMapperEx.xml
[DEBUG] file DepotItemMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/DepotItemMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/DepotItemMapperEx.xml
[DEBUG] Copying file mapper_xml/UserBusinessMapperEx.xml
[DEBUG] file UserBusinessMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/UserBusinessMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/UserBusinessMapperEx.xml
[DEBUG] Copying file mapper_xml/DepotItemMapper.xml
[DEBUG] file DepotItemMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/DepotItemMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/DepotItemMapper.xml
[DEBUG] Copying file mapper_xml/RoleMapperEx.xml
[DEBUG] file RoleMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/RoleMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/RoleMapperEx.xml
[DEBUG] Copying file mapper_xml/SerialNumberMapper.xml
[DEBUG] file SerialNumberMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/SerialNumberMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/SerialNumberMapper.xml
[DEBUG] Copying file mapper_xml/MaterialInitialStockMapper.xml
[DEBUG] file MaterialInitialStockMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialInitialStockMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/MaterialInitialStockMapper.xml
[DEBUG] Copying file mapper_xml/PersonMapperEx.xml
[DEBUG] file PersonMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/PersonMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/PersonMapperEx.xml
[DEBUG] Copying file mapper_xml/DepotMapperEx.xml
[DEBUG] file DepotMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/DepotMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/DepotMapperEx.xml
[DEBUG] Copying file mapper_xml/PlatformConfigMapperEx.xml
[DEBUG] file PlatformConfigMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/PlatformConfigMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/PlatformConfigMapperEx.xml
[DEBUG] Copying file mapper_xml/PlatformConfigMapper.xml
[DEBUG] file PlatformConfigMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/PlatformConfigMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/PlatformConfigMapper.xml
[DEBUG] Copying file mapper_xml/MaterialPropertyMapperEx.xml
[DEBUG] file MaterialPropertyMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialPropertyMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/MaterialPropertyMapperEx.xml
[DEBUG] Copying file mapper_xml/InOutItemMapperEx.xml
[DEBUG] file InOutItemMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/InOutItemMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/InOutItemMapperEx.xml
[DEBUG] Copying file mapper_xml/UserBusinessMapper.xml
[DEBUG] file UserBusinessMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/UserBusinessMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/UserBusinessMapper.xml
[DEBUG] Copying file mapper_xml/LogMapper.xml
[DEBUG] file LogMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/LogMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/LogMapper.xml
[DEBUG] Copying file mapper_xml/MaterialInitialStockMapperEx.xml
[DEBUG] file MaterialInitialStockMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialInitialStockMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/MaterialInitialStockMapperEx.xml
[DEBUG] Copying file mapper_xml/AccountHeadMapperEx.xml
[DEBUG] file AccountHeadMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/AccountHeadMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/AccountHeadMapperEx.xml
[DEBUG] Copying file mapper_xml/OrgaUserRelMapperEx.xml
[DEBUG] file OrgaUserRelMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/OrgaUserRelMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/OrgaUserRelMapperEx.xml
[DEBUG] Copying file mapper_xml/SupplierMapper.xml
[DEBUG] file SupplierMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/SupplierMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/SupplierMapper.xml
[DEBUG] Copying file mapper_xml/OrganizationMapper.xml
[DEBUG] file OrganizationMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/OrganizationMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/OrganizationMapper.xml
[DEBUG] Copying file mapper_xml/MaterialPropertyMapper.xml
[DEBUG] file MaterialPropertyMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialPropertyMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/MaterialPropertyMapper.xml
[DEBUG] Copying file mapper_xml/UserMapperEx.xml
[DEBUG] file UserMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/UserMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/UserMapperEx.xml
[DEBUG] Copying file mapper_xml/AccountMapperEx.xml
[DEBUG] file AccountMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/AccountMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/AccountMapperEx.xml
[DEBUG] Copying file mapper_xml/SupplierMapperEx.xml
[DEBUG] file SupplierMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/SupplierMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/SupplierMapperEx.xml
[DEBUG] Copying file mapper_xml/AccountMapper.xml
[DEBUG] file AccountMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/AccountMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/AccountMapper.xml
[DEBUG] Copying file mapper_xml/SystemConfigMapperEx.xml
[DEBUG] file SystemConfigMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/SystemConfigMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/SystemConfigMapperEx.xml
[DEBUG] Copying file mapper_xml/AccountHeadMapper.xml
[DEBUG] file AccountHeadMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/AccountHeadMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/AccountHeadMapper.xml
[DEBUG] Copying file mapper_xml/MaterialMapper.xml
[DEBUG] file MaterialMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/MaterialMapper.xml
[DEBUG] Copying file mapper_xml/UserMapper.xml
[DEBUG] file UserMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/UserMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/UserMapper.xml
[DEBUG] Copying file mapper_xml/MaterialMapperEx.xml
[DEBUG] file MaterialMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/MaterialMapperEx.xml
[DEBUG] Copying file mapper_xml/MaterialCategoryMapperEx.xml
[DEBUG] file MaterialCategoryMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialCategoryMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/MaterialCategoryMapperEx.xml
[DEBUG] Copying file mapper_xml/SystemConfigMapper.xml
[DEBUG] file SystemConfigMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/SystemConfigMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/SystemConfigMapper.xml
[DEBUG] Copying file mapper_xml/MaterialAttributeMapper.xml
[DEBUG] file MaterialAttributeMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialAttributeMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/MaterialAttributeMapper.xml
[DEBUG] Copying file mapper_xml/WorkOrderMapperEx.xml
[DEBUG] file WorkOrderMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/WorkOrderMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/WorkOrderMapperEx.xml
[DEBUG] Copying file mapper_xml/LogMapperEx.xml
[DEBUG] file LogMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/LogMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/LogMapperEx.xml
[DEBUG] Copying file mapper_xml/AccountItemMapper.xml
[DEBUG] file AccountItemMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/AccountItemMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/AccountItemMapper.xml
[DEBUG] Copying file mapper_xml/MaterialCurrentStockMapper.xml
[DEBUG] file MaterialCurrentStockMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialCurrentStockMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/MaterialCurrentStockMapper.xml
[DEBUG] Copying file mapper_xml/MsgMapper.xml
[DEBUG] file MsgMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MsgMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/MsgMapper.xml
[DEBUG] Copying file mapper_xml/AccountItemMapperEx.xml
[DEBUG] file AccountItemMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/AccountItemMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/AccountItemMapperEx.xml
[DEBUG] Copying file mapper_xml/FunctionMapper.xml
[DEBUG] file FunctionMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/FunctionMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/FunctionMapper.xml
[DEBUG] Copying file mapper_xml/MsgMapperEx.xml
[DEBUG] file MsgMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MsgMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/MsgMapperEx.xml
[DEBUG] Copying file mapper_xml/DepotMapper.xml
[DEBUG] file DepotMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/DepotMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/DepotMapper.xml
[DEBUG] Copying file mapper_xml/PersonMapper.xml
[DEBUG] file PersonMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/PersonMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/PersonMapper.xml
[DEBUG] Copying file mapper_xml/UnitMapperEx.xml
[DEBUG] file UnitMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/UnitMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/UnitMapperEx.xml
[DEBUG] Copying file mapper_xml/MaterialCategoryMapper.xml
[DEBUG] file MaterialCategoryMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialCategoryMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/MaterialCategoryMapper.xml
[DEBUG] Copying file mapper_xml/OrganizationMapperEx.xml
[DEBUG] file OrganizationMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/OrganizationMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/OrganizationMapperEx.xml
[DEBUG] Copying file mapper_xml/SerialNumberMapperEx.xml
[DEBUG] file SerialNumberMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/SerialNumberMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/SerialNumberMapperEx.xml
[DEBUG] Copying file mapper_xml/OrgaUserRelMapper.xml
[DEBUG] file OrgaUserRelMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/OrgaUserRelMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/OrgaUserRelMapper.xml
[DEBUG] Copying file mapper_xml/TenantMapperEx.xml
[DEBUG] file TenantMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/TenantMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/TenantMapperEx.xml
[DEBUG] Copying file mapper_xml/UnitMapper.xml
[DEBUG] file UnitMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/UnitMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/UnitMapper.xml
[DEBUG] Copying file mapper_xml/SequenceMapperEx.xml
[DEBUG] file SequenceMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/SequenceMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/SequenceMapperEx.xml
[DEBUG] Copying file mapper_xml/TenantMapper.xml
[DEBUG] file TenantMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/TenantMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/TenantMapper.xml
[DEBUG] Copying file mapper_xml/MaterialAttributeMapperEx.xml
[DEBUG] file MaterialAttributeMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialAttributeMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/MaterialAttributeMapperEx.xml
[DEBUG] Copying file mapper_xml/InOutItemMapper.xml
[DEBUG] file InOutItemMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/InOutItemMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/InOutItemMapper.xml
[DEBUG] Copying file mapper_xml/MaterialCurrentStockMapperEx.xml
[DEBUG] file MaterialCurrentStockMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialCurrentStockMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/MaterialCurrentStockMapperEx.xml
[DEBUG] Copying file mapper_xml/DepotHeadMapper.xml
[DEBUG] file DepotHeadMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/DepotHeadMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/DepotHeadMapper.xml
[DEBUG] Copying file mapper_xml/RoleMapper.xml
[DEBUG] file RoleMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/RoleMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/RoleMapper.xml
[DEBUG] Copying file mapper_xml/DepotHeadMapperEx.xml
[DEBUG] file DepotHeadMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/DepotHeadMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/DepotHeadMapperEx.xml
[DEBUG] Copying file logback-spring.xml
[DEBUG] file logback-spring.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/logback-spring.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/logback-spring.xml
[DEBUG] no use filter components
[INFO] 
[INFO] --- compiler:3.7.0:compile (default-compile) @ jshERP-boot ---
[DEBUG] Dependency collection stats {ConflictMarker.analyzeTime=92292, ConflictMarker.markTime=72000, ConflictMarker.nodeCount=118, ConflictIdSorter.graphTime=36459, ConflictIdSorter.topsortTime=47458, ConflictIdSorter.conflictIdCount=45, ConflictIdSorter.conflictIdCycleCount=0, ConflictResolver.totalTime=552917, ConflictResolver.conflictItemCount=72, DfDependencyCollector.collectTime=43051708, DfDependencyCollector.transformTime=820625}
[DEBUG] org.apache.maven.plugins:maven-compiler-plugin:jar:3.7.0
[DEBUG]    org.apache.maven:maven-plugin-api:jar:3.0:compile
[DEBUG]       org.apache.maven:maven-model:jar:3.0:compile
[DEBUG]       org.sonatype.sisu:sisu-inject-plexus:jar:1.4.2:compile
[DEBUG]          org.sonatype.sisu:sisu-inject-bean:jar:1.4.2:compile
[DEBUG]             org.sonatype.sisu:sisu-guice:jar:noaop:2.1.7:compile
[DEBUG]    org.apache.maven:maven-artifact:jar:3.0:compile
[DEBUG]       org.codehaus.plexus:plexus-utils:jar:2.0.4:compile
[DEBUG]    org.apache.maven:maven-core:jar:3.0:compile
[DEBUG]       org.apache.maven:maven-settings:jar:3.0:compile
[DEBUG]       org.apache.maven:maven-settings-builder:jar:3.0:compile
[DEBUG]       org.apache.maven:maven-repository-metadata:jar:3.0:compile
[DEBUG]       org.apache.maven:maven-model-builder:jar:3.0:compile
[DEBUG]       org.apache.maven:maven-aether-provider:jar:3.0:runtime
[DEBUG]       org.sonatype.aether:aether-impl:jar:1.7:compile
[DEBUG]          org.sonatype.aether:aether-spi:jar:1.7:compile
[DEBUG]       org.sonatype.aether:aether-api:jar:1.7:compile
[DEBUG]       org.sonatype.aether:aether-util:jar:1.7:compile
[DEBUG]       org.codehaus.plexus:plexus-interpolation:jar:1.14:compile
[DEBUG]       org.codehaus.plexus:plexus-classworlds:jar:2.2.3:compile
[DEBUG]       org.codehaus.plexus:plexus-component-annotations:jar:1.6:compile (version managed from default)
[DEBUG]       org.sonatype.plexus:plexus-sec-dispatcher:jar:1.3:compile
[DEBUG]          org.sonatype.plexus:plexus-cipher:jar:1.4:compile
[DEBUG]    org.apache.maven.shared:maven-shared-utils:jar:3.1.0:compile
[DEBUG]       commons-io:commons-io:jar:2.5:compile
[DEBUG]    org.apache.maven.shared:maven-shared-incremental:jar:1.1:compile
[DEBUG]    org.codehaus.plexus:plexus-java:jar:0.9.2:compile
[DEBUG]       org.ow2.asm:asm:jar:6.0_BETA:compile
[DEBUG]       com.thoughtworks.qdox:qdox:jar:2.0-M7:compile
[DEBUG]    org.codehaus.plexus:plexus-compiler-api:jar:2.8.2:compile
[DEBUG]    org.codehaus.plexus:plexus-compiler-manager:jar:2.8.2:compile
[DEBUG]    org.codehaus.plexus:plexus-compiler-javac:jar:2.8.2:runtime
[DEBUG] Created new class realm plugin>org.apache.maven.plugins:maven-compiler-plugin:3.7.0
[DEBUG] Importing foreign packages into class realm plugin>org.apache.maven.plugins:maven-compiler-plugin:3.7.0
[DEBUG]   Imported:  < maven.api
[DEBUG] Populating class realm plugin>org.apache.maven.plugins:maven-compiler-plugin:3.7.0
[DEBUG]   Included: org.apache.maven.plugins:maven-compiler-plugin:jar:3.7.0
[DEBUG]   Included: org.sonatype.sisu:sisu-inject-bean:jar:1.4.2
[DEBUG]   Included: org.sonatype.sisu:sisu-guice:jar:noaop:2.1.7
[DEBUG]   Included: org.codehaus.plexus:plexus-utils:jar:2.0.4
[DEBUG]   Included: org.sonatype.aether:aether-util:jar:1.7
[DEBUG]   Included: org.codehaus.plexus:plexus-interpolation:jar:1.14
[DEBUG]   Included: org.codehaus.plexus:plexus-component-annotations:jar:1.6
[DEBUG]   Included: org.sonatype.plexus:plexus-sec-dispatcher:jar:1.3
[DEBUG]   Included: org.sonatype.plexus:plexus-cipher:jar:1.4
[DEBUG]   Included: org.apache.maven.shared:maven-shared-utils:jar:3.1.0
[DEBUG]   Included: commons-io:commons-io:jar:2.5
[DEBUG]   Included: org.apache.maven.shared:maven-shared-incremental:jar:1.1
[DEBUG]   Included: org.codehaus.plexus:plexus-java:jar:0.9.2
[DEBUG]   Included: org.ow2.asm:asm:jar:6.0_BETA
[DEBUG]   Included: com.thoughtworks.qdox:qdox:jar:2.0-M7
[DEBUG]   Included: org.codehaus.plexus:plexus-compiler-api:jar:2.8.2
[DEBUG]   Included: org.codehaus.plexus:plexus-compiler-manager:jar:2.8.2
[DEBUG]   Included: org.codehaus.plexus:plexus-compiler-javac:jar:2.8.2
[DEBUG] Loading mojo org.apache.maven.plugins:maven-compiler-plugin:3.7.0:compile from plugin realm ClassRealm[plugin>org.apache.maven.plugins:maven-compiler-plugin:3.7.0, parent: jdk.internal.loader.ClassLoaders$AppClassLoader@18ff02e4]
[DEBUG] Configuring mojo execution 'org.apache.maven.plugins:maven-compiler-plugin:3.7.0:compile:default-compile' with basic configurator -->
[DEBUG]   (s) groupId = org.projectlombok
[DEBUG]   (s) artifactId = lombok
[DEBUG]   (s) version = 1.18.30
[DEBUG]   (f) annotationProcessorPaths = [org.projectlombok:lombok:1.18.30.jar]
[DEBUG]   (f) basedir = /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot
[DEBUG]   (f) buildDirectory = /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target
[DEBUG]   (f) compilePath = [/Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes, /Users/<USER>/.m2/repository/com/gitee/starblues/springboot-plugin-framework/2.2.1-RELEASE/springboot-plugin-framework-2.2.1-RELEASE.jar, /Users/<USER>/.m2/repository/org/pf4j/pf4j/3.1.0/pf4j-3.1.0.jar, /Users/<USER>/.m2/repository/com/github/zafarkhaja/java-semver/0.9.0/java-semver-0.9.0.jar, /Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.9.4/jackson-databind-2.9.4.jar, /Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.9.0/jackson-annotations-2.9.0.jar, /Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.9.4/jackson-core-2.9.4.jar, /Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.9.4/jackson-dataformat-yaml-2.9.4.jar, /Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.19/snakeyaml-1.19.jar, /Users/<USER>/.m2/repository/com/gitee/starblues/springboot-plugin-framework-extension-mybatis/2.2.1-RELEASE/springboot-plugin-framework-extension-mybatis-2.2.1-RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.0.0.RELEASE/spring-boot-starter-web-2.0.0.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.0.0.RELEASE/spring-boot-starter-2.0.0.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.0.0.RELEASE/spring-boot-2.0.0.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.0.0.RELEASE/spring-boot-starter-logging-2.0.0.RELEASE.jar, /Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, /Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, /Users/<USER>/.m2/repository/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2.jar, /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.0.0.RELEASE/spring-boot-starter-json-2.0.0.RELEASE.jar, /Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.9.4/jackson-datatype-jdk8-2.9.4.jar, /Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.9.4/jackson-datatype-jsr310-2.9.4.jar, /Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.9.4/jackson-module-parameter-names-2.9.4.jar, /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.0.0.RELEASE/spring-boot-starter-tomcat-2.0.0.RELEASE.jar, /Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/8.5.28/tomcat-embed-core-8.5.28.jar, /Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/8.5.28/tomcat-embed-el-8.5.28.jar, /Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/8.5.28/tomcat-embed-websocket-8.5.28.jar, /Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.0.7.Final/hibernate-validator-6.0.7.Final.jar, /Users/<USER>/.m2/repository/javax/validation/validation-api/2.0.1.Final/validation-api-2.0.1.Final.jar, /Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.3.2.Final/jboss-logging-3.3.2.Final.jar, /Users/<USER>/.m2/repository/org/springframework/spring-web/5.0.4.RELEASE/spring-web-5.0.4.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/spring-beans/5.0.4.RELEASE/spring-beans-5.0.4.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.0.4.RELEASE/spring-webmvc-5.0.4.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/spring-aop/5.0.4.RELEASE/spring-aop-5.0.4.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/spring-context/5.0.4.RELEASE/spring-context-5.0.4.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/spring-expression/5.0.4.RELEASE/spring-expression-5.0.4.RELEASE.jar, /Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.7.10/byte-buddy-1.7.10.jar, /Users/<USER>/.m2/repository/org/springframework/spring-core/5.0.4.RELEASE/spring-core-5.0.4.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.0.4.RELEASE/spring-jcl-5.0.4.RELEASE.jar, /Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar, /Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.28/mysql-connector-java-8.0.28.jar, /Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.11.4/protobuf-java-3.11.4.jar, /Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.2/httpclient-4.5.2.jar, /Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.9/httpcore-4.4.9.jar, /Users/<USER>/.m2/repository/commons-codec/commons-codec/1.11/commons-codec-1.11.jar, /Users/<USER>/.m2/repository/net/sourceforge/jexcelapi/jxl/2.6.12/jxl-2.6.12.jar, /Users/<USER>/.m2/repository/log4j/log4j/1.2.14/log4j-1.2.14.jar, /Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.30/lombok-1.18.30.jar, /Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.15.0/log4j-to-slf4j-2.15.0.jar, /Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.25/slf4j-api-1.7.25.jar, /Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.10.0/log4j-api-2.10.0.jar, /Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.25/jul-to-slf4j-1.7.25.jar, /Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.0.7.1/mybatis-plus-boot-starter-3.0.7.1.jar, /Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.0.7.1/mybatis-plus-3.0.7.1.jar, /Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.0.7.1/mybatis-plus-extension-3.0.7.1.jar, /Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.0.7.1/mybatis-plus-core-3.0.7.1.jar, /Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.0.7.1/mybatis-plus-annotation-3.0.7.1.jar, /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.0.0.RELEASE/spring-boot-autoconfigure-2.0.0.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.0.0.RELEASE/spring-boot-starter-jdbc-2.0.0.RELEASE.jar, /Users/<USER>/.m2/repository/com/zaxxer/HikariCP/2.7.8/HikariCP-2.7.8.jar, /Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.0.4.RELEASE/spring-jdbc-5.0.4.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-redis/1.4.1.RELEASE/spring-boot-starter-redis-1.4.1.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/2.0.5.RELEASE/spring-data-redis-2.0.5.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.0.5.RELEASE/spring-data-keyvalue-2.0.5.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.0.5.RELEASE/spring-data-commons-2.0.5.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/spring-tx/5.0.4.RELEASE/spring-tx-5.0.4.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/spring-oxm/5.0.4.RELEASE/spring-oxm-5.0.4.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.0.4.RELEASE/spring-context-support-5.0.4.RELEASE.jar, /Users/<USER>/.m2/repository/redis/clients/jedis/2.9.0/jedis-2.9.0.jar, /Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.5.0/commons-pool2-2.5.0.jar, /Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/2.7.0/springfox-swagger2-2.7.0.jar, /Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.5.13/swagger-annotations-1.5.13.jar, /Users/<USER>/.m2/repository/io/swagger/swagger-models/1.5.13/swagger-models-1.5.13.jar, /Users/<USER>/.m2/repository/io/springfox/springfox-spi/2.7.0/springfox-spi-2.7.0.jar, /Users/<USER>/.m2/repository/io/springfox/springfox-core/2.7.0/springfox-core-2.7.0.jar, /Users/<USER>/.m2/repository/io/springfox/springfox-schema/2.7.0/springfox-schema-2.7.0.jar, /Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/2.7.0/springfox-swagger-common-2.7.0.jar, /Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/2.7.0/springfox-spring-web-2.7.0.jar, /Users/<USER>/.m2/repository/org/reflections/reflections/0.9.11/reflections-0.9.11.jar, /Users/<USER>/.m2/repository/org/javassist/javassist/3.21.0-GA/javassist-3.21.0-GA.jar, /Users/<USER>/.m2/repository/com/google/guava/guava/18.0/guava-18.0.jar, /Users/<USER>/.m2/repository/com/fasterxml/classmate/1.3.4/classmate-1.3.4.jar, /Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar, /Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.1.0.Final/mapstruct-1.1.0.Final.jar, /Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-bootstrap-ui/1.6/swagger-bootstrap-ui-1.6.jar, /Users/<USER>/.m2/repository/com/aliyun/oss/aliyun-sdk-oss/3.10.1/aliyun-sdk-oss-3.10.1.jar, /Users/<USER>/.m2/repository/org/jdom/jdom/1.1/jdom-1.1.jar, /Users/<USER>/.m2/repository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar, /Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar, /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-core/3.4.0/aliyun-java-sdk-core-3.4.0.jar, /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ram/3.0.0/aliyun-java-sdk-ram-3.0.0.jar, /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java-sdk-sts-3.0.0.jar, /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4.2.0.jar, /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-kms/2.7.0/aliyun-java-sdk-kms-2.7.0.jar, /Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.2/gson-2.8.2.jar, /Users/<USER>/.m2/repository/com/itextpdf/itextpdf/5.5.13.1/itextpdf-5.5.13.1.jar, /Users/<USER>/.m2/repository/commons-io/commons-io/1.3.2/commons-io-1.3.2.jar, /Users/<USER>/.m2/repository/com/belerweb/pinyin4j/2.5.1/pinyin4j-2.5.1.jar, /Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-starter/1.2.13/pagehelper-spring-boot-starter-1.2.13.jar, /Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.1.1/mybatis-spring-boot-starter-2.1.1.jar, /Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/2.1.1/mybatis-spring-boot-autoconfigure-2.1.1.jar, /Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.3/mybatis-3.5.3.jar, /Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.0.3/mybatis-spring-2.0.3.jar, /Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-autoconfigure/1.2.13/pagehelper-spring-boot-autoconfigure-1.2.13.jar, /Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper/5.1.11/pagehelper-5.1.11.jar, /Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/2.0/jsqlparser-2.0.jar]
[DEBUG]   (f) compileSourceRoots = [/Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java]
[DEBUG]   (f) compilerId = javac
[DEBUG]   (f) debug = true
[DEBUG]   (f) encoding = UTF-8
[DEBUG]   (f) failOnError = true
[DEBUG]   (f) failOnWarning = false
[DEBUG]   (f) forceJavacCompilerUse = false
[DEBUG]   (f) fork = false
[DEBUG]   (f) generatedSourcesDirectory = /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/generated-sources/annotations
[DEBUG]   (f) mojoExecution = org.apache.maven.plugins:maven-compiler-plugin:3.7.0:compile {execution: default-compile}
[DEBUG]   (f) optimize = false
[DEBUG]   (f) outputDirectory = /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes
[DEBUG]   (f) parameters = true
[DEBUG]   (f) project = MavenProject: com.jsh:jshERP-boot:3.5-SNAPSHOT @ /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/pom.xml
[DEBUG]   (f) projectArtifact = com.jsh:jshERP-boot:jar:3.5-SNAPSHOT
[DEBUG]   (f) session = org.apache.maven.execution.MavenSession@62765aec
[DEBUG]   (f) showDeprecation = false
[DEBUG]   (f) showWarnings = false
[DEBUG]   (f) skipMultiThreadWarning = false
[DEBUG]   (f) source = 1.8
[DEBUG]   (f) staleMillis = 0
[DEBUG]   (f) target = 1.8
[DEBUG]   (f) useIncrementalCompilation = true
[DEBUG]   (f) verbose = false
[DEBUG] -- end configuration --
[DEBUG] Using compiler 'javac'.
[DEBUG] Adding /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/generated-sources/annotations to compile source roots:
  /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java
[DEBUG] New compile source roots:
  /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java
  /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/generated-sources/annotations
[DEBUG] CompilerReuseStrategy: reuseCreated
[DEBUG] useIncrementalCompilation enabled
[INFO] Changes detected - recompiling the module!
[DEBUG] Classpath:
[DEBUG]  /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes
[DEBUG]  /Users/<USER>/.m2/repository/com/gitee/starblues/springboot-plugin-framework/2.2.1-RELEASE/springboot-plugin-framework-2.2.1-RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/pf4j/pf4j/3.1.0/pf4j-3.1.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/github/zafarkhaja/java-semver/0.9.0/java-semver-0.9.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.9.4/jackson-databind-2.9.4.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.9.0/jackson-annotations-2.9.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.9.4/jackson-core-2.9.4.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.9.4/jackson-dataformat-yaml-2.9.4.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.19/snakeyaml-1.19.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/gitee/starblues/springboot-plugin-framework-extension-mybatis/2.2.1-RELEASE/springboot-plugin-framework-extension-mybatis-2.2.1-RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.0.0.RELEASE/spring-boot-starter-web-2.0.0.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.0.0.RELEASE/spring-boot-starter-2.0.0.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.0.0.RELEASE/spring-boot-2.0.0.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.0.0.RELEASE/spring-boot-starter-logging-2.0.0.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar
[DEBUG]  /Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar
[DEBUG]  /Users/<USER>/.m2/repository/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.0.0.RELEASE/spring-boot-starter-json-2.0.0.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.9.4/jackson-datatype-jdk8-2.9.4.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.9.4/jackson-datatype-jsr310-2.9.4.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.9.4/jackson-module-parameter-names-2.9.4.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.0.0.RELEASE/spring-boot-starter-tomcat-2.0.0.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/8.5.28/tomcat-embed-core-8.5.28.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/8.5.28/tomcat-embed-el-8.5.28.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/8.5.28/tomcat-embed-websocket-8.5.28.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.0.7.Final/hibernate-validator-6.0.7.Final.jar
[DEBUG]  /Users/<USER>/.m2/repository/javax/validation/validation-api/2.0.1.Final/validation-api-2.0.1.Final.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.3.2.Final/jboss-logging-3.3.2.Final.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-web/5.0.4.RELEASE/spring-web-5.0.4.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-beans/5.0.4.RELEASE/spring-beans-5.0.4.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.0.4.RELEASE/spring-webmvc-5.0.4.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-aop/5.0.4.RELEASE/spring-aop-5.0.4.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-context/5.0.4.RELEASE/spring-context-5.0.4.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-expression/5.0.4.RELEASE/spring-expression-5.0.4.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.7.10/byte-buddy-1.7.10.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-core/5.0.4.RELEASE/spring-core-5.0.4.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.0.4.RELEASE/spring-jcl-5.0.4.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar
[DEBUG]  /Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.28/mysql-connector-java-8.0.28.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.11.4/protobuf-java-3.11.4.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.2/httpclient-4.5.2.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.9/httpcore-4.4.9.jar
[DEBUG]  /Users/<USER>/.m2/repository/commons-codec/commons-codec/1.11/commons-codec-1.11.jar
[DEBUG]  /Users/<USER>/.m2/repository/net/sourceforge/jexcelapi/jxl/2.6.12/jxl-2.6.12.jar
[DEBUG]  /Users/<USER>/.m2/repository/log4j/log4j/1.2.14/log4j-1.2.14.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.30/lombok-1.18.30.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.15.0/log4j-to-slf4j-2.15.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.25/slf4j-api-1.7.25.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.10.0/log4j-api-2.10.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.25/jul-to-slf4j-1.7.25.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.0.7.1/mybatis-plus-boot-starter-3.0.7.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.0.7.1/mybatis-plus-3.0.7.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.0.7.1/mybatis-plus-extension-3.0.7.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.0.7.1/mybatis-plus-core-3.0.7.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.0.7.1/mybatis-plus-annotation-3.0.7.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.0.0.RELEASE/spring-boot-autoconfigure-2.0.0.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.0.0.RELEASE/spring-boot-starter-jdbc-2.0.0.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/zaxxer/HikariCP/2.7.8/HikariCP-2.7.8.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.0.4.RELEASE/spring-jdbc-5.0.4.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-redis/1.4.1.RELEASE/spring-boot-starter-redis-1.4.1.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/2.0.5.RELEASE/spring-data-redis-2.0.5.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.0.5.RELEASE/spring-data-keyvalue-2.0.5.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.0.5.RELEASE/spring-data-commons-2.0.5.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-tx/5.0.4.RELEASE/spring-tx-5.0.4.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-oxm/5.0.4.RELEASE/spring-oxm-5.0.4.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.0.4.RELEASE/spring-context-support-5.0.4.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/redis/clients/jedis/2.9.0/jedis-2.9.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.5.0/commons-pool2-2.5.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/2.7.0/springfox-swagger2-2.7.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.5.13/swagger-annotations-1.5.13.jar
[DEBUG]  /Users/<USER>/.m2/repository/io/swagger/swagger-models/1.5.13/swagger-models-1.5.13.jar
[DEBUG]  /Users/<USER>/.m2/repository/io/springfox/springfox-spi/2.7.0/springfox-spi-2.7.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/io/springfox/springfox-core/2.7.0/springfox-core-2.7.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/io/springfox/springfox-schema/2.7.0/springfox-schema-2.7.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/2.7.0/springfox-swagger-common-2.7.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/2.7.0/springfox-spring-web-2.7.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/reflections/reflections/0.9.11/reflections-0.9.11.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/javassist/javassist/3.21.0-GA/javassist-3.21.0-GA.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/google/guava/guava/18.0/guava-18.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/fasterxml/classmate/1.3.4/classmate-1.3.4.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.1.0.Final/mapstruct-1.1.0.Final.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-bootstrap-ui/1.6/swagger-bootstrap-ui-1.6.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/aliyun/oss/aliyun-sdk-oss/3.10.1/aliyun-sdk-oss-3.10.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/jdom/jdom/1.1/jdom-1.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-core/3.4.0/aliyun-java-sdk-core-3.4.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ram/3.0.0/aliyun-java-sdk-ram-3.0.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java-sdk-sts-3.0.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4.2.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-kms/2.7.0/aliyun-java-sdk-kms-2.7.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.2/gson-2.8.2.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/itextpdf/itextpdf/5.5.13.1/itextpdf-5.5.13.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/commons-io/commons-io/1.3.2/commons-io-1.3.2.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/belerweb/pinyin4j/2.5.1/pinyin4j-2.5.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-starter/1.2.13/pagehelper-spring-boot-starter-1.2.13.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.1.1/mybatis-spring-boot-starter-2.1.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/2.1.1/mybatis-spring-boot-autoconfigure-2.1.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.3/mybatis-3.5.3.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.0.3/mybatis-spring-2.0.3.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-autoconfigure/1.2.13/pagehelper-spring-boot-autoconfigure-1.2.13.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper/5.1.11/pagehelper-5.1.11.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/2.0/jsqlparser-2.0.jar
[DEBUG] Source roots:
[DEBUG]  /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java
[DEBUG]  /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/generated-sources/annotations
[DEBUG] Command line options:
[DEBUG] -d /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes -classpath /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes:/Users/<USER>/.m2/repository/com/gitee/starblues/springboot-plugin-framework/2.2.1-RELEASE/springboot-plugin-framework-2.2.1-RELEASE.jar:/Users/<USER>/.m2/repository/org/pf4j/pf4j/3.1.0/pf4j-3.1.0.jar:/Users/<USER>/.m2/repository/com/github/zafarkhaja/java-semver/0.9.0/java-semver-0.9.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.9.4/jackson-databind-2.9.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.9.0/jackson-annotations-2.9.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.9.4/jackson-core-2.9.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.9.4/jackson-dataformat-yaml-2.9.4.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.19/snakeyaml-1.19.jar:/Users/<USER>/.m2/repository/com/gitee/starblues/springboot-plugin-framework-extension-mybatis/2.2.1-RELEASE/springboot-plugin-framework-extension-mybatis-2.2.1-RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.0.0.RELEASE/spring-boot-starter-web-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.0.0.RELEASE/spring-boot-starter-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.0.0.RELEASE/spring-boot-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.0.0.RELEASE/spring-boot-starter-logging-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar:/Users/<USER>/.m2/repository/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.0.0.RELEASE/spring-boot-starter-json-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.9.4/jackson-datatype-jdk8-2.9.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.9.4/jackson-datatype-jsr310-2.9.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.9.4/jackson-module-parameter-names-2.9.4.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.0.0.RELEASE/spring-boot-starter-tomcat-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/8.5.28/tomcat-embed-core-8.5.28.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/8.5.28/tomcat-embed-el-8.5.28.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/8.5.28/tomcat-embed-websocket-8.5.28.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.0.7.Final/hibernate-validator-6.0.7.Final.jar:/Users/<USER>/.m2/repository/javax/validation/validation-api/2.0.1.Final/validation-api-2.0.1.Final.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.3.2.Final/jboss-logging-3.3.2.Final.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.0.4.RELEASE/spring-web-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.0.4.RELEASE/spring-beans-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.0.4.RELEASE/spring-webmvc-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.0.4.RELEASE/spring-aop-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.0.4.RELEASE/spring-context-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.0.4.RELEASE/spring-expression-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.7.10/byte-buddy-1.7.10.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.0.4.RELEASE/spring-core-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.0.4.RELEASE/spring-jcl-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.28/mysql-connector-java-8.0.28.jar:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.11.4/protobuf-java-3.11.4.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.2/httpclient-4.5.2.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.9/httpcore-4.4.9.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.11/commons-codec-1.11.jar:/Users/<USER>/.m2/repository/net/sourceforge/jexcelapi/jxl/2.6.12/jxl-2.6.12.jar:/Users/<USER>/.m2/repository/log4j/log4j/1.2.14/log4j-1.2.14.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.30/lombok-1.18.30.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.15.0/log4j-to-slf4j-2.15.0.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.25/slf4j-api-1.7.25.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.10.0/log4j-api-2.10.0.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.25/jul-to-slf4j-1.7.25.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.0.7.1/mybatis-plus-boot-starter-3.0.7.1.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.0.7.1/mybatis-plus-3.0.7.1.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.0.7.1/mybatis-plus-extension-3.0.7.1.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.0.7.1/mybatis-plus-core-3.0.7.1.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.0.7.1/mybatis-plus-annotation-3.0.7.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.0.0.RELEASE/spring-boot-autoconfigure-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.0.0.RELEASE/spring-boot-starter-jdbc-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/2.7.8/HikariCP-2.7.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.0.4.RELEASE/spring-jdbc-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-redis/1.4.1.RELEASE/spring-boot-starter-redis-1.4.1.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/2.0.5.RELEASE/spring-data-redis-2.0.5.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.0.5.RELEASE/spring-data-keyvalue-2.0.5.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.0.5.RELEASE/spring-data-commons-2.0.5.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.0.4.RELEASE/spring-tx-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/5.0.4.RELEASE/spring-oxm-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.0.4.RELEASE/spring-context-support-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/redis/clients/jedis/2.9.0/jedis-2.9.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.5.0/commons-pool2-2.5.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/2.7.0/springfox-swagger2-2.7.0.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.5.13/swagger-annotations-1.5.13.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-models/1.5.13/swagger-models-1.5.13.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spi/2.7.0/springfox-spi-2.7.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-core/2.7.0/springfox-core-2.7.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-schema/2.7.0/springfox-schema-2.7.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/2.7.0/springfox-swagger-common-2.7.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/2.7.0/springfox-spring-web-2.7.0.jar:/Users/<USER>/.m2/repository/org/reflections/reflections/0.9.11/reflections-0.9.11.jar:/Users/<USER>/.m2/repository/org/javassist/javassist/3.21.0-GA/javassist-3.21.0-GA.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/18.0/guava-18.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.3.4/classmate-1.3.4.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.1.0.Final/mapstruct-1.1.0.Final.jar:/Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-bootstrap-ui/1.6/swagger-bootstrap-ui-1.6.jar:/Users/<USER>/.m2/repository/com/aliyun/oss/aliyun-sdk-oss/3.10.1/aliyun-sdk-oss-3.10.1.jar:/Users/<USER>/.m2/repository/org/jdom/jdom/1.1/jdom-1.1.jar:/Users/<USER>/.m2/repository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar:/Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-core/3.4.0/aliyun-java-sdk-core-3.4.0.jar:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ram/3.0.0/aliyun-java-sdk-ram-3.0.0.jar:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java-sdk-sts-3.0.0.jar:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4.2.0.jar:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-kms/2.7.0/aliyun-java-sdk-kms-2.7.0.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.2/gson-2.8.2.jar:/Users/<USER>/.m2/repository/com/itextpdf/itextpdf/5.5.13.1/itextpdf-5.5.13.1.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/1.3.2/commons-io-1.3.2.jar:/Users/<USER>/.m2/repository/com/belerweb/pinyin4j/2.5.1/pinyin4j-2.5.1.jar:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-starter/1.2.13/pagehelper-spring-boot-starter-1.2.13.jar:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.1.1/mybatis-spring-boot-starter-2.1.1.jar:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/2.1.1/mybatis-spring-boot-autoconfigure-2.1.1.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.3/mybatis-3.5.3.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.0.3/mybatis-spring-2.0.3.jar:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-autoconfigure/1.2.13/pagehelper-spring-boot-autoconfigure-1.2.13.jar:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper/5.1.11/pagehelper-5.1.11.jar:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/2.0/jsqlparser-2.0.jar: -sourcepath /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java:/Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/generated-sources/annotations: -s /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/generated-sources/annotations -processorpath /Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.30/lombok-1.18.30.jar: -g -parameters -nowarn -target 1.8 -source 1.8 -encoding UTF-8
[DEBUG] incrementalBuildHelper#beforeRebuildExecution
[INFO] Compiling 277 source files to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes
[DEBUG] incrementalBuildHelper#afterRebuildExecution
[INFO] -------------------------------------------------------------
[ERROR] COMPILATION ERROR : 
[INFO] -------------------------------------------------------------
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/controller/WorkOrderController.java:[6,29] 找不到符号
  符号:   类 ErpInfo
  位置: 程序包 com.jsh.erp.constants
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/controller/ProductionController.java:[6,29] 找不到符号
  符号:   类 ErpInfo
  位置: 程序包 com.jsh.erp.constants
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/WorkOrderItemMapper.java:[4,39] 找不到符号
  符号:   类 WorkOrderItemExample
  位置: 程序包 com.jsh.erp.datasource.entities
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/WorkOrderItemMapper.java:[18,25] 找不到符号
  符号:   类 WorkOrderItemExample
  位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/WorkOrderItemMapper.java:[23,25] 找不到符号
  符号:   类 WorkOrderItemExample
  位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/WorkOrderItemMapper.java:[43,41] 找不到符号
  符号:   类 WorkOrderItemExample
  位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/WorkOrderItemMapper.java:[53,91] 找不到符号
  符号:   类 WorkOrderItemExample
  位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/WorkOrderItemMapper.java:[58,82] 找不到符号
  符号:   类 WorkOrderItemExample
  位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[INFO] 8 errors 
[INFO] -------------------------------------------------------------
[INFO] ------------------------------------------------------------------------
[INFO] BUILD FAILURE
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  5.681 s
[INFO] Finished at: 2025-06-21T17:42:24+08:00
[INFO] ------------------------------------------------------------------------
[ERROR] Failed to execute goal org.apache.maven.plugins:maven-compiler-plugin:3.7.0:compile (default-compile) on project jshERP-boot: Compilation failure: Compilation failure: 
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/controller/WorkOrderController.java:[6,29] 找不到符号
[ERROR]   符号:   类 ErpInfo
[ERROR]   位置: 程序包 com.jsh.erp.constants
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/controller/ProductionController.java:[6,29] 找不到符号
[ERROR]   符号:   类 ErpInfo
[ERROR]   位置: 程序包 com.jsh.erp.constants
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/WorkOrderItemMapper.java:[4,39] 找不到符号
[ERROR]   符号:   类 WorkOrderItemExample
[ERROR]   位置: 程序包 com.jsh.erp.datasource.entities
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/WorkOrderItemMapper.java:[18,25] 找不到符号
[ERROR]   符号:   类 WorkOrderItemExample
[ERROR]   位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/WorkOrderItemMapper.java:[23,25] 找不到符号
[ERROR]   符号:   类 WorkOrderItemExample
[ERROR]   位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/WorkOrderItemMapper.java:[43,41] 找不到符号
[ERROR]   符号:   类 WorkOrderItemExample
[ERROR]   位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/WorkOrderItemMapper.java:[53,91] 找不到符号
[ERROR]   符号:   类 WorkOrderItemExample
[ERROR]   位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/WorkOrderItemMapper.java:[58,82] 找不到符号
[ERROR]   符号:   类 WorkOrderItemExample
[ERROR]   位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] -> [Help 1]
org.apache.maven.lifecycle.LifecycleExecutionException: Failed to execute goal org.apache.maven.plugins:maven-compiler-plugin:3.7.0:compile (default-compile) on project jshERP-boot: Compilation failure
    at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:333)
    at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
    at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
    at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
    at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
    at org.apache.maven.lifecycle.internal.MojoExecutor.executeForkedExecutions (MojoExecutor.java:448)
    at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:311)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
    at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
    at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
    at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
    at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
    at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
    at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
    at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
    at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
    at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
    at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
    at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
    at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
    at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
    at jdk.internal.reflect.DirectMethodHandleAccessor.invoke (DirectMethodHandleAccessor.java:103)
    at java.lang.reflect.Method.invoke (Method.java:580)
    at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
    at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
    at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
    at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
Caused by: org.apache.maven.plugin.compiler.CompilationFailureException: Compilation failure
    at org.apache.maven.plugin.compiler.AbstractCompilerMojo.execute (AbstractCompilerMojo.java:1161)
    at org.apache.maven.plugin.compiler.CompilerMojo.execute (CompilerMojo.java:168)
    at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
    at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
    at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
    at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
    at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
    at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
    at org.apache.maven.lifecycle.internal.MojoExecutor.executeForkedExecutions (MojoExecutor.java:448)
    at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:311)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
    at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
    at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
    at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
    at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
    at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
    at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
    at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
    at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
    at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
    at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
    at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
    at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
    at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
    at jdk.internal.reflect.DirectMethodHandleAccessor.invoke (DirectMethodHandleAccessor.java:103)
    at java.lang.reflect.Method.invoke (Method.java:580)
    at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
    at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
    at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
    at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
[ERROR] 
[ERROR] 
[ERROR] For more information about the errors and possible solutions, please read the following articles:
[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/MojoFailureException
[DEBUG] Shutting down adapter factory; available factories [file-lock, rwlock-local, semaphore-local, noop]; available name mappers [discriminating, file-gav, file-hgav, file-static, gav, static]
[DEBUG] Shutting down 'file-lock' factory
[DEBUG] Shutting down 'rwlock-local' factory
[DEBUG] Shutting down 'semaphore-local' factory
[DEBUG] Shutting down 'noop' factory
