server.port=9999
#登录超时-秒
server.servlet.session.timeout=36000
#服务路径
server.servlet.context-path=/jshERP-boot
#数据库连接
spring.datasource.url=******************************************************************************************************************************************************************************************************************
spring.datasource.driverClassName=com.mysql.jdbc.Driver
spring.datasource.username=jsh_user
spring.datasource.password=123456
#mybatis-plus配置
mybatis-plus.mapper-locations=classpath:./mapper_xml/*.xml
# Redis
spring.redis.host=127.0.0.1
spring.redis.port=6379
spring.redis.password=1234abcd
#租户对应的角色id
manage.roleId=10
#租户允许创建的用户数
tenant.userNumLimit=1000000
#租户允许试用的天数
tenant.tryDayLimit=3000
# 插件配置已移除
#文件上传方式 1-本机 2-oss
file.uploadType=1
#文件上传根目录
file.path=/opt/jshERP/upload
#文件上传临时路径
server.tomcat.basedir=/opt/tmp/tomcat
#文件上传限制(byte)
spring.servlet.multipart.max-file-size=10485760
spring.servlet.multipart.max-request-size=10485760

# 日历排班插件配置已移除