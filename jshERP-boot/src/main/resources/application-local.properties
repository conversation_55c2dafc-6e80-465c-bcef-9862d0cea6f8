# jshERP 本地开发环境配置
# 连接到 Docker 中的数据库服务，后端服务在宿主机运行

# 服务器配置
server.port=9999
server.servlet.context-path=/jshERP-boot

# 数据库配置（连接到 Docker 中的 MySQL）
spring.datasource.url=******************************************************************************************************************************************************************************************************************
spring.datasource.username=jsh_user
spring.datasource.password=123456
spring.datasource.driver-class-name=com.mysql.jdbc.Driver

# 连接池配置
spring.datasource.max-active=20
spring.datasource.max-idle=8
spring.datasource.min-idle=8
spring.datasource.initial-size=10

# Redis 配置（连接到 Docker 中的 Redis）
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.password=1234abcd
spring.redis.timeout=3000
spring.redis.jedis.pool.max-active=20
spring.redis.jedis.pool.max-wait=-1
spring.redis.jedis.pool.max-idle=8
spring.redis.jedis.pool.min-idle=0

# MyBatis 配置
mybatis-plus.mapper-locations=classpath*:mapper_xml/*Mapper.xml
mybatis-plus.type-aliases-package=com.jsh.erp.datasource.entities
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.cache-enabled=false
mybatis-plus.configuration.call-setters-on-nulls=true
mybatis-plus.configuration.jdbc-type-for-null=null

# 分页插件配置
pagehelper.helperDialect=mysql
pagehelper.reasonable=true
pagehelper.supportMethodsArguments=true
pagehelper.params=count=countSql

# 文件上传配置
file.uploadPath=/Users/<USER>/Desktop/jshERP-0612-Cursor/volumes/uploads/
file.staticAccessPath=/upload/**

# 日志配置
logging.level.com.jsh.erp=debug
logging.level.root=info
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# SpringBoot DevTools 配置（开发环境热重载）
spring.devtools.restart.enabled=true
spring.devtools.restart.additional-paths=src/main/java
spring.devtools.livereload.enabled=true

# Swagger 配置
swagger.enabled=true
swagger.title=jshERP API 文档
swagger.description=华夏ERP系统接口文档
swagger.version=3.5
swagger.contact.name=华夏ERP团队
swagger.contact.url=https://github.com/jishenghua/jshERP
swagger.contact.email=

# 其他配置
spring.jackson.time-zone=GMT+8
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss

# 多数据源配置
spring.shardingsphere.enabled=false 