<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.SupplierMapper">
  <resultMap id="BaseResultMap" type="com.jsh.erp.datasource.entities.Supplier">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="supplier" jdbcType="VARCHAR" property="supplier" />
    <result column="contacts" jdbcType="VARCHAR" property="contacts" />
    <result column="phone_num" jdbcType="VARCHAR" property="phoneNum" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="isystem" jdbcType="TINYINT" property="isystem" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="enabled" jdbcType="BIT" property="enabled" />
    <result column="advance_in" jdbcType="DECIMAL" property="advanceIn" />
    <result column="begin_need_get" jdbcType="DECIMAL" property="beginNeedGet" />
    <result column="begin_need_pay" jdbcType="DECIMAL" property="beginNeedPay" />
    <result column="all_need_get" jdbcType="DECIMAL" property="allNeedGet" />
    <result column="all_need_pay" jdbcType="DECIMAL" property="allNeedPay" />
    <result column="fax" jdbcType="VARCHAR" property="fax" />
    <result column="telephone" jdbcType="VARCHAR" property="telephone" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="tax_num" jdbcType="VARCHAR" property="taxNum" />
    <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
    <result column="account_number" jdbcType="VARCHAR" property="accountNumber" />
    <result column="tax_rate" jdbcType="DECIMAL" property="taxRate" />
    <result column="sort" jdbcType="VARCHAR" property="sort" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, supplier, contacts, phone_num, email, description, isystem, type, enabled, advance_in, 
    begin_need_get, begin_need_pay, all_need_get, all_need_pay, fax, telephone, address, 
    tax_num, bank_name, account_number, tax_rate, sort, creator, tenant_id, delete_flag
  </sql>
  <select id="selectByExample" parameterType="com.jsh.erp.datasource.entities.SupplierExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from jsh_supplier
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jsh_supplier
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from jsh_supplier
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.jsh.erp.datasource.entities.SupplierExample">
    delete from jsh_supplier
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.jsh.erp.datasource.entities.Supplier">
    insert into jsh_supplier (id, supplier, contacts, 
      phone_num, email, description, 
      isystem, type, enabled, 
      advance_in, begin_need_get, begin_need_pay, 
      all_need_get, all_need_pay, fax, 
      telephone, address, tax_num, 
      bank_name, account_number, tax_rate, 
      sort, creator, tenant_id, 
      delete_flag)
    values (#{id,jdbcType=BIGINT}, #{supplier,jdbcType=VARCHAR}, #{contacts,jdbcType=VARCHAR}, 
      #{phoneNum,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{isystem,jdbcType=TINYINT}, #{type,jdbcType=VARCHAR}, #{enabled,jdbcType=BIT}, 
      #{advanceIn,jdbcType=DECIMAL}, #{beginNeedGet,jdbcType=DECIMAL}, #{beginNeedPay,jdbcType=DECIMAL}, 
      #{allNeedGet,jdbcType=DECIMAL}, #{allNeedPay,jdbcType=DECIMAL}, #{fax,jdbcType=VARCHAR}, 
      #{telephone,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, #{taxNum,jdbcType=VARCHAR}, 
      #{bankName,jdbcType=VARCHAR}, #{accountNumber,jdbcType=VARCHAR}, #{taxRate,jdbcType=DECIMAL}, 
      #{sort,jdbcType=VARCHAR}, #{creator,jdbcType=BIGINT}, #{tenantId,jdbcType=BIGINT}, 
      #{deleteFlag,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.jsh.erp.datasource.entities.Supplier">
    insert into jsh_supplier
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="supplier != null">
        supplier,
      </if>
      <if test="contacts != null">
        contacts,
      </if>
      <if test="phoneNum != null">
        phone_num,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="isystem != null">
        isystem,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="enabled != null">
        enabled,
      </if>
      <if test="advanceIn != null">
        advance_in,
      </if>
      <if test="beginNeedGet != null">
        begin_need_get,
      </if>
      <if test="beginNeedPay != null">
        begin_need_pay,
      </if>
      <if test="allNeedGet != null">
        all_need_get,
      </if>
      <if test="allNeedPay != null">
        all_need_pay,
      </if>
      <if test="fax != null">
        fax,
      </if>
      <if test="telephone != null">
        telephone,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="taxNum != null">
        tax_num,
      </if>
      <if test="bankName != null">
        bank_name,
      </if>
      <if test="accountNumber != null">
        account_number,
      </if>
      <if test="taxRate != null">
        tax_rate,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="supplier != null">
        #{supplier,jdbcType=VARCHAR},
      </if>
      <if test="contacts != null">
        #{contacts,jdbcType=VARCHAR},
      </if>
      <if test="phoneNum != null">
        #{phoneNum,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="isystem != null">
        #{isystem,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="enabled != null">
        #{enabled,jdbcType=BIT},
      </if>
      <if test="advanceIn != null">
        #{advanceIn,jdbcType=DECIMAL},
      </if>
      <if test="beginNeedGet != null">
        #{beginNeedGet,jdbcType=DECIMAL},
      </if>
      <if test="beginNeedPay != null">
        #{beginNeedPay,jdbcType=DECIMAL},
      </if>
      <if test="allNeedGet != null">
        #{allNeedGet,jdbcType=DECIMAL},
      </if>
      <if test="allNeedPay != null">
        #{allNeedPay,jdbcType=DECIMAL},
      </if>
      <if test="fax != null">
        #{fax,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null">
        #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="taxNum != null">
        #{taxNum,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="accountNumber != null">
        #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="taxRate != null">
        #{taxRate,jdbcType=DECIMAL},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.jsh.erp.datasource.entities.SupplierExample" resultType="java.lang.Long">
    select count(*) from jsh_supplier
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update jsh_supplier
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.supplier != null">
        supplier = #{record.supplier,jdbcType=VARCHAR},
      </if>
      <if test="record.contacts != null">
        contacts = #{record.contacts,jdbcType=VARCHAR},
      </if>
      <if test="record.phoneNum != null">
        phone_num = #{record.phoneNum,jdbcType=VARCHAR},
      </if>
      <if test="record.email != null">
        email = #{record.email,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.isystem != null">
        isystem = #{record.isystem,jdbcType=TINYINT},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.enabled != null">
        enabled = #{record.enabled,jdbcType=BIT},
      </if>
      <if test="record.advanceIn != null">
        advance_in = #{record.advanceIn,jdbcType=DECIMAL},
      </if>
      <if test="record.beginNeedGet != null">
        begin_need_get = #{record.beginNeedGet,jdbcType=DECIMAL},
      </if>
      <if test="record.beginNeedPay != null">
        begin_need_pay = #{record.beginNeedPay,jdbcType=DECIMAL},
      </if>
      <if test="record.allNeedGet != null">
        all_need_get = #{record.allNeedGet,jdbcType=DECIMAL},
      </if>
      <if test="record.allNeedPay != null">
        all_need_pay = #{record.allNeedPay,jdbcType=DECIMAL},
      </if>
      <if test="record.fax != null">
        fax = #{record.fax,jdbcType=VARCHAR},
      </if>
      <if test="record.telephone != null">
        telephone = #{record.telephone,jdbcType=VARCHAR},
      </if>
      <if test="record.address != null">
        address = #{record.address,jdbcType=VARCHAR},
      </if>
      <if test="record.taxNum != null">
        tax_num = #{record.taxNum,jdbcType=VARCHAR},
      </if>
      <if test="record.bankName != null">
        bank_name = #{record.bankName,jdbcType=VARCHAR},
      </if>
      <if test="record.accountNumber != null">
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.taxRate != null">
        tax_rate = #{record.taxRate,jdbcType=DECIMAL},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=BIGINT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
      <if test="record.deleteFlag != null">
        delete_flag = #{record.deleteFlag,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update jsh_supplier
    set id = #{record.id,jdbcType=BIGINT},
      supplier = #{record.supplier,jdbcType=VARCHAR},
      contacts = #{record.contacts,jdbcType=VARCHAR},
      phone_num = #{record.phoneNum,jdbcType=VARCHAR},
      email = #{record.email,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      isystem = #{record.isystem,jdbcType=TINYINT},
      type = #{record.type,jdbcType=VARCHAR},
      enabled = #{record.enabled,jdbcType=BIT},
      advance_in = #{record.advanceIn,jdbcType=DECIMAL},
      begin_need_get = #{record.beginNeedGet,jdbcType=DECIMAL},
      begin_need_pay = #{record.beginNeedPay,jdbcType=DECIMAL},
      all_need_get = #{record.allNeedGet,jdbcType=DECIMAL},
      all_need_pay = #{record.allNeedPay,jdbcType=DECIMAL},
      fax = #{record.fax,jdbcType=VARCHAR},
      telephone = #{record.telephone,jdbcType=VARCHAR},
      address = #{record.address,jdbcType=VARCHAR},
      tax_num = #{record.taxNum,jdbcType=VARCHAR},
      bank_name = #{record.bankName,jdbcType=VARCHAR},
      account_number = #{record.accountNumber,jdbcType=VARCHAR},
      tax_rate = #{record.taxRate,jdbcType=DECIMAL},
      sort = #{record.sort,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=BIGINT},
      delete_flag = #{record.deleteFlag,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.jsh.erp.datasource.entities.Supplier">
    update jsh_supplier
    <set>
      <if test="supplier != null">
        supplier = #{supplier,jdbcType=VARCHAR},
      </if>
      <if test="contacts != null">
        contacts = #{contacts,jdbcType=VARCHAR},
      </if>
      <if test="phoneNum != null">
        phone_num = #{phoneNum,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="isystem != null">
        isystem = #{isystem,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="enabled != null">
        enabled = #{enabled,jdbcType=BIT},
      </if>
      <if test="advanceIn != null">
        advance_in = #{advanceIn,jdbcType=DECIMAL},
      </if>
      <if test="beginNeedGet != null">
        begin_need_get = #{beginNeedGet,jdbcType=DECIMAL},
      </if>
      <if test="beginNeedPay != null">
        begin_need_pay = #{beginNeedPay,jdbcType=DECIMAL},
      </if>
      <if test="allNeedGet != null">
        all_need_get = #{allNeedGet,jdbcType=DECIMAL},
      </if>
      <if test="allNeedPay != null">
        all_need_pay = #{allNeedPay,jdbcType=DECIMAL},
      </if>
      <if test="fax != null">
        fax = #{fax,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null">
        telephone = #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="taxNum != null">
        tax_num = #{taxNum,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        bank_name = #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="accountNumber != null">
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="taxRate != null">
        tax_rate = #{taxRate,jdbcType=DECIMAL},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jsh.erp.datasource.entities.Supplier">
    update jsh_supplier
    set supplier = #{supplier,jdbcType=VARCHAR},
      contacts = #{contacts,jdbcType=VARCHAR},
      phone_num = #{phoneNum,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      isystem = #{isystem,jdbcType=TINYINT},
      type = #{type,jdbcType=VARCHAR},
      enabled = #{enabled,jdbcType=BIT},
      advance_in = #{advanceIn,jdbcType=DECIMAL},
      begin_need_get = #{beginNeedGet,jdbcType=DECIMAL},
      begin_need_pay = #{beginNeedPay,jdbcType=DECIMAL},
      all_need_get = #{allNeedGet,jdbcType=DECIMAL},
      all_need_pay = #{allNeedPay,jdbcType=DECIMAL},
      fax = #{fax,jdbcType=VARCHAR},
      telephone = #{telephone,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      tax_num = #{taxNum,jdbcType=VARCHAR},
      bank_name = #{bankName,jdbcType=VARCHAR},
      account_number = #{accountNumber,jdbcType=VARCHAR},
      tax_rate = #{taxRate,jdbcType=DECIMAL},
      sort = #{sort,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      tenant_id = #{tenantId,jdbcType=BIGINT},
      delete_flag = #{deleteFlag,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>