<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.AccountHeadMapper">
  <resultMap id="BaseResultMap" type="com.jsh.erp.datasource.entities.AccountHead">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="organ_id" jdbcType="BIGINT" property="organId" />
    <result column="hands_person_id" jdbcType="BIGINT" property="handsPersonId" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="change_amount" jdbcType="DECIMAL" property="changeAmount" />
    <result column="discount_money" jdbcType="DECIMAL" property="discountMoney" />
    <result column="total_price" jdbcType="DECIMAL" property="totalPrice" />
    <result column="account_id" jdbcType="BIGINT" property="accountId" />
    <result column="bill_no" jdbcType="VARCHAR" property="billNo" />
    <result column="bill_time" jdbcType="TIMESTAMP" property="billTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, type, organ_id, hands_person_id, creator, change_amount, discount_money, total_price, 
    account_id, bill_no, bill_time, remark, file_name, status, source, tenant_id, delete_flag
  </sql>
  <select id="selectByExample" parameterType="com.jsh.erp.datasource.entities.AccountHeadExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from jsh_account_head
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jsh_account_head
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from jsh_account_head
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.jsh.erp.datasource.entities.AccountHeadExample">
    delete from jsh_account_head
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.jsh.erp.datasource.entities.AccountHead">
    insert into jsh_account_head (id, type, organ_id, 
      hands_person_id, creator, change_amount, 
      discount_money, total_price, account_id, 
      bill_no, bill_time, remark, 
      file_name, status, source, 
      tenant_id, delete_flag)
    values (#{id,jdbcType=BIGINT}, #{type,jdbcType=VARCHAR}, #{organId,jdbcType=BIGINT}, 
      #{handsPersonId,jdbcType=BIGINT}, #{creator,jdbcType=BIGINT}, #{changeAmount,jdbcType=DECIMAL}, 
      #{discountMoney,jdbcType=DECIMAL}, #{totalPrice,jdbcType=DECIMAL}, #{accountId,jdbcType=BIGINT}, 
      #{billNo,jdbcType=VARCHAR}, #{billTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, 
      #{fileName,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{source,jdbcType=VARCHAR}, 
      #{tenantId,jdbcType=BIGINT}, #{deleteFlag,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.jsh.erp.datasource.entities.AccountHead">
    insert into jsh_account_head
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="organId != null">
        organ_id,
      </if>
      <if test="handsPersonId != null">
        hands_person_id,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="changeAmount != null">
        change_amount,
      </if>
      <if test="discountMoney != null">
        discount_money,
      </if>
      <if test="totalPrice != null">
        total_price,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="billNo != null">
        bill_no,
      </if>
      <if test="billTime != null">
        bill_time,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="organId != null">
        #{organId,jdbcType=BIGINT},
      </if>
      <if test="handsPersonId != null">
        #{handsPersonId,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="changeAmount != null">
        #{changeAmount,jdbcType=DECIMAL},
      </if>
      <if test="discountMoney != null">
        #{discountMoney,jdbcType=DECIMAL},
      </if>
      <if test="totalPrice != null">
        #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=BIGINT},
      </if>
      <if test="billNo != null">
        #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="billTime != null">
        #{billTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.jsh.erp.datasource.entities.AccountHeadExample" resultType="java.lang.Long">
    select count(*) from jsh_account_head
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update jsh_account_head
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.organId != null">
        organ_id = #{record.organId,jdbcType=BIGINT},
      </if>
      <if test="record.handsPersonId != null">
        hands_person_id = #{record.handsPersonId,jdbcType=BIGINT},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=BIGINT},
      </if>
      <if test="record.changeAmount != null">
        change_amount = #{record.changeAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.discountMoney != null">
        discount_money = #{record.discountMoney,jdbcType=DECIMAL},
      </if>
      <if test="record.totalPrice != null">
        total_price = #{record.totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=BIGINT},
      </if>
      <if test="record.billNo != null">
        bill_no = #{record.billNo,jdbcType=VARCHAR},
      </if>
      <if test="record.billTime != null">
        bill_time = #{record.billTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.fileName != null">
        file_name = #{record.fileName,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.source != null">
        source = #{record.source,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
      <if test="record.deleteFlag != null">
        delete_flag = #{record.deleteFlag,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update jsh_account_head
    set id = #{record.id,jdbcType=BIGINT},
      type = #{record.type,jdbcType=VARCHAR},
      organ_id = #{record.organId,jdbcType=BIGINT},
      hands_person_id = #{record.handsPersonId,jdbcType=BIGINT},
      creator = #{record.creator,jdbcType=BIGINT},
      change_amount = #{record.changeAmount,jdbcType=DECIMAL},
      discount_money = #{record.discountMoney,jdbcType=DECIMAL},
      total_price = #{record.totalPrice,jdbcType=DECIMAL},
      account_id = #{record.accountId,jdbcType=BIGINT},
      bill_no = #{record.billNo,jdbcType=VARCHAR},
      bill_time = #{record.billTime,jdbcType=TIMESTAMP},
      remark = #{record.remark,jdbcType=VARCHAR},
      file_name = #{record.fileName,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      source = #{record.source,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=BIGINT},
      delete_flag = #{record.deleteFlag,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.jsh.erp.datasource.entities.AccountHead">
    update jsh_account_head
    <set>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="organId != null">
        organ_id = #{organId,jdbcType=BIGINT},
      </if>
      <if test="handsPersonId != null">
        hands_person_id = #{handsPersonId,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="changeAmount != null">
        change_amount = #{changeAmount,jdbcType=DECIMAL},
      </if>
      <if test="discountMoney != null">
        discount_money = #{discountMoney,jdbcType=DECIMAL},
      </if>
      <if test="totalPrice != null">
        total_price = #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=BIGINT},
      </if>
      <if test="billNo != null">
        bill_no = #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="billTime != null">
        bill_time = #{billTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jsh.erp.datasource.entities.AccountHead">
    update jsh_account_head
    set type = #{type,jdbcType=VARCHAR},
      organ_id = #{organId,jdbcType=BIGINT},
      hands_person_id = #{handsPersonId,jdbcType=BIGINT},
      creator = #{creator,jdbcType=BIGINT},
      change_amount = #{changeAmount,jdbcType=DECIMAL},
      discount_money = #{discountMoney,jdbcType=DECIMAL},
      total_price = #{totalPrice,jdbcType=DECIMAL},
      account_id = #{accountId,jdbcType=BIGINT},
      bill_no = #{billNo,jdbcType=VARCHAR},
      bill_time = #{billTime,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR},
      file_name = #{fileName,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      source = #{source,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=BIGINT},
      delete_flag = #{deleteFlag,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>