<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.MaterialExtendMapper">
  <resultMap id="BaseResultMap" type="com.jsh.erp.datasource.entities.MaterialExtend">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="material_id" jdbcType="BIGINT" property="materialId" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="commodity_unit" jdbcType="VARCHAR" property="commodityUnit" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="purchase_decimal" jdbcType="DECIMAL" property="purchaseDecimal" />
    <result column="commodity_decimal" jdbcType="DECIMAL" property="commodityDecimal" />
    <result column="wholesale_decimal" jdbcType="DECIMAL" property="wholesaleDecimal" />
    <result column="low_decimal" jdbcType="DECIMAL" property="lowDecimal" />
    <result column="default_flag" jdbcType="VARCHAR" property="defaultFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_serial" jdbcType="VARCHAR" property="createSerial" />
    <result column="update_serial" jdbcType="VARCHAR" property="updateSerial" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="delete_Flag" jdbcType="VARCHAR" property="deleteFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, material_id, bar_code, commodity_unit, sku, purchase_decimal, commodity_decimal, 
    wholesale_decimal, low_decimal, default_flag, create_time, create_serial, update_serial, 
    update_time, tenant_id, delete_Flag
  </sql>
  <select id="selectByExample" parameterType="com.jsh.erp.datasource.entities.MaterialExtendExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from jsh_material_extend
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jsh_material_extend
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from jsh_material_extend
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.jsh.erp.datasource.entities.MaterialExtendExample">
    delete from jsh_material_extend
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.jsh.erp.datasource.entities.MaterialExtend">
    insert into jsh_material_extend (id, material_id, bar_code, 
      commodity_unit, sku, purchase_decimal, 
      commodity_decimal, wholesale_decimal, low_decimal, 
      default_flag, create_time, create_serial, 
      update_serial, update_time, tenant_id, 
      delete_Flag)
    values (#{id,jdbcType=BIGINT}, #{materialId,jdbcType=BIGINT}, #{barCode,jdbcType=VARCHAR}, 
      #{commodityUnit,jdbcType=VARCHAR}, #{sku,jdbcType=VARCHAR}, #{purchaseDecimal,jdbcType=DECIMAL}, 
      #{commodityDecimal,jdbcType=DECIMAL}, #{wholesaleDecimal,jdbcType=DECIMAL}, #{lowDecimal,jdbcType=DECIMAL}, 
      #{defaultFlag,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createSerial,jdbcType=VARCHAR}, 
      #{updateSerial,jdbcType=VARCHAR}, #{updateTime,jdbcType=BIGINT}, #{tenantId,jdbcType=BIGINT}, 
      #{deleteFlag,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.jsh.erp.datasource.entities.MaterialExtend">
    insert into jsh_material_extend
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="materialId != null">
        material_id,
      </if>
      <if test="barCode != null">
        bar_code,
      </if>
      <if test="commodityUnit != null">
        commodity_unit,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="purchaseDecimal != null">
        purchase_decimal,
      </if>
      <if test="commodityDecimal != null">
        commodity_decimal,
      </if>
      <if test="wholesaleDecimal != null">
        wholesale_decimal,
      </if>
      <if test="lowDecimal != null">
        low_decimal,
      </if>
      <if test="defaultFlag != null">
        default_flag,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createSerial != null">
        create_serial,
      </if>
      <if test="updateSerial != null">
        update_serial,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="deleteFlag != null">
        delete_Flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="materialId != null">
        #{materialId,jdbcType=BIGINT},
      </if>
      <if test="barCode != null">
        #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="commodityUnit != null">
        #{commodityUnit,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="purchaseDecimal != null">
        #{purchaseDecimal,jdbcType=DECIMAL},
      </if>
      <if test="commodityDecimal != null">
        #{commodityDecimal,jdbcType=DECIMAL},
      </if>
      <if test="wholesaleDecimal != null">
        #{wholesaleDecimal,jdbcType=DECIMAL},
      </if>
      <if test="lowDecimal != null">
        #{lowDecimal,jdbcType=DECIMAL},
      </if>
      <if test="defaultFlag != null">
        #{defaultFlag,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createSerial != null">
        #{createSerial,jdbcType=VARCHAR},
      </if>
      <if test="updateSerial != null">
        #{updateSerial,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.jsh.erp.datasource.entities.MaterialExtendExample" resultType="java.lang.Long">
    select count(*) from jsh_material_extend
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update jsh_material_extend
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.materialId != null">
        material_id = #{record.materialId,jdbcType=BIGINT},
      </if>
      <if test="record.barCode != null">
        bar_code = #{record.barCode,jdbcType=VARCHAR},
      </if>
      <if test="record.commodityUnit != null">
        commodity_unit = #{record.commodityUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.sku != null">
        sku = #{record.sku,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseDecimal != null">
        purchase_decimal = #{record.purchaseDecimal,jdbcType=DECIMAL},
      </if>
      <if test="record.commodityDecimal != null">
        commodity_decimal = #{record.commodityDecimal,jdbcType=DECIMAL},
      </if>
      <if test="record.wholesaleDecimal != null">
        wholesale_decimal = #{record.wholesaleDecimal,jdbcType=DECIMAL},
      </if>
      <if test="record.lowDecimal != null">
        low_decimal = #{record.lowDecimal,jdbcType=DECIMAL},
      </if>
      <if test="record.defaultFlag != null">
        default_flag = #{record.defaultFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createSerial != null">
        create_serial = #{record.createSerial,jdbcType=VARCHAR},
      </if>
      <if test="record.updateSerial != null">
        update_serial = #{record.updateSerial,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=BIGINT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
      <if test="record.deleteFlag != null">
        delete_Flag = #{record.deleteFlag,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update jsh_material_extend
    set id = #{record.id,jdbcType=BIGINT},
      material_id = #{record.materialId,jdbcType=BIGINT},
      bar_code = #{record.barCode,jdbcType=VARCHAR},
      commodity_unit = #{record.commodityUnit,jdbcType=VARCHAR},
      sku = #{record.sku,jdbcType=VARCHAR},
      purchase_decimal = #{record.purchaseDecimal,jdbcType=DECIMAL},
      commodity_decimal = #{record.commodityDecimal,jdbcType=DECIMAL},
      wholesale_decimal = #{record.wholesaleDecimal,jdbcType=DECIMAL},
      low_decimal = #{record.lowDecimal,jdbcType=DECIMAL},
      default_flag = #{record.defaultFlag,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_serial = #{record.createSerial,jdbcType=VARCHAR},
      update_serial = #{record.updateSerial,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=BIGINT},
      delete_Flag = #{record.deleteFlag,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.jsh.erp.datasource.entities.MaterialExtend">
    update jsh_material_extend
    <set>
      <if test="materialId != null">
        material_id = #{materialId,jdbcType=BIGINT},
      </if>
      <if test="barCode != null">
        bar_code = #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="commodityUnit != null">
        commodity_unit = #{commodityUnit,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="purchaseDecimal != null">
        purchase_decimal = #{purchaseDecimal,jdbcType=DECIMAL},
      </if>
      <if test="commodityDecimal != null">
        commodity_decimal = #{commodityDecimal,jdbcType=DECIMAL},
      </if>
      <if test="wholesaleDecimal != null">
        wholesale_decimal = #{wholesaleDecimal,jdbcType=DECIMAL},
      </if>
      <if test="lowDecimal != null">
        low_decimal = #{lowDecimal,jdbcType=DECIMAL},
      </if>
      <if test="defaultFlag != null">
        default_flag = #{defaultFlag,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createSerial != null">
        create_serial = #{createSerial,jdbcType=VARCHAR},
      </if>
      <if test="updateSerial != null">
        update_serial = #{updateSerial,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        delete_Flag = #{deleteFlag,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jsh.erp.datasource.entities.MaterialExtend">
    update jsh_material_extend
    set material_id = #{materialId,jdbcType=BIGINT},
      bar_code = #{barCode,jdbcType=VARCHAR},
      commodity_unit = #{commodityUnit,jdbcType=VARCHAR},
      sku = #{sku,jdbcType=VARCHAR},
      purchase_decimal = #{purchaseDecimal,jdbcType=DECIMAL},
      commodity_decimal = #{commodityDecimal,jdbcType=DECIMAL},
      wholesale_decimal = #{wholesaleDecimal,jdbcType=DECIMAL},
      low_decimal = #{lowDecimal,jdbcType=DECIMAL},
      default_flag = #{defaultFlag,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_serial = #{createSerial,jdbcType=VARCHAR},
      update_serial = #{updateSerial,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=BIGINT},
      tenant_id = #{tenantId,jdbcType=BIGINT},
      delete_Flag = #{deleteFlag,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>