<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.DepotMapper">
  <resultMap id="BaseResultMap" type="com.jsh.erp.datasource.entities.Depot">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="warehousing" jdbcType="DECIMAL" property="warehousing" />
    <result column="truckage" jdbcType="DECIMAL" property="truckage" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="sort" jdbcType="VARCHAR" property="sort" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="principal" jdbcType="BIGINT" property="principal" />
    <result column="enabled" jdbcType="BIT" property="enabled" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="delete_Flag" jdbcType="VARCHAR" property="deleteFlag" />
    <result column="is_default" jdbcType="BIT" property="isDefault" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, address, warehousing, truckage, type, sort, remark, principal, enabled, 
    tenant_id, delete_Flag, is_default
  </sql>
  <select id="selectByExample" parameterType="com.jsh.erp.datasource.entities.DepotExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from jsh_depot
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jsh_depot
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from jsh_depot
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.jsh.erp.datasource.entities.DepotExample">
    delete from jsh_depot
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.jsh.erp.datasource.entities.Depot">
    insert into jsh_depot (id, name, address, 
      warehousing, truckage, type, 
      sort, remark, principal, 
      enabled, tenant_id, delete_Flag, 
      is_default)
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, 
      #{warehousing,jdbcType=DECIMAL}, #{truckage,jdbcType=DECIMAL}, #{type,jdbcType=INTEGER}, 
      #{sort,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{principal,jdbcType=BIGINT}, 
      #{enabled,jdbcType=BIT}, #{tenantId,jdbcType=BIGINT}, #{deleteFlag,jdbcType=VARCHAR}, 
      #{isDefault,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.jsh.erp.datasource.entities.Depot">
    insert into jsh_depot
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="warehousing != null">
        warehousing,
      </if>
      <if test="truckage != null">
        truckage,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="principal != null">
        principal,
      </if>
      <if test="enabled != null">
        enabled,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="deleteFlag != null">
        delete_Flag,
      </if>
      <if test="isDefault != null">
        is_default,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="warehousing != null">
        #{warehousing,jdbcType=DECIMAL},
      </if>
      <if test="truckage != null">
        #{truckage,jdbcType=DECIMAL},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="principal != null">
        #{principal,jdbcType=BIGINT},
      </if>
      <if test="enabled != null">
        #{enabled,jdbcType=BIT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=VARCHAR},
      </if>
      <if test="isDefault != null">
        #{isDefault,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.jsh.erp.datasource.entities.DepotExample" resultType="java.lang.Long">
    select count(*) from jsh_depot
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update jsh_depot
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.address != null">
        address = #{record.address,jdbcType=VARCHAR},
      </if>
      <if test="record.warehousing != null">
        warehousing = #{record.warehousing,jdbcType=DECIMAL},
      </if>
      <if test="record.truckage != null">
        truckage = #{record.truckage,jdbcType=DECIMAL},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.principal != null">
        principal = #{record.principal,jdbcType=BIGINT},
      </if>
      <if test="record.enabled != null">
        enabled = #{record.enabled,jdbcType=BIT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
      <if test="record.deleteFlag != null">
        delete_Flag = #{record.deleteFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.isDefault != null">
        is_default = #{record.isDefault,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update jsh_depot
    set id = #{record.id,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      address = #{record.address,jdbcType=VARCHAR},
      warehousing = #{record.warehousing,jdbcType=DECIMAL},
      truckage = #{record.truckage,jdbcType=DECIMAL},
      type = #{record.type,jdbcType=INTEGER},
      sort = #{record.sort,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      principal = #{record.principal,jdbcType=BIGINT},
      enabled = #{record.enabled,jdbcType=BIT},
      tenant_id = #{record.tenantId,jdbcType=BIGINT},
      delete_Flag = #{record.deleteFlag,jdbcType=VARCHAR},
      is_default = #{record.isDefault,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.jsh.erp.datasource.entities.Depot">
    update jsh_depot
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="warehousing != null">
        warehousing = #{warehousing,jdbcType=DECIMAL},
      </if>
      <if test="truckage != null">
        truckage = #{truckage,jdbcType=DECIMAL},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="principal != null">
        principal = #{principal,jdbcType=BIGINT},
      </if>
      <if test="enabled != null">
        enabled = #{enabled,jdbcType=BIT},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        delete_Flag = #{deleteFlag,jdbcType=VARCHAR},
      </if>
      <if test="isDefault != null">
        is_default = #{isDefault,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jsh.erp.datasource.entities.Depot">
    update jsh_depot
    set name = #{name,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      warehousing = #{warehousing,jdbcType=DECIMAL},
      truckage = #{truckage,jdbcType=DECIMAL},
      type = #{type,jdbcType=INTEGER},
      sort = #{sort,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      principal = #{principal,jdbcType=BIGINT},
      enabled = #{enabled,jdbcType=BIT},
      tenant_id = #{tenantId,jdbcType=BIGINT},
      delete_Flag = #{deleteFlag,jdbcType=VARCHAR},
      is_default = #{isDefault,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>