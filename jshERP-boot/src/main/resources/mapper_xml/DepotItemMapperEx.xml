<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper  namespace="com.jsh.erp.datasource.mappers.DepotItemMapperEx">

    <resultMap id="DetailByTypeAndMIdResultMap" type="com.jsh.erp.datasource.entities.DepotItemVo4DetailByTypeAndMId">
        <result column="number" jdbcType="VARCHAR" property="number" />
        <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
        <result column="material_name" jdbcType="VARCHAR" property="materialName" />
        <result column="type" jdbcType="VARCHAR" property="type" />
        <result column="sub_type" jdbcType="VARCHAR" property="subType" />
        <result column="b_num" jdbcType="DECIMAL" property="bnum" />
        <result column="unit_price" jdbcType="DECIMAL" property="unitPrice" />
        <result column="all_price" jdbcType="DECIMAL" property="allPrice" />
        <result column="material_unit" jdbcType="VARCHAR" property="materialUnit" />
        <result column="depotName" jdbcType="VARCHAR" property="depotName" />
        <result column="oTime" jdbcType="TIMESTAMP" property="otime" />
    </resultMap>

    <resultMap extends="com.jsh.erp.datasource.mappers.DepotItemMapper.BaseResultMap" id="ResultAndMaterialMap" type="com.jsh.erp.datasource.entities.DepotItemVo4Material">
        <result column="mName" jdbcType="VARCHAR" property="mname" />
        <result column="mModel" jdbcType="VARCHAR" property="mmodel" />
    </resultMap>

    <resultMap extends="com.jsh.erp.datasource.mappers.DepotItemMapper.BaseResultMap" id="ResultWithInfoExMap" type="com.jsh.erp.datasource.entities.DepotItemVo4WithInfoEx">
        <result column="MName" jdbcType="VARCHAR" property="MName" />
        <result column="MModel" jdbcType="VARCHAR" property="MModel" />
        <result column="MaterialUnit" jdbcType="VARCHAR" property="MaterialUnit" />
        <result column="MColor" jdbcType="VARCHAR" property="MColor" />
        <result column="MStandard" jdbcType="VARCHAR" property="MStandard" />
        <result column="MMfrs" jdbcType="VARCHAR" property="MMfrs" />
        <result column="weight" jdbcType="VARCHAR" property="weight" />
        <result column="position" jdbcType="VARCHAR" property="position" />
        <result column="img_name" jdbcType="VARCHAR" property="imgName" />
        <result column="MOtherField1" jdbcType="VARCHAR" property="MOtherField1" />
        <result column="MOtherField2" jdbcType="VARCHAR" property="MOtherField2" />
        <result column="MOtherField3" jdbcType="VARCHAR" property="MOtherField3" />
        <result column="enable_serial_number" jdbcType="VARCHAR" property="enableSerialNumber" />
        <result column="enable_batch_number" jdbcType="VARCHAR" property="enableBatchNumber" />
        <result column="brand" jdbcType="VARCHAR" property="brand" />
        <result column="DepotName" jdbcType="VARCHAR" property="DepotName" />
        <result column="AnotherDepotName" jdbcType="VARCHAR" property="AnotherDepotName" />
        <result column="barCode" jdbcType="VARCHAR" property="barCode" />
        <result column="purchase_decimal" jdbcType="DECIMAL" property="purchaseDecimal" />
    </resultMap>

    <resultMap extends="com.jsh.erp.datasource.mappers.DepotItemMapper.BaseResultMap" id="ResultByMaterial" type="com.jsh.erp.datasource.entities.DepotItemVo4WithInfoEx">
        <result column="MId" jdbcType="VARCHAR" property="MId" />
        <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
        <result column="MName" jdbcType="VARCHAR" property="MName" />
        <result column="MMfrs" jdbcType="VARCHAR" property="MMfrs" />
        <result column="MModel" jdbcType="VARCHAR" property="MModel" />
        <result column="MStandard" jdbcType="VARCHAR" property="MStandard" />
        <result column="MaterialUnit" jdbcType="VARCHAR" property="MaterialUnit" />
        <result column="MOtherField1" jdbcType="VARCHAR" property="MOtherField1" />
        <result column="MOtherField2" jdbcType="VARCHAR" property="MOtherField2" />
        <result column="MOtherField3" jdbcType="VARCHAR" property="MOtherField3" />
        <result column="unit_id" jdbcType="BIGINT" property="unitId" />
        <result column="unit_name" jdbcType="VARCHAR" property="unitName" />
        <result column="MColor" jdbcType="VARCHAR" property="MColor" />
        <result column="brand" jdbcType="VARCHAR" property="brand" />
        <result column="purchase_decimal" jdbcType="DECIMAL" property="purchaseDecimal" />
        <result column="currentUnitPrice" jdbcType="DECIMAL" property="currentUnitPrice" />
    </resultMap>

    <resultMap id="ResultStockWarningCount" type="com.jsh.erp.datasource.vo.DepotItemStockWarningCount">
        <result column="MId" jdbcType="VARCHAR" property="MId" />
        <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
        <result column="MName" jdbcType="VARCHAR" property="MName" />
        <result column="MMfrs" jdbcType="VARCHAR" property="MMfrs" />
        <result column="MModel" jdbcType="VARCHAR" property="MModel" />
        <result column="MStandard" jdbcType="VARCHAR" property="MStandard" />
        <result column="MColor" jdbcType="VARCHAR" property="MColor" />
        <result column="brand" jdbcType="VARCHAR" property="brand" />
        <result column="MaterialUnit" jdbcType="VARCHAR" property="MaterialUnit" />
        <result column="MOtherField1" jdbcType="VARCHAR" property="MOtherField1" />
        <result column="MOtherField2" jdbcType="VARCHAR" property="MOtherField2" />
        <result column="MOtherField3" jdbcType="VARCHAR" property="MOtherField3" />
        <result column="unit_name" jdbcType="VARCHAR" property="unitName" />
        <result column="current_number" jdbcType="DECIMAL" property="currentNumber" />
        <result column="low_safe_stock" jdbcType="DECIMAL" property="lowSafeStock" />
        <result column="high_safe_stock" jdbcType="DECIMAL" property="highSafeStock" />
    </resultMap>

    <resultMap id="stockMap" type="com.jsh.erp.datasource.vo.DepotItemVo4Stock">
        <result column="inTotal" jdbcType="DECIMAL" property="inTotal" />
        <result column="outTotal" jdbcType="DECIMAL" property="outTotal" />
        <result column="transfInTotal" jdbcType="DECIMAL" property="transfInTotal" />
        <result column="transfOutTotal" jdbcType="DECIMAL" property="transfOutTotal" />
        <result column="assemInTotal" jdbcType="DECIMAL" property="assemInTotal" />
        <result column="assemOutTotal" jdbcType="DECIMAL" property="assemOutTotal" />
        <result column="disAssemInTotal" jdbcType="DECIMAL" property="disAssemInTotal" />
        <result column="disAssemOutTotal" jdbcType="DECIMAL" property="disAssemOutTotal" />
    </resultMap>

    <resultMap id="batchNumberListMap" type="com.jsh.erp.datasource.vo.DepotItemVoBatchNumberList">
        <result column="id" jdbcType="VARCHAR" property="id" />
        <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="standard" jdbcType="VARCHAR" property="standard" />
        <result column="model" jdbcType="VARCHAR" property="model" />
        <result column="unit_id" jdbcType="BIGINT" property="unitId" />
        <result column="commodity_unit" jdbcType="VARCHAR" property="commodityUnit" />
        <result column="batch_number" jdbcType="VARCHAR" property="batchNumber" />
        <result column="expiration_date" jdbcType="TIMESTAMP" property="expirationDate" />
        <result column="total_num" jdbcType="VARCHAR" property="totalNum" />
    </resultMap>

    <resultMap id="materialSumMap" type="com.jsh.erp.datasource.entities.DepotItemVo4MaterialAndSum">
        <result column="material_extend_id" jdbcType="VARCHAR" property="materialExtendId" />
        <result column="oper_number" jdbcType="VARCHAR" property="operNumber" />
    </resultMap>

    <select id="selectByConditionDepotItem" parameterType="com.jsh.erp.datasource.entities.DepotItemExample" resultMap="com.jsh.erp.datasource.mappers.DepotItemMapper.BaseResultMap">
        select *
        FROM jsh_depot_item
        where 1=1
        <if test="name != null">
            <bind name="bindName" value="'%'+name+'%'"/>
            and name like #{bindName}
        </if>
        <if test="type != null">
            and type = #{type}
        </if>
        <if test="remark != null">
            <bind name="bindRemark" value="'%'+remark+'%'"/>
            and remark like #{bindRemark}
        </if>
        and ifnull(delete_flag,'0') !='1'
        <if test="offset != null and rows != null">
            limit #{offset},#{rows}
        </if>
    </select>

    <select id="countsByDepotItem" resultType="java.lang.Long">
        SELECT
        COUNT(id)
        FROM jsh_depot_item
        WHERE 1=1
        <if test="name != null">
            <bind name="bindName" value="'%'+name+'%'"/>
            and name like #{bindName}
        </if>
        <if test="type != null">
            and type = #{type}
        </if>
        <if test="remark != null">
            <bind name="bindRemark" value="'%'+remark+'%'"/>
            and remark like #{bindRemark}
        </if>
        and ifnull(delete_flag,'0') !='1'
    </select>

    <select id="findDetailByDepotIdsAndMaterialIdList" parameterType="com.jsh.erp.datasource.entities.DepotItemExample" resultMap="DetailByTypeAndMIdResultMap">
        select tb.number, tb.bar_code, tb.material_name, tb.type, tb.sub_type, tb.b_num, tb.unit_price,
        ifnull(tb.b_num*tb.unit_price,0) as all_price, tb.material_unit, tb.depotName, tb.oTime from
        (select dh.number,me.bar_code,m.name material_name,dh.type,dh.sub_type,
        case
            when type='入库' then ifnull(di.basic_number,0)
            when type='出库' then 0-ifnull(di.basic_number,0)
            when dh.sub_type='组装单' and di.material_type='组合件' then ifnull(di.basic_number,0)
            when dh.sub_type='组装单' and di.material_type='普通子件' then 0-ifnull(di.basic_number,0)
            when dh.sub_type='拆卸单' and di.material_type='普通子件' then ifnull(di.basic_number,0)
            when dh.sub_type='拆卸单' and di.material_type='组合件' then 0-ifnull(di.basic_number,0)
            when dh.sub_type='盘点复盘' then ifnull(di.basic_number,0)
            else 0
        end
        as b_num, di.unit_price, di.material_unit,
        (select name from jsh_depot d where d.id=di.depot_id and ifnull(d.delete_flag,'0') !='1') as depotName,
        date_format(dh.oper_time,'%Y-%m-%d %H:%i:%S') as oTime
        from jsh_depot_head dh
        left join jsh_depot_item di on dh.id=di.header_id and ifnull(di.delete_flag,'0') !='1'
        left join jsh_material m on di.material_id=m.id and ifnull(m.delete_flag,'0') !='1'
        left join jsh_material_extend me on me.id=di.material_extend_id and ifnull(me.delete_Flag,'0') !='1'
        where (dh.type!='其它'
        or (dh.type='其它' and dh.sub_type='组装单')
        or (dh.type='其它' and dh.sub_type='拆卸单')
        or (dh.type='其它' and dh.sub_type='盘点复盘'))
        <if test="inOutManageFlag">
            and (dh.sub_type!='采购' and dh.sub_type!='采购退货' and dh.sub_type!='销售' and dh.sub_type!='销售退货')
        </if>
        <if test="depotIdArray != null and depotIdArray.length>0">
            and di.depot_id in (
            <foreach collection="depotIdArray" item="depotId" separator=",">#{depotId}</foreach>
            )
        </if>
        <if test="forceFlag">
            and dh.status = '1'
        </if>
        <if test="sku != null and sku !=''">
            and di.sku = #{sku}
        </if>
        <if test="batchNumber != null and batchNumber !=''">
            and di.batch_number = #{batchNumber}
        </if>
        <if test="number != null and number !=''">
            <bind name="bindNumber" value="'%'+number+'%'"/>
            and dh.number like #{bindNumber}
        </if>
        <if test="beginTime != null and beginTime !=''">
            and dh.oper_time >= #{beginTime}
        </if>
        <if test="endTime != null and endTime !=''">
            and dh.oper_time &lt;= #{endTime}
        </if>
        and di.material_id = #{mId}
        and ifnull(dh.delete_flag,'0') !='1'
        union all
        --单独构造记录：调拨入库
        select dh.number,me.bar_code,m.name material_name,dh.type,dh.sub_type,
        ifnull(di.basic_number,0) as b_num, di.unit_price, di.material_unit,
        (select concat(name,'[调入]') from jsh_depot d where d.id=di.another_depot_id and ifnull(d.delete_flag,'0') !='1') as depotName,
        date_format(dh.oper_time,'%Y-%m-%d %H:%i:%S') as oTime
        from jsh_depot_head dh
        left join jsh_depot_item di on dh.id=di.header_id and ifnull(di.delete_flag,'0') !='1'
        left join jsh_material m on di.material_id=m.id and ifnull(m.delete_flag,'0') !='1'
        left join jsh_material_extend me on me.id=di.material_extend_id and ifnull(me.delete_Flag,'0') !='1'
        where dh.type='出库' and dh.sub_type='调拨'
        <if test="depotIdArray != null and depotIdArray.length>0">
            and di.another_depot_id in (
            <foreach collection="depotIdArray" item="depotId" separator=",">#{depotId}</foreach>
            )
        </if>
        <if test="forceFlag">
            and dh.status = '1'
        </if>
        <if test="sku != null and sku !=''">
            and di.sku = #{sku}
        </if>
        <if test="batchNumber != null and batchNumber !=''">
            and di.batch_number = #{batchNumber}
        </if>
        <if test="number != null and number !=''">
            <bind name="bindNumber" value="'%'+number+'%'"/>
            and dh.number like #{bindNumber}
        </if>
        <if test="beginTime != null and beginTime !=''">
            and dh.oper_time >= #{beginTime}
        </if>
        <if test="endTime != null and endTime !=''">
            and dh.oper_time &lt;= #{endTime}
        </if>
        and di.material_id = #{mId}
        and ifnull(dh.delete_flag,'0') !='1') tb
        order by tb.oTime desc
        <if test="offset != null and rows != null">
            limit #{offset},#{rows}
        </if>
    </select>

    <select id="findDetailByDepotIdsAndMaterialIdCount" resultType="java.lang.Long">
        select count(tb.number) from
        (select dh.number from jsh_depot_head dh
        left JOIN jsh_depot_item di on dh.id=di.header_id and ifnull(di.delete_flag,'0') !='1'
        where (dh.type!='其它'
        or (dh.type='其它' and dh.sub_type='组装单')
        or (dh.type='其它' and dh.sub_type='拆卸单')
        or (dh.type='其它' and dh.sub_type='盘点复盘'))
        <if test="inOutManageFlag">
            and (dh.sub_type!='采购' and dh.sub_type!='采购退货' and dh.sub_type!='销售' and dh.sub_type!='销售退货')
        </if>
        <if test="depotIdArray != null and depotIdArray.length>0">
            and di.depot_id in (
            <foreach collection="depotIdArray" item="depotId" separator=",">#{depotId}</foreach>
            )
        </if>
        <if test="forceFlag">
            and dh.status = '1'
        </if>
        <if test="sku != null and sku !=''">
            and di.sku = #{sku}
        </if>
        <if test="batchNumber != null and batchNumber !=''">
            and di.batch_number = #{batchNumber}
        </if>
        <if test="number != null and number !=''">
            <bind name="bindNumber" value="'%'+number+'%'"/>
            and dh.number like #{bindNumber}
        </if>
        <if test="beginTime != null and beginTime !=''">
            and dh.oper_time >= #{beginTime}
        </if>
        <if test="endTime != null and endTime !=''">
            and dh.oper_time &lt;= #{endTime}
        </if>
        and di.material_id =#{mId}
        and ifnull(dh.delete_flag,'0') !='1'
        union all
        --单独构造记录：调拨入库
        select dh.number from jsh_depot_head dh
        left join jsh_depot_item di on dh.id=di.header_id and ifnull(di.delete_flag,'0') !='1'
        where dh.type='出库' and dh.sub_type='调拨'
        <if test="depotIdArray != null and depotIdArray.length>0">
            and di.another_depot_id in (
            <foreach collection="depotIdArray" item="depotId" separator=",">#{depotId}</foreach>
            )
        </if>
        <if test="forceFlag">
            and dh.status = '1'
        </if>
        <if test="sku != null and sku !=''">
            and di.sku = #{sku}
        </if>
        <if test="batchNumber != null and batchNumber !=''">
            and di.batch_number = #{batchNumber}
        </if>
        <if test="number != null and number !=''">
            <bind name="bindNumber" value="'%'+number+'%'"/>
            and dh.number like #{bindNumber}
        </if>
        <if test="beginTime != null and beginTime !=''">
            and dh.oper_time >= #{beginTime}
        </if>
        <if test="endTime != null and endTime !=''">
            and dh.oper_time &lt;= #{endTime}
        </if>
        and di.material_id = #{mId}
        and ifnull(dh.delete_flag,'0') !='1') tb
    </select>

    <select id="getDetailList" parameterType="com.jsh.erp.datasource.entities.DepotItemExample" resultMap="ResultWithInfoExMap">
        select di.*,m.name MName,m.model MModel,m.unit MaterialUnit,m.color MColor,m.standard MStandard,m.mfrs MMfrs,m.weight, m.position, m.img_name,
        m.other_field1 MOtherField1,m.other_field2 MOtherField2,m.other_field3 MOtherField3,m.enable_serial_number, m.enable_batch_number,
        m.brand, dp1.name DepotName,dp2.name AnotherDepotName, me.bar_code barCode, me.purchase_decimal
        from jsh_depot_item di
        left join jsh_material m on di.material_id=m.id  and ifnull(m.delete_flag,'0') !='1'
        left join jsh_material_extend me on me.id=di.material_extend_id  and ifnull(me.delete_Flag,'0') !='1'
        left join jsh_depot dp1 on di.depot_id=dp1.id and ifnull(dp1.delete_Flag,'0') !='1'
        left join jsh_depot dp2 on di.another_depot_id=dp2.id and ifnull(dp2.delete_Flag,'0') !='1'
        where di.header_id = #{headerId}
        and ifnull(di.delete_flag,'0') !='1'
        order by di.id asc
    </select>

    <select id="getBillDetailListByIds" resultType="com.jsh.erp.datasource.entities.DepotItemVo4WithInfoEx">
        select di.*,m.name MName,m.model MModel,m.unit MaterialUnit,m.color MColor,m.standard MStandard,m.mfrs MMfrs,m.weight, m.position, m.img_name,
        m.other_field1 MOtherField1,m.other_field2 MOtherField2,m.other_field3 MOtherField3,m.enable_serial_number, m.enable_batch_number,
        m.brand, dp1.name DepotName,dp2.name AnotherDepotName, me.bar_code barCode, me.purchase_decimal
        from jsh_depot_item di
        left join jsh_material m on di.material_id=m.id  and ifnull(m.delete_flag,'0') !='1'
        left join jsh_material_extend me on me.id=di.material_extend_id  and ifnull(me.delete_Flag,'0') !='1'
        left join jsh_depot dp1 on di.depot_id=dp1.id and ifnull(dp1.delete_Flag,'0') !='1'
        left join jsh_depot dp2 on di.another_depot_id=dp2.id and ifnull(dp2.delete_Flag,'0') !='1'
        where ifnull(di.delete_flag,'0') !='1'
        and di.header_id in
        <foreach collection="idList" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
        order by di.header_id desc, di.id asc
    </select>

    <select id="getInOutStock" parameterType="com.jsh.erp.datasource.entities.DepotItemExample" resultMap="ResultByMaterial">
        select m.id MId, me.bar_code, m.name MName, m.mfrs MMfrs, m.model MModel, m.standard MStandard, m.brand,
        m.other_field1 MOtherField1,m.other_field2 MOtherField2,m.other_field3 MOtherField3, m.img_name,
        concat_ws('', m.unit, u.basic_unit) MaterialUnit, m.color MColor, m.unit_id, u.name unit_name,
        ifnull(me.purchase_decimal,0) purchase_decimal, ifnull(mcs.current_unit_price,0) currentUnitPrice
        from jsh_material m
        left join jsh_material_extend me on me.material_id=m.id and ifnull(me.delete_Flag,'0') !='1'
        left join jsh_material_current_stock mcs on m.id = mcs.material_id and ifnull(mcs.delete_flag,'0') !='1'
        left join jsh_depot_item di on di.material_id=m.id and ifnull(di.delete_Flag,'0') !='1'
        left join jsh_depot_head dh on di.header_id=dh.id and ifnull(dh.delete_flag,'0') !='1'
        left join jsh_unit u on m.unit_id=u.id and ifnull(u.delete_Flag,'0') !='1'
        where 1=1 and me.default_flag=1
        <if test="materialParam != null and materialParam !=''">
            <bind name="bindKey" value="'%'+materialParam+'%'"/>
            and (me.bar_code like #{bindKey} or m.name like #{bindKey} or m.mnemonic like #{bindKey} or m.standard like #{bindKey} or m.model like #{bindKey}
            or m.color like #{bindKey} or m.mfrs like #{bindKey} or m.brand like #{bindKey} or m.other_field1 like #{bindKey}
            or m.other_field2 like #{bindKey} or m.other_field3 like #{bindKey})
        </if>
        <if test="categoryIdList.size()>0">
            and m.category_id in
            <foreach collection="categoryIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="endTime != null">
            and dh.oper_time &lt;= #{endTime}
        </if>
        and ifnull(m.delete_flag,'0') !='1'
        group by m.id, me.bar_code, m.name, m.mfrs, m.model, m.standard, m.brand,
        m.other_field1,m.other_field2,m.other_field3, m.img_name ,m.unit, u.basic_unit, m.color, m.unit_id, u.name,
        me.purchase_decimal, mcs.current_unit_price
        order by m.id desc
        <if test="offset != null and rows != null">
            limit #{offset},#{rows}
        </if>
    </select>

    <select id="getInOutStockCount" resultType="java.lang.Integer">
        select count(1) from (select m.id
        from jsh_material m
        left join jsh_material_extend me on me.material_id=m.id and ifnull(me.delete_Flag,'0') !='1'
        left join jsh_depot_item di on di.material_id=m.id and ifnull(di.delete_Flag,'0') !='1'
        left join jsh_depot_head dh on di.header_id=dh.id and ifnull(dh.delete_flag,'0') !='1'
        where 1=1 and me.default_flag=1
        <if test="materialParam != null and materialParam !=''">
            <bind name="bindKey" value="'%'+materialParam+'%'"/>
            and (me.bar_code like #{bindKey} or m.name like #{bindKey} or m.mnemonic like #{bindKey} or m.standard like #{bindKey} or m.model like #{bindKey}
            or m.color like #{bindKey} or m.mfrs like #{bindKey} or m.brand like #{bindKey} or m.other_field1 like #{bindKey}
            or m.other_field2 like #{bindKey} or m.other_field3 like #{bindKey})
        </if>
        <if test="categoryIdList.size()>0">
            and m.category_id in
            <foreach collection="categoryIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="endTime != null">
            and dh.oper_time &lt;= #{endTime}
        </if>
        and ifnull(m.delete_flag,'0') !='1'
        group by m.id) cc
    </select>

    <select id="getListWithBuyOrSale" parameterType="com.jsh.erp.datasource.entities.DepotItemExample" resultMap="ResultByMaterial">
        select m.id MId, di.material_extend_id, me.bar_code, m.name MName, m.mfrs MMfrs, m.model MModel, m.standard MStandard,
        m.other_field1 MOtherField1,m.other_field2 MOtherField2,m.other_field3 MOtherField3,
        concat_ws('', m.unit, u.basic_unit) MaterialUnit, m.color MColor, m.brand, u.name unit_name
        from jsh_material m
        left join jsh_depot_item di on di.material_id=m.id  and ifnull(di.delete_Flag,'0') !='1'
        left join jsh_material_extend me on me.id=di.material_extend_id  and ifnull(me.delete_Flag,'0') !='1'
        left join jsh_depot_head dh on di.header_id=dh.id  and ifnull(dh.delete_flag,'0') !='1'
        left join jsh_unit u on m.unit_id=u.id and ifnull(u.delete_Flag,'0') !='1'
        where 1=1
        <if test="categoryList.size()>0">
            and m.category_id in
            <foreach collection="categoryList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="materialParam != null and materialParam !=''">
            <bind name="bindKey" value="'%'+materialParam+'%'"/>
            and (me.bar_code like #{bindKey} or m.name like #{bindKey} or m.mnemonic like #{bindKey} or m.standard like #{bindKey} or m.model like #{bindKey}
            or m.color like #{bindKey} or m.mfrs like #{bindKey} or m.brand like #{bindKey} or m.other_field1 like #{bindKey}
            or m.other_field2 like #{bindKey} or m.other_field3 like #{bindKey})
        </if>
        <if test="billType =='buy'">
            and (dh.sub_type = '采购' or dh.sub_type = '采购退货')
        </if>
        <if test="billType =='sale'">
            and (dh.sub_type = '销售' or dh.sub_type = '销售退货')
        </if>
        <if test="billType =='retail'">
            and (dh.sub_type = '零售' or dh.sub_type = '零售退货')
        </if>
        <if test="beginTime != null">
            and dh.oper_time >= #{beginTime}
        </if>
        <if test="endTime != null">
            and dh.oper_time &lt;= #{endTime}
        </if>
        <if test="creatorArray != null">
            and dh.creator in (
            <foreach collection="creatorArray" item="creator" separator=",">
                #{creator}
            </foreach>
            )
        </if>
        <if test="organId != null">
            and dh.organ_id = #{organId}
        </if>
        <if test="organArray != null and organArray !=''">
            and dh.organ_id in (
            <foreach collection="organArray" item="organId" separator=",">
                #{organId}
            </foreach>
            )
        </if>
        <if test="depotList.size()>0">
            and di.depot_id in
            <foreach collection="depotList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="forceFlag">
            and (dh.status = '1' or dh.status = '2' or dh.status = '3')
        </if>
        and ifnull(m.delete_flag,'0') !='1'
        group by m.id, di.material_extend_id, me.bar_code, m.name, m.mfrs, m.model, m.standard,
        m.other_field1,m.other_field2,m.other_field3, m.unit, u.basic_unit, m.color, m.brand, u.name
        order by m.id desc
        <if test="offset != null and rows != null">
            limit #{offset},#{rows}
        </if>
    </select>

    <select id="getListWithBuyOrSaleCount" resultType="java.lang.Integer">
        select count(1) from (select m.id, di.material_extend_id
        from jsh_material m
        left join jsh_depot_item di on di.material_id=m.id  and ifnull(m.delete_Flag,'0') !='1'
        left join jsh_material_extend me on me.id=di.material_extend_id  and ifnull(me.delete_Flag,'0') !='1'
        left join jsh_depot_head dh on di.header_id=dh.id  and ifnull(dh.delete_flag,'0') !='1'
        where 1=1
        <if test="categoryList.size()>0">
            and m.category_id in
            <foreach collection="categoryList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="materialParam != null and materialParam !=''">
            <bind name="bindKey" value="'%'+materialParam+'%'"/>
            and (me.bar_code like #{bindKey} or m.name like #{bindKey} or m.mnemonic like #{bindKey} or m.standard like #{bindKey} or m.model like #{bindKey}
            or m.color like #{bindKey} or m.mfrs like #{bindKey} or m.brand like #{bindKey} or m.other_field1 like #{bindKey}
            or m.other_field2 like #{bindKey} or m.other_field3 like #{bindKey})
        </if>
        <if test="billType =='buy'">
            and (dh.sub_type = '采购' or dh.sub_type = '采购退货')
        </if>
        <if test="billType =='sale'">
            and (dh.sub_type = '销售' or dh.sub_type = '销售退货')
        </if>
        <if test="billType =='retail'">
            and (dh.sub_type = '零售' or dh.sub_type = '零售退货')
        </if>
        <if test="beginTime != null">
            and dh.oper_time >= #{beginTime}
        </if>
        <if test="endTime != null">
            and dh.oper_time &lt;= #{endTime}
        </if>
        <if test="creatorArray != null">
            and dh.creator in (
            <foreach collection="creatorArray" item="creator" separator=",">
                #{creator}
            </foreach>
            )
        </if>
        <if test="organId != null">
            and dh.organ_id = #{organId}
        </if>
        <if test="organArray != null and organArray !=''">
            and dh.organ_id in (
            <foreach collection="organArray" item="organId" separator=",">
                #{organId}
            </foreach>
            )
        </if>
        <if test="depotList.size()>0">
            and di.depot_id in
            <foreach collection="depotList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="forceFlag">
            and (dh.status = '1' or dh.status = '2' or dh.status = '3')
        </if>
        and ifnull(di.delete_flag,'0') !='1'
        group by m.id, di.material_extend_id) cc
    </select>

    <select id="buyOrSaleNumber" resultType="java.math.BigDecimal">
        select ifnull(sum(basic_number),0) as BasicNumber from jsh_depot_item di,jsh_depot_head dh
        where di.header_id = dh.id
        and dh.type=#{type} and dh.sub_type=#{subType}
        and dh.oper_time &gt;= #{beginTime}
        and dh.oper_time &lt;= #{endTime}
        and di.material_extend_id =#{meId}
        <if test="creatorArray != null">
            and dh.creator in (
            <foreach collection="creatorArray" item="creator" separator=",">
                #{creator}
            </foreach>
            )
        </if>
        <if test="organId != null">
            and dh.organ_id = #{organId}
        </if>
        <if test="organArray != null and organArray !=''">
            and dh.organ_id in (
            <foreach collection="organArray" item="organId" separator=",">
                #{organId}
            </foreach>
            )
        </if>
        <if test="depotList.size()>0">
            and di.depot_id in
            <foreach collection="depotList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="forceFlag">
            and (dh.status = '1' or dh.status = '2' or dh.status = '3')
        </if>
        and ifnull(dh.delete_flag,'0') !='1'
        and ifnull(di.delete_flag,'0') !='1'
    </select>

    <select id="buyOrSalePrice" resultType="java.math.BigDecimal">
        select ifnull(sum(all_price),0) as AllPrice from jsh_depot_item di,jsh_depot_head dh
        where di.header_id = dh.id
        and dh.type=#{type} and dh.sub_type=#{subType}
        and dh.oper_time &gt;= #{beginTime}
        and dh.oper_time &lt;= #{endTime}
        <if test="meId != null">
            and di.material_extend_id =#{meId}
        </if>
        <if test="creatorArray != null">
            and dh.creator in (
            <foreach collection="creatorArray" item="creator" separator=",">
                #{creator}
            </foreach>
            )
        </if>
        <if test="organId != null">
            and dh.organ_id = #{organId}
        </if>
        <if test="organArray != null and organArray !=''">
            and dh.organ_id in (
            <foreach collection="organArray" item="organId" separator=",">
                #{organId}
            </foreach>
            )
        </if>
        <if test="depotList.size()>0">
            and di.depot_id in
            <foreach collection="depotList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="forceFlag">
            and (dh.status = '1' or dh.status = '2' or dh.status = '3')
        </if>
        and ifnull(dh.delete_flag,'0') !='1'
        and ifnull(di.delete_flag,'0') !='1'
    </select>

    <select id="buyOrSalePriceTotal" resultType="java.math.BigDecimal">
        select ifnull(sum(di.all_price),0) as AllPrice
        from jsh_depot_item di
        left join jsh_depot_head dh on dh.id=di.header_id and ifnull(dh.delete_flag,'0') !='1'
        left join jsh_material_extend me on me.id=di.material_extend_id  and ifnull(me.delete_Flag,'0') !='1'
        left join jsh_material m on m.id=di.material_id and ifnull(m.delete_Flag,'0') !='1'
        where ifnull(di.delete_flag,'0') !='1'
        and dh.type=#{type} and dh.sub_type=#{subType}
        and dh.oper_time &gt;= #{beginTime}
        and dh.oper_time &lt;= #{endTime}
        <if test="categoryList.size()>0">
            and m.category_id in
            <foreach collection="categoryList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="materialParam != null and materialParam !=''">
            <bind name="bindKey" value="'%'+materialParam+'%'"/>
            and (me.bar_code like #{bindKey} or m.name like #{bindKey} or m.mnemonic like #{bindKey} or m.standard like #{bindKey} or m.model like #{bindKey})
        </if>
        <if test="creatorArray != null">
            and dh.creator in (
            <foreach collection="creatorArray" item="creator" separator=",">
                #{creator}
            </foreach>
            )
        </if>
        <if test="organId != null">
            and dh.organ_id = #{organId}
        </if>
        <if test="organArray != null and organArray !=''">
            and dh.organ_id in (
            <foreach collection="organArray" item="organId" separator=",">
                #{organId}
            </foreach>
            )
        </if>
        <if test="depotList.size()>0">
            and di.depot_id in
            <foreach collection="depotList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="forceFlag">
            and (dh.status = '1' or dh.status = '2' or dh.status = '3')
        </if>
    </select>

    <select id="inOrOutPriceList" resultType="com.jsh.erp.datasource.vo.InOutPriceVo">
        select distinct dh.id, dh.discount_last_money, dh.total_price, dh.type, dh.sub_type, dh.oper_time
        from jsh_depot_head dh
        left join jsh_depot_item di on dh.id = di.header_id and ifnull(di.delete_flag,'0') != '1'
        where di.id is not null
        and (dh.type='入库' or dh.type='出库')
        and dh.oper_time &gt;= #{beginTime}
        and dh.oper_time &lt;= #{endTime}
        <if test="forceFlag">
            and (dh.status = '1' or dh.status = '2' or dh.status = '3')
        </if>
        <if test="creatorArray != null">
            and dh.creator in (
            <foreach collection="creatorArray" item="creator" separator=",">
                #{creator}
            </foreach>
            )
        </if>
        and ifnull(dh.delete_flag,'0') != '1'
    </select>

    <select id="getSkuStockCheckSumByDepotList"  resultType="java.math.BigDecimal">
        select ifnull(sum(di.basic_number),0) stockCheckSum from jsh_depot_head dh
        left JOIN jsh_depot_item di on dh.id=di.header_id
        where 1=1
        <if test="meId != null">
            and di.material_extend_id=#{meId}
        </if>
        and dh.sub_type='盘点复盘'
        <if test="depotList.size()>0">
            and di.depot_id in
            <foreach collection="depotList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="forceFlag">
            and dh.status = '1'
        </if>
        <if test="beginTime != null">
            and dh.oper_time &gt;= #{beginTime}
        </if>
        <if test="endTime != null">
            and dh.oper_time &lt;= #{endTime}
        </if>
        and ifnull(dh.delete_flag,'0') !='1'
        and ifnull(di.delete_flag,'0') !='1'
    </select>

    <select id="getStockCheckSumByDepotList"  resultType="java.math.BigDecimal">
        select ifnull(sum(di.basic_number),0) stockCheckSum from jsh_depot_head dh
        left JOIN jsh_depot_item di on dh.id=di.header_id
        where 1=1
        <if test="mId != null">
            and di.material_id=#{mId}
        </if>
        and dh.sub_type='盘点复盘'
        <if test="depotList.size()>0">
            and di.depot_id in
            <foreach collection="depotList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="forceFlag">
            and dh.status = '1'
        </if>
        <if test="beginTime != null">
            and dh.oper_time &gt;= #{beginTime}
        </if>
        <if test="endTime != null">
            and dh.oper_time &lt;= #{endTime}
        </if>
        and ifnull(dh.delete_flag,'0') !='1'
        and ifnull(di.delete_flag,'0') !='1'
    </select>

    <select id="getSkuStockByParamWithDepotList" resultMap="stockMap">
        select
        sum(case when dh.type='入库' <include refid="inOutManageParam" /> <include refid="depotParam" /> then di.basic_number else 0 end) as inTotal,
        sum(case when dh.type='出库' and dh.sub_type!='调拨' <include refid="inOutManageParam" /> <include refid="depotParam" /> then di.basic_number else 0 end) as outTotal,
        sum(case when dh.sub_type='调拨' <include refid="anotherDepotParam" /> then di.basic_number else 0 end) as transfInTotal,
        sum(case when dh.sub_type='调拨' <include refid="depotParam" /> then di.basic_number else 0 end) as transfOutTotal,
        sum(case when dh.sub_type='组装单' and di.material_type='组合件' <include refid="depotParam" /> then di.basic_number else 0 end) as assemInTotal,
        sum(case when dh.sub_type='组装单' and di.material_type='普通子件' <include refid="depotParam" /> then di.basic_number else 0 end) as assemOutTotal,
        sum(case when dh.sub_type='拆卸单' and di.material_type='普通子件' <include refid="depotParam" /> then di.basic_number else 0 end) as disAssemInTotal,
        sum(case when dh.sub_type='拆卸单' and di.material_type='组合件' <include refid="depotParam" /> then di.basic_number else 0 end) as disAssemOutTotal
        from
        jsh_depot_head dh
        left join jsh_depot_item di on dh.id=di.header_id and ifnull(di.delete_flag,'0') !='1'
        where 1=1
        and ifnull(dh.delete_flag,'0') !='1'
        and di.material_extend_id=#{meId}
        and ifnull(di.sku,'') !=''
        <if test="forceFlag">
            and dh.status = '1'
        </if>
        <if test="beginTime != null">
            and dh.oper_time &gt;= #{beginTime}
        </if>
        <if test="endTime != null">
            and dh.oper_time &lt;= #{endTime}
        </if>
    </select>

    <select id="getStockByParamWithDepotList" resultMap="stockMap">
        select
        sum(case when dh.type='入库' <include refid="inOutManageParam" /> <include refid="depotParam" /> then di.basic_number else 0 end) as inTotal,
        sum(case when dh.type='出库' and dh.sub_type!='调拨' <include refid="inOutManageParam" /> <include refid="depotParam" /> then di.basic_number else 0 end) as outTotal,
        sum(case when dh.sub_type='调拨' <include refid="anotherDepotParam" /> then di.basic_number else 0 end) as transfInTotal,
        sum(case when dh.sub_type='调拨' <include refid="depotParam" /> then di.basic_number else 0 end) as transfOutTotal,
        sum(case when dh.sub_type='组装单' and di.material_type='组合件' <include refid="depotParam" /> then di.basic_number else 0 end) as assemInTotal,
        sum(case when dh.sub_type='组装单' and di.material_type='普通子件' <include refid="depotParam" /> then di.basic_number else 0 end) as assemOutTotal,
        sum(case when dh.sub_type='拆卸单' and di.material_type='普通子件' <include refid="depotParam" /> then di.basic_number else 0 end) as disAssemInTotal,
        sum(case when dh.sub_type='拆卸单' and di.material_type='组合件' <include refid="depotParam" /> then di.basic_number else 0 end) as disAssemOutTotal
        from
        jsh_depot_head dh
        left join jsh_depot_item di on dh.id=di.header_id and ifnull(di.delete_flag,'0') !='1'
        where 1=1
        and ifnull(dh.delete_flag,'0') !='1'
        and di.material_id=#{mId}
        <if test="forceFlag">
            and dh.status = '1'
        </if>
        <if test="beginTime != null">
            and dh.oper_time &gt;= #{beginTime}
        </if>
        <if test="endTime != null">
            and dh.oper_time &lt;= #{endTime}
        </if>
    </select>

    <sql id="inOutManageParam">
        <if test="inOutManageFlag">
            and (dh.sub_type!='采购' and dh.sub_type!='采购退货' and dh.sub_type!='销售' and dh.sub_type!='销售退货')
        </if>
    </sql>

    <sql id="depotParam">
        <if test="depotList.size()>0">
            and di.depot_id in <foreach collection="depotList" item="item" index="index" separator="," open="(" close=")">#{item}</foreach>
        </if>
    </sql>

    <sql id="anotherDepotParam">
        <if test="depotList.size()>0">
            and di.another_depot_id in <foreach collection="depotList" item="item" index="index" separator="," open="(" close=")">#{item}</foreach>
        </if>
    </sql>

    <select id="findDepotItemListBydepotheadId"  resultType="com.jsh.erp.datasource.entities.DepotItem">
        select
        dep.id,dep.header_id,dep.material_id,dep.material_unit,dep.oper_number,
        dep.basic_number,dep.unit_price,dep.tax_unit_price,dep.all_price,dep.remark,
        dep.depot_id,dep.another_depot_id,dep.tax_rate,
        dep.tax_money,dep.tax_last_money,dep.material_type
        from jsh_depot_item dep,jsh_material mat
        where 1=1
        <if test="depotheadId != null">
           and dep.header_id = #{depotheadId}
        </if>
            and dep.material_id=mat.id
        <if test="enableSerialNumber != null">
           and mat.enable_serial_number = #{enableSerialNumber}
        </if>
        and ifnull(dep.delete_flag,'0') !='1'
        and ifnull(mat.delete_flag,'0') !='1'
    </select>

    <update id="batchDeleteDepotItemByDepotHeadIds">
        update jsh_depot_item
        set delete_flag='1'
        where 1=1
        and header_id in
        (
        <foreach collection="depotheadIds" item="depotheadId" separator=",">
            #{depotheadId}
        </foreach>
        )
    </update>

    <update id="batchDeleteDepotItemByIds">
        update jsh_depot_item
        set delete_flag='1'
        where 1=1
        and id in (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
    </update>

    <select id="getDepotItemListListByDepotIds" resultMap="com.jsh.erp.datasource.mappers.DepotItemMapper.BaseResultMap">
        select
        <include refid="com.jsh.erp.datasource.mappers.DepotItemMapper.Base_Column_List" />
        from jsh_depot_item
        where 1=1
        and depot_id in (
        <foreach collection="depotIds" item="depotId" separator=",">
            #{depotId}
        </foreach>
        )
        and ifnull(delete_flag,'0') !='1'
    </select>

    <select id="getDepotItemListListByMaterialIds" resultMap="com.jsh.erp.datasource.mappers.DepotItemMapper.BaseResultMap">
        select
        <include refid="com.jsh.erp.datasource.mappers.DepotItemMapper.Base_Column_List" />
        from jsh_depot_item
        where 1=1
        and material_id in (
        <foreach collection="materialIds" item="materialId" separator=",">
            #{materialId}
        </foreach>
        )
        and ifnull(delete_flag,'0') !='1'
    </select>

    <select id="findStockWarningCount" parameterType="com.jsh.erp.datasource.entities.DepotItemExample" resultMap="ResultStockWarningCount">
        select m.id MId, m.name MName, me.bar_code, m.mfrs MMfrs, m.model MModel, m.standard MStandard,m.color MColor, m.brand,
        m.other_field1 MOtherField1,m.other_field2 MOtherField2,m.other_field3 MOtherField3,d.name depotName,
        m.unit MaterialUnit, u.basic_unit unit_name,mcs.current_number,
        mis.low_safe_stock, mis.high_safe_stock
        from jsh_material m
        left join jsh_material_extend me on me.material_id=m.id and ifnull(me.delete_Flag,'0') !='1'
        left join jsh_material_initial_stock mis on mis.material_id=m.id and ifnull(mis.delete_Flag,'0') !='1'
        left join jsh_material_current_stock mcs on mcs.material_id=m.id and ifnull(mcs.delete_Flag,'0') !='1'
        left join jsh_unit u on m.unit_id=u.id and ifnull(u.delete_Flag,'0') !='1'
        left join jsh_depot d on d.id=mis.depot_id and ifnull(u.delete_flag,'0') !='1'
        where 1=1
        and me.default_flag=1
        and ifnull(m.delete_flag,'0') !='1'
        and mis.depot_id=mcs.depot_id
        and ((ifnull(mis.low_safe_stock,0)!=0 and mcs.current_number &lt; ifnull(mis.low_safe_stock,0))
            or (ifnull(mis.high_safe_stock,0)!=0 and mcs.current_number > ifnull(mis.high_safe_stock,0)))
        <if test="materialParam != null and materialParam !=''">
            <bind name="bindKey" value="'%'+materialParam+'%'"/>
            and (me.bar_code like #{bindKey} or m.name like #{bindKey} or m.mnemonic like #{bindKey} or m.standard like #{bindKey} or m.model like #{bindKey}
            or m.color like #{bindKey} or m.mfrs like #{bindKey} or m.brand like #{bindKey} or m.other_field1 like #{bindKey}
            or m.other_field2 like #{bindKey} or m.other_field3 like #{bindKey})
        </if>
        <if test="depotList.size()>0">
            and mis.depot_id in
            <foreach collection="depotList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="categoryList.size()>0">
            and m.category_id in
            <foreach collection="categoryList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        order by depotName asc
        <if test="offset != null and rows != null">
            limit #{offset},#{rows}
        </if>
    </select>

    <select id="findStockWarningCountTotal" resultType="java.lang.Integer">
        select count(1) from
        (select m.id
        from jsh_material m
        left join jsh_material_extend me on me.material_id=m.id and ifnull(me.delete_Flag,'0') !='1'
        left join jsh_material_initial_stock mis on mis.material_id=m.id and ifnull(mis.delete_Flag,'0') !='1'
        left join jsh_material_current_stock mcs on mcs.material_id=m.id and ifnull(mcs.delete_Flag,'0') !='1'
        left join jsh_unit u on m.unit_id=u.id and ifnull(u.delete_Flag,'0') !='1'
        left join jsh_depot d on d.id=mis.depot_id and ifnull(u.delete_flag,'0') !='1'
        where 1=1
        and me.default_flag=1
        and ifnull(m.delete_flag,'0') !='1'
        and mis.depot_id=mcs.depot_id
        and ((ifnull(mis.low_safe_stock,0)!=0 and mcs.current_number &lt; ifnull(mis.low_safe_stock,0))
            or (ifnull(mis.high_safe_stock,0)!=0 and mcs.current_number > ifnull(mis.high_safe_stock,0)))
        <if test="materialParam != null and materialParam !=''">
            <bind name="bindKey" value="'%'+materialParam+'%'"/>
            and (me.bar_code like #{bindKey} or m.name like #{bindKey} or m.mnemonic like #{bindKey} or m.standard like #{bindKey} or m.model like #{bindKey}
            or m.color like #{bindKey} or m.mfrs like #{bindKey} or m.brand like #{bindKey} or m.other_field1 like #{bindKey}
            or m.other_field2 like #{bindKey} or m.other_field3 like #{bindKey})
        </if>
        <if test="depotList.size()>0">
            and mis.depot_id in
            <foreach collection="depotList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="categoryList.size()>0">
            and m.category_id in
            <foreach collection="categoryList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        ) tb
    </select>

    <select id="getFinishNumber" resultType="java.math.BigDecimal">
        select ifnull(sum(di.basic_number),0) from jsh_depot_item di
        where di.material_extend_id=#{meId}
        and di.link_id=#{linkId}
        and ifnull(di.delete_flag,'0') !='1'
        and di.header_id
        in
        (
        select dh.id from jsh_depot_head dh
        where 1=1
        <if test="noType == 'normal'">
            and dh.link_number=#{linkStr}
        </if>
        <if test="noType == 'apply'">
            and dh.link_apply=#{linkStr}
        </if>
        and ifnull(dh.delete_flag,'0') !='1'
        <if test="goToType != null and goToType !=''">
            and dh.sub_type=#{goToType}
        </if>
        )
    </select>

    <select id="getRealFinishNumber" resultType="java.math.BigDecimal">
        select ifnull(sum(di.basic_number),0) from jsh_depot_item di
        where di.material_extend_id=#{meId}
        and di.link_id=#{linkId}
        and ifnull(di.delete_flag,'0') !='1'
        and di.header_id
        in
        (
        select dh.id from jsh_depot_head dh
        where
        dh.id!=#{currentHeaderId}
        <if test="linkType == 'normal'">
            and dh.link_number=#{linkStr}
        </if>
        <if test="linkType == 'apply'">
            and dh.link_apply=#{linkStr}
        </if>
        and ifnull(dh.delete_flag,'0') !='1'
        <if test="goToType != null and goToType !=''">
            and dh.sub_type=#{goToType}
        </if>
        )
    </select>

    <select id="getBatchNumberList" resultMap="batchNumberListMap">
        select id, bar_code, name, standard, model, unit_id,
        commodity_unit, batch_number, expiration_date, sum(basic_number) total_num from
        (select di.batch_number id, me.bar_code, m.name, m.standard, m.model, m.unit_id,
         me.commodity_unit, di.batch_number,di.expiration_date,
         di.basic_number*(case dh.type when '入库' then 1 when '出库' then -1 end) as basic_number
        from jsh_depot_head dh
        left join jsh_depot_item di on dh.id=di.header_id
        left join jsh_material m on m.id=di.material_id and ifnull(m.delete_flag, '0') != '1'
        left join jsh_material_extend me on me.material_id=m.id and ifnull(me.delete_flag,'0') !='1'
        where me.bar_code= #{barCode}
        <if test="name != null">
            <bind name="bindName" value="'%'+name+'%'"/>
            and di.batch_number like #{bindName}
        </if>
        <if test="depotId != null">
            and di.depot_id= #{depotId}
        </if>
        <if test="batchNumber != null">
            and di.batch_number= #{batchNumber}
        </if>
        <if test="number != null">
            and dh.number!= #{number}
        </if>
        <if test="forceFlag">
            and dh.status = '1'
        </if>
        <if test="inOutManageFlag">
            and (dh.sub_type!='采购' and dh.sub_type!='采购退货' and dh.sub_type!='销售' and dh.sub_type!='销售退货')
        </if>
        and m.enable_batch_number =1
        and di.delete_flag!=1) tb
        group by id, bar_code, name, standard, model, unit_id,
        commodity_unit, batch_number, expiration_date
        order by expiration_date asc
    </select>

    <select id="getCountByMaterialAndDepot" resultType="java.lang.Long">
        select count(1) from jsh_depot_item di
        where di.material_id=#{mId} and di.depot_id=#{depotId}
        and ifnull(di.delete_flag,'0') !='1'
    </select>

    <select id="getLinkBillDetailMaterialSum" resultMap="materialSumMap">
        select di.material_extend_id, sum(di.oper_number) oper_number from jsh_depot_head dh
        left join jsh_depot_item di on dh.id=di.header_id and ifnull(di.delete_flag,'0') !='1'
        where dh.number=#{linkStr}
          and ifnull(dh.delete_flag,'0') !='1'
        group by di.material_extend_id
    </select>

    <select id="getBatchBillDetailMaterialSum" resultMap="materialSumMap">
        select di.material_extend_id, sum(di.oper_number) oper_number from jsh_depot_head dh
        left join jsh_depot_item di on dh.id=di.header_id and ifnull(di.delete_flag,'0') !='1'
        where dh.type=#{type}
        <if test="linkType == 'normal'">
            and dh.link_number=#{linkStr}
        </if>
        <if test="linkType == 'apply'">
            and dh.link_apply=#{linkStr}
        </if>
        and ifnull(dh.delete_flag,'0') !='1'
        group by di.material_extend_id
    </select>

    <select id="getCountByMaterialAndBatchNumber" resultType="java.lang.Long">
        select count(di.id) from
        jsh_depot_head dh
        left join jsh_depot_item di on dh.id=di.header_id and ifnull(di.delete_flag,'0') !='1'
        where 1=1
        and ifnull(dh.delete_flag,'0') !='1'
        and di.material_extend_id=#{meId}
        and di.batch_number=#{batchNumber}
    </select>

    <select id="getDepotItemByBatchNumber" resultType="com.jsh.erp.datasource.entities.DepotItem">
        select di.* from jsh_depot_head dh
        left join jsh_depot_item di on dh.id=di.header_id and ifnull(di.delete_flag,'0') !='1'
        where 1=1
        and ifnull(dh.delete_flag,'0') !='1'
        and di.material_extend_id = #{materialExtendId}
        and di.batch_number = #{batchNumber}
        and dh.type = '入库'
    </select>

    <select id="getBillItemByParam" resultType="com.jsh.erp.datasource.entities.MaterialVo4Unit">
        select m.id, m.name, m.standard, m.model, me.id meId,me.commodity_unit commodityUnit,
        me.purchase_decimal purchaseDecimal, me.wholesale_decimal wholesaleDecimal, me.bar_code mBarCode, me.sku
        from jsh_material m
        left join jsh_material_extend me on m.id=me.material_id and ifnull(me.delete_Flag,'0') !='1'
        where 1=1
        <if test="barCodes != null">
            and me.bar_code in (${barCodes})
        </if>
        and ifnull(m.delete_flag,'0') !='1'
        order by m.id desc
    </select>

    <select id="getCurrentStockByParam" resultType="java.math.BigDecimal">
        select sum(ifnull(current_number,0))
        from jsh_material_current_stock
        where 1=1
        <if test="depotId != null">
            and depot_id = ${depotId}
        </if>
        <if test="mId != null">
            and material_id = ${mId}
        </if>
        and ifnull(delete_flag,'0') !='1'
    </select>

    <select id="getLastUnitPriceByParam" resultType="java.math.BigDecimal">
        select di.unit_price from jsh_depot_head dh
        left join jsh_depot_item di on dh.id=di.header_id and ifnull(di.delete_flag,'0') !='1'
        where dh.organ_id = #{organId}
        and di.material_extend_id = #{meId}
        and dh.type = #{type} and dh.sub_type = #{subType}
        and ifnull(dh.delete_flag,'0') !='1'
        order by dh.id desc, di.id desc
        limit 0,1
    </select>

</mapper>
