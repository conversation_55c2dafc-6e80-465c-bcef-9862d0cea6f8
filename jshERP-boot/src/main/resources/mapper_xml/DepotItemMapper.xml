<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.DepotItemMapper">
  <resultMap id="BaseResultMap" type="com.jsh.erp.datasource.entities.DepotItem">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="header_id" jdbcType="BIGINT" property="headerId" />
    <result column="material_id" jdbcType="BIGINT" property="materialId" />
    <result column="material_extend_id" jdbcType="BIGINT" property="materialExtendId" />
    <result column="material_unit" jdbcType="VARCHAR" property="materialUnit" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="oper_number" jdbcType="DECIMAL" property="operNumber" />
    <result column="basic_number" jdbcType="DECIMAL" property="basicNumber" />
    <result column="unit_price" jdbcType="DECIMAL" property="unitPrice" />
    <result column="purchase_unit_price" jdbcType="DECIMAL" property="purchaseUnitPrice" />
    <result column="tax_unit_price" jdbcType="DECIMAL" property="taxUnitPrice" />
    <result column="all_price" jdbcType="DECIMAL" property="allPrice" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="depot_id" jdbcType="BIGINT" property="depotId" />
    <result column="another_depot_id" jdbcType="BIGINT" property="anotherDepotId" />
    <result column="tax_rate" jdbcType="DECIMAL" property="taxRate" />
    <result column="tax_money" jdbcType="DECIMAL" property="taxMoney" />
    <result column="tax_last_money" jdbcType="DECIMAL" property="taxLastMoney" />
    <result column="material_type" jdbcType="VARCHAR" property="materialType" />
    <result column="sn_list" jdbcType="VARCHAR" property="snList" />
    <result column="batch_number" jdbcType="VARCHAR" property="batchNumber" />
    <result column="expiration_date" jdbcType="TIMESTAMP" property="expirationDate" />
    <result column="link_id" jdbcType="BIGINT" property="linkId" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, header_id, material_id, material_extend_id, material_unit, sku, oper_number, 
    basic_number, unit_price, purchase_unit_price, tax_unit_price, all_price, remark, 
    depot_id, another_depot_id, tax_rate, tax_money, tax_last_money, material_type, sn_list, 
    batch_number, expiration_date, link_id, tenant_id, delete_flag
  </sql>
  <select id="selectByExample" parameterType="com.jsh.erp.datasource.entities.DepotItemExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from jsh_depot_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jsh_depot_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from jsh_depot_item
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.jsh.erp.datasource.entities.DepotItemExample">
    delete from jsh_depot_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.jsh.erp.datasource.entities.DepotItem">
    insert into jsh_depot_item (id, header_id, material_id, 
      material_extend_id, material_unit, sku, 
      oper_number, basic_number, unit_price, 
      purchase_unit_price, tax_unit_price, all_price, 
      remark, depot_id, another_depot_id, 
      tax_rate, tax_money, tax_last_money, 
      material_type, sn_list, batch_number, 
      expiration_date, link_id, tenant_id, 
      delete_flag)
    values (#{id,jdbcType=BIGINT}, #{headerId,jdbcType=BIGINT}, #{materialId,jdbcType=BIGINT}, 
      #{materialExtendId,jdbcType=BIGINT}, #{materialUnit,jdbcType=VARCHAR}, #{sku,jdbcType=VARCHAR}, 
      #{operNumber,jdbcType=DECIMAL}, #{basicNumber,jdbcType=DECIMAL}, #{unitPrice,jdbcType=DECIMAL}, 
      #{purchaseUnitPrice,jdbcType=DECIMAL}, #{taxUnitPrice,jdbcType=DECIMAL}, #{allPrice,jdbcType=DECIMAL}, 
      #{remark,jdbcType=VARCHAR}, #{depotId,jdbcType=BIGINT}, #{anotherDepotId,jdbcType=BIGINT}, 
      #{taxRate,jdbcType=DECIMAL}, #{taxMoney,jdbcType=DECIMAL}, #{taxLastMoney,jdbcType=DECIMAL}, 
      #{materialType,jdbcType=VARCHAR}, #{snList,jdbcType=VARCHAR}, #{batchNumber,jdbcType=VARCHAR}, 
      #{expirationDate,jdbcType=TIMESTAMP}, #{linkId,jdbcType=BIGINT}, #{tenantId,jdbcType=BIGINT}, 
      #{deleteFlag,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.jsh.erp.datasource.entities.DepotItem">
    insert into jsh_depot_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="headerId != null">
        header_id,
      </if>
      <if test="materialId != null">
        material_id,
      </if>
      <if test="materialExtendId != null">
        material_extend_id,
      </if>
      <if test="materialUnit != null">
        material_unit,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="operNumber != null">
        oper_number,
      </if>
      <if test="basicNumber != null">
        basic_number,
      </if>
      <if test="unitPrice != null">
        unit_price,
      </if>
      <if test="purchaseUnitPrice != null">
        purchase_unit_price,
      </if>
      <if test="taxUnitPrice != null">
        tax_unit_price,
      </if>
      <if test="allPrice != null">
        all_price,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="depotId != null">
        depot_id,
      </if>
      <if test="anotherDepotId != null">
        another_depot_id,
      </if>
      <if test="taxRate != null">
        tax_rate,
      </if>
      <if test="taxMoney != null">
        tax_money,
      </if>
      <if test="taxLastMoney != null">
        tax_last_money,
      </if>
      <if test="materialType != null">
        material_type,
      </if>
      <if test="snList != null">
        sn_list,
      </if>
      <if test="batchNumber != null">
        batch_number,
      </if>
      <if test="expirationDate != null">
        expiration_date,
      </if>
      <if test="linkId != null">
        link_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="headerId != null">
        #{headerId,jdbcType=BIGINT},
      </if>
      <if test="materialId != null">
        #{materialId,jdbcType=BIGINT},
      </if>
      <if test="materialExtendId != null">
        #{materialExtendId,jdbcType=BIGINT},
      </if>
      <if test="materialUnit != null">
        #{materialUnit,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="operNumber != null">
        #{operNumber,jdbcType=DECIMAL},
      </if>
      <if test="basicNumber != null">
        #{basicNumber,jdbcType=DECIMAL},
      </if>
      <if test="unitPrice != null">
        #{unitPrice,jdbcType=DECIMAL},
      </if>
      <if test="purchaseUnitPrice != null">
        #{purchaseUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="taxUnitPrice != null">
        #{taxUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="allPrice != null">
        #{allPrice,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="depotId != null">
        #{depotId,jdbcType=BIGINT},
      </if>
      <if test="anotherDepotId != null">
        #{anotherDepotId,jdbcType=BIGINT},
      </if>
      <if test="taxRate != null">
        #{taxRate,jdbcType=DECIMAL},
      </if>
      <if test="taxMoney != null">
        #{taxMoney,jdbcType=DECIMAL},
      </if>
      <if test="taxLastMoney != null">
        #{taxLastMoney,jdbcType=DECIMAL},
      </if>
      <if test="materialType != null">
        #{materialType,jdbcType=VARCHAR},
      </if>
      <if test="snList != null">
        #{snList,jdbcType=VARCHAR},
      </if>
      <if test="batchNumber != null">
        #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="expirationDate != null">
        #{expirationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="linkId != null">
        #{linkId,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.jsh.erp.datasource.entities.DepotItemExample" resultType="java.lang.Long">
    select count(*) from jsh_depot_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update jsh_depot_item
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.headerId != null">
        header_id = #{record.headerId,jdbcType=BIGINT},
      </if>
      <if test="record.materialId != null">
        material_id = #{record.materialId,jdbcType=BIGINT},
      </if>
      <if test="record.materialExtendId != null">
        material_extend_id = #{record.materialExtendId,jdbcType=BIGINT},
      </if>
      <if test="record.materialUnit != null">
        material_unit = #{record.materialUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.sku != null">
        sku = #{record.sku,jdbcType=VARCHAR},
      </if>
      <if test="record.operNumber != null">
        oper_number = #{record.operNumber,jdbcType=DECIMAL},
      </if>
      <if test="record.basicNumber != null">
        basic_number = #{record.basicNumber,jdbcType=DECIMAL},
      </if>
      <if test="record.unitPrice != null">
        unit_price = #{record.unitPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.purchaseUnitPrice != null">
        purchase_unit_price = #{record.purchaseUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.taxUnitPrice != null">
        tax_unit_price = #{record.taxUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.allPrice != null">
        all_price = #{record.allPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.depotId != null">
        depot_id = #{record.depotId,jdbcType=BIGINT},
      </if>
      <if test="record.anotherDepotId != null">
        another_depot_id = #{record.anotherDepotId,jdbcType=BIGINT},
      </if>
      <if test="record.taxRate != null">
        tax_rate = #{record.taxRate,jdbcType=DECIMAL},
      </if>
      <if test="record.taxMoney != null">
        tax_money = #{record.taxMoney,jdbcType=DECIMAL},
      </if>
      <if test="record.taxLastMoney != null">
        tax_last_money = #{record.taxLastMoney,jdbcType=DECIMAL},
      </if>
      <if test="record.materialType != null">
        material_type = #{record.materialType,jdbcType=VARCHAR},
      </if>
      <if test="record.snList != null">
        sn_list = #{record.snList,jdbcType=VARCHAR},
      </if>
      <if test="record.batchNumber != null">
        batch_number = #{record.batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.expirationDate != null">
        expiration_date = #{record.expirationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.linkId != null">
        link_id = #{record.linkId,jdbcType=BIGINT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
      <if test="record.deleteFlag != null">
        delete_flag = #{record.deleteFlag,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update jsh_depot_item
    set id = #{record.id,jdbcType=BIGINT},
      header_id = #{record.headerId,jdbcType=BIGINT},
      material_id = #{record.materialId,jdbcType=BIGINT},
      material_extend_id = #{record.materialExtendId,jdbcType=BIGINT},
      material_unit = #{record.materialUnit,jdbcType=VARCHAR},
      sku = #{record.sku,jdbcType=VARCHAR},
      oper_number = #{record.operNumber,jdbcType=DECIMAL},
      basic_number = #{record.basicNumber,jdbcType=DECIMAL},
      unit_price = #{record.unitPrice,jdbcType=DECIMAL},
      purchase_unit_price = #{record.purchaseUnitPrice,jdbcType=DECIMAL},
      tax_unit_price = #{record.taxUnitPrice,jdbcType=DECIMAL},
      all_price = #{record.allPrice,jdbcType=DECIMAL},
      remark = #{record.remark,jdbcType=VARCHAR},
      depot_id = #{record.depotId,jdbcType=BIGINT},
      another_depot_id = #{record.anotherDepotId,jdbcType=BIGINT},
      tax_rate = #{record.taxRate,jdbcType=DECIMAL},
      tax_money = #{record.taxMoney,jdbcType=DECIMAL},
      tax_last_money = #{record.taxLastMoney,jdbcType=DECIMAL},
      material_type = #{record.materialType,jdbcType=VARCHAR},
      sn_list = #{record.snList,jdbcType=VARCHAR},
      batch_number = #{record.batchNumber,jdbcType=VARCHAR},
      expiration_date = #{record.expirationDate,jdbcType=TIMESTAMP},
      link_id = #{record.linkId,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=BIGINT},
      delete_flag = #{record.deleteFlag,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.jsh.erp.datasource.entities.DepotItem">
    update jsh_depot_item
    <set>
      <if test="headerId != null">
        header_id = #{headerId,jdbcType=BIGINT},
      </if>
      <if test="materialId != null">
        material_id = #{materialId,jdbcType=BIGINT},
      </if>
      <if test="materialExtendId != null">
        material_extend_id = #{materialExtendId,jdbcType=BIGINT},
      </if>
      <if test="materialUnit != null">
        material_unit = #{materialUnit,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="operNumber != null">
        oper_number = #{operNumber,jdbcType=DECIMAL},
      </if>
      <if test="basicNumber != null">
        basic_number = #{basicNumber,jdbcType=DECIMAL},
      </if>
      <if test="unitPrice != null">
        unit_price = #{unitPrice,jdbcType=DECIMAL},
      </if>
      <if test="purchaseUnitPrice != null">
        purchase_unit_price = #{purchaseUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="taxUnitPrice != null">
        tax_unit_price = #{taxUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="allPrice != null">
        all_price = #{allPrice,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="depotId != null">
        depot_id = #{depotId,jdbcType=BIGINT},
      </if>
      <if test="anotherDepotId != null">
        another_depot_id = #{anotherDepotId,jdbcType=BIGINT},
      </if>
      <if test="taxRate != null">
        tax_rate = #{taxRate,jdbcType=DECIMAL},
      </if>
      <if test="taxMoney != null">
        tax_money = #{taxMoney,jdbcType=DECIMAL},
      </if>
      <if test="taxLastMoney != null">
        tax_last_money = #{taxLastMoney,jdbcType=DECIMAL},
      </if>
      <if test="materialType != null">
        material_type = #{materialType,jdbcType=VARCHAR},
      </if>
      <if test="snList != null">
        sn_list = #{snList,jdbcType=VARCHAR},
      </if>
      <if test="batchNumber != null">
        batch_number = #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="expirationDate != null">
        expiration_date = #{expirationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="linkId != null">
        link_id = #{linkId,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jsh.erp.datasource.entities.DepotItem">
    update jsh_depot_item
    set header_id = #{headerId,jdbcType=BIGINT},
      material_id = #{materialId,jdbcType=BIGINT},
      material_extend_id = #{materialExtendId,jdbcType=BIGINT},
      material_unit = #{materialUnit,jdbcType=VARCHAR},
      sku = #{sku,jdbcType=VARCHAR},
      oper_number = #{operNumber,jdbcType=DECIMAL},
      basic_number = #{basicNumber,jdbcType=DECIMAL},
      unit_price = #{unitPrice,jdbcType=DECIMAL},
      purchase_unit_price = #{purchaseUnitPrice,jdbcType=DECIMAL},
      tax_unit_price = #{taxUnitPrice,jdbcType=DECIMAL},
      all_price = #{allPrice,jdbcType=DECIMAL},
      remark = #{remark,jdbcType=VARCHAR},
      depot_id = #{depotId,jdbcType=BIGINT},
      another_depot_id = #{anotherDepotId,jdbcType=BIGINT},
      tax_rate = #{taxRate,jdbcType=DECIMAL},
      tax_money = #{taxMoney,jdbcType=DECIMAL},
      tax_last_money = #{taxLastMoney,jdbcType=DECIMAL},
      material_type = #{materialType,jdbcType=VARCHAR},
      sn_list = #{snList,jdbcType=VARCHAR},
      batch_number = #{batchNumber,jdbcType=VARCHAR},
      expiration_date = #{expirationDate,jdbcType=TIMESTAMP},
      link_id = #{linkId,jdbcType=BIGINT},
      tenant_id = #{tenantId,jdbcType=BIGINT},
      delete_flag = #{deleteFlag,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>