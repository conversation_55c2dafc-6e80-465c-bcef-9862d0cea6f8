<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.UserBusinessMapper">
  <resultMap id="BaseResultMap" type="com.jsh.erp.datasource.entities.UserBusiness">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="key_id" jdbcType="VARCHAR" property="keyId" />
    <result column="value" jdbcType="VARCHAR" property="value" />
    <result column="btn_str" jdbcType="VARCHAR" property="btnStr" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, type, key_id, value, btn_str, tenant_id, delete_flag
  </sql>
  <select id="selectByExample" parameterType="com.jsh.erp.datasource.entities.UserBusinessExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from jsh_user_business
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jsh_user_business
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from jsh_user_business
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.jsh.erp.datasource.entities.UserBusinessExample">
    delete from jsh_user_business
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.jsh.erp.datasource.entities.UserBusiness">
    insert into jsh_user_business (id, type, key_id, 
      value, btn_str, tenant_id, 
      delete_flag)
    values (#{id,jdbcType=BIGINT}, #{type,jdbcType=VARCHAR}, #{keyId,jdbcType=VARCHAR}, 
      #{value,jdbcType=VARCHAR}, #{btnStr,jdbcType=VARCHAR}, #{tenantId,jdbcType=BIGINT}, 
      #{deleteFlag,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.jsh.erp.datasource.entities.UserBusiness">
    insert into jsh_user_business
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="keyId != null">
        key_id,
      </if>
      <if test="value != null">
        value,
      </if>
      <if test="btnStr != null">
        btn_str,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="keyId != null">
        #{keyId,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        #{value,jdbcType=VARCHAR},
      </if>
      <if test="btnStr != null">
        #{btnStr,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.jsh.erp.datasource.entities.UserBusinessExample" resultType="java.lang.Long">
    select count(*) from jsh_user_business
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update jsh_user_business
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.keyId != null">
        key_id = #{record.keyId,jdbcType=VARCHAR},
      </if>
      <if test="record.value != null">
        value = #{record.value,jdbcType=VARCHAR},
      </if>
      <if test="record.btnStr != null">
        btn_str = #{record.btnStr,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
      <if test="record.deleteFlag != null">
        delete_flag = #{record.deleteFlag,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update jsh_user_business
    set id = #{record.id,jdbcType=BIGINT},
      type = #{record.type,jdbcType=VARCHAR},
      key_id = #{record.keyId,jdbcType=VARCHAR},
      value = #{record.value,jdbcType=VARCHAR},
      btn_str = #{record.btnStr,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=BIGINT},
      delete_flag = #{record.deleteFlag,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.jsh.erp.datasource.entities.UserBusiness">
    update jsh_user_business
    <set>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="keyId != null">
        key_id = #{keyId,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        value = #{value,jdbcType=VARCHAR},
      </if>
      <if test="btnStr != null">
        btn_str = #{btnStr,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jsh.erp.datasource.entities.UserBusiness">
    update jsh_user_business
    set type = #{type,jdbcType=VARCHAR},
      key_id = #{keyId,jdbcType=VARCHAR},
      value = #{value,jdbcType=VARCHAR},
      btn_str = #{btnStr,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=BIGINT},
      delete_flag = #{deleteFlag,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>