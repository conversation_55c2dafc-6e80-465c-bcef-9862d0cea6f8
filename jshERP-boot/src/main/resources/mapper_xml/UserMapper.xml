<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.UserMapper">
  <resultMap id="BaseResultMap" type="com.jsh.erp.datasource.entities.User">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="login_name" jdbcType="VARCHAR" property="loginName" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="leader_flag" jdbcType="VARCHAR" property="leaderFlag" />
    <result column="position" jdbcType="VARCHAR" property="position" />
    <result column="department" jdbcType="VARCHAR" property="department" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="phonenum" jdbcType="VARCHAR" property="phonenum" />
    <result column="ismanager" jdbcType="TINYINT" property="ismanager" />
    <result column="isystem" jdbcType="TINYINT" property="isystem" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="weixin_open_id" jdbcType="VARCHAR" property="weixinOpenId" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, username, login_name, password, leader_flag, position, department, email, phonenum, 
    ismanager, isystem, status, description, remark, weixin_open_id, tenant_id, delete_flag
  </sql>
  <select id="selectByExample" parameterType="com.jsh.erp.datasource.entities.UserExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from jsh_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jsh_user
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from jsh_user
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.jsh.erp.datasource.entities.UserExample">
    delete from jsh_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.jsh.erp.datasource.entities.User">
    insert into jsh_user (id, username, login_name, 
      password, leader_flag, position, 
      department, email, phonenum, 
      ismanager, isystem, status, 
      description, remark, weixin_open_id, 
      tenant_id, delete_flag)
    values (#{id,jdbcType=BIGINT}, #{username,jdbcType=VARCHAR}, #{loginName,jdbcType=VARCHAR}, 
      #{password,jdbcType=VARCHAR}, #{leaderFlag,jdbcType=VARCHAR}, #{position,jdbcType=VARCHAR}, 
      #{department,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, #{phonenum,jdbcType=VARCHAR}, 
      #{ismanager,jdbcType=TINYINT}, #{isystem,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}, 
      #{description,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{weixinOpenId,jdbcType=VARCHAR}, 
      #{tenantId,jdbcType=BIGINT}, #{deleteFlag,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.jsh.erp.datasource.entities.User">
    insert into jsh_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="username != null">
        username,
      </if>
      <if test="loginName != null">
        login_name,
      </if>
      <if test="password != null">
        password,
      </if>
      <if test="leaderFlag != null">
        leader_flag,
      </if>
      <if test="position != null">
        position,
      </if>
      <if test="department != null">
        department,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="phonenum != null">
        phonenum,
      </if>
      <if test="ismanager != null">
        ismanager,
      </if>
      <if test="isystem != null">
        isystem,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="weixinOpenId != null">
        weixin_open_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="loginName != null">
        #{loginName,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="leaderFlag != null">
        #{leaderFlag,jdbcType=VARCHAR},
      </if>
      <if test="position != null">
        #{position,jdbcType=VARCHAR},
      </if>
      <if test="department != null">
        #{department,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="phonenum != null">
        #{phonenum,jdbcType=VARCHAR},
      </if>
      <if test="ismanager != null">
        #{ismanager,jdbcType=TINYINT},
      </if>
      <if test="isystem != null">
        #{isystem,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="weixinOpenId != null">
        #{weixinOpenId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.jsh.erp.datasource.entities.UserExample" resultType="java.lang.Long">
    select count(*) from jsh_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update jsh_user
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.username != null">
        username = #{record.username,jdbcType=VARCHAR},
      </if>
      <if test="record.loginName != null">
        login_name = #{record.loginName,jdbcType=VARCHAR},
      </if>
      <if test="record.password != null">
        password = #{record.password,jdbcType=VARCHAR},
      </if>
      <if test="record.leaderFlag != null">
        leader_flag = #{record.leaderFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.position != null">
        position = #{record.position,jdbcType=VARCHAR},
      </if>
      <if test="record.department != null">
        department = #{record.department,jdbcType=VARCHAR},
      </if>
      <if test="record.email != null">
        email = #{record.email,jdbcType=VARCHAR},
      </if>
      <if test="record.phonenum != null">
        phonenum = #{record.phonenum,jdbcType=VARCHAR},
      </if>
      <if test="record.ismanager != null">
        ismanager = #{record.ismanager,jdbcType=TINYINT},
      </if>
      <if test="record.isystem != null">
        isystem = #{record.isystem,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.weixinOpenId != null">
        weixin_open_id = #{record.weixinOpenId,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
      <if test="record.deleteFlag != null">
        delete_flag = #{record.deleteFlag,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update jsh_user
    set id = #{record.id,jdbcType=BIGINT},
      username = #{record.username,jdbcType=VARCHAR},
      login_name = #{record.loginName,jdbcType=VARCHAR},
      password = #{record.password,jdbcType=VARCHAR},
      leader_flag = #{record.leaderFlag,jdbcType=VARCHAR},
      position = #{record.position,jdbcType=VARCHAR},
      department = #{record.department,jdbcType=VARCHAR},
      email = #{record.email,jdbcType=VARCHAR},
      phonenum = #{record.phonenum,jdbcType=VARCHAR},
      ismanager = #{record.ismanager,jdbcType=TINYINT},
      isystem = #{record.isystem,jdbcType=TINYINT},
      status = #{record.status,jdbcType=TINYINT},
      description = #{record.description,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      weixin_open_id = #{record.weixinOpenId,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=BIGINT},
      delete_flag = #{record.deleteFlag,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.jsh.erp.datasource.entities.User">
    update jsh_user
    <set>
      <if test="username != null">
        username = #{username,jdbcType=VARCHAR},
      </if>
      <if test="loginName != null">
        login_name = #{loginName,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        password = #{password,jdbcType=VARCHAR},
      </if>
      <if test="leaderFlag != null">
        leader_flag = #{leaderFlag,jdbcType=VARCHAR},
      </if>
      <if test="position != null">
        position = #{position,jdbcType=VARCHAR},
      </if>
      <if test="department != null">
        department = #{department,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="phonenum != null">
        phonenum = #{phonenum,jdbcType=VARCHAR},
      </if>
      <if test="ismanager != null">
        ismanager = #{ismanager,jdbcType=TINYINT},
      </if>
      <if test="isystem != null">
        isystem = #{isystem,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="weixinOpenId != null">
        weixin_open_id = #{weixinOpenId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jsh.erp.datasource.entities.User">
    update jsh_user
    set username = #{username,jdbcType=VARCHAR},
      login_name = #{loginName,jdbcType=VARCHAR},
      password = #{password,jdbcType=VARCHAR},
      leader_flag = #{leaderFlag,jdbcType=VARCHAR},
      position = #{position,jdbcType=VARCHAR},
      department = #{department,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      phonenum = #{phonenum,jdbcType=VARCHAR},
      ismanager = #{ismanager,jdbcType=TINYINT},
      isystem = #{isystem,jdbcType=TINYINT},
      status = #{status,jdbcType=TINYINT},
      description = #{description,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      weixin_open_id = #{weixinOpenId,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=BIGINT},
      delete_flag = #{deleteFlag,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>