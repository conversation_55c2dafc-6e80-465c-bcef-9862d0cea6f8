# jshERP Docker 容器环境配置
# 该配置文件专门用于 Docker 容器化部署

# ================== 服务器配置 ==================
server.port=9999
server.servlet.context-path=/jshERP-boot
server.tomcat.uri-encoding=UTF-8
server.tomcat.max-connections=1000
server.tomcat.threads.min-spare=10
server.tomcat.threads.max=200

# ================== 数据源配置 ==================
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.url=${SPRING_DATASOURCE_URL:*********************************************************************************************************************************************************************************************************************}
spring.datasource.username=${SPRING_DATASOURCE_USERNAME:jsh_user}
spring.datasource.password=${SPRING_DATASOURCE_PASSWORD:123456}

# Druid 连接池配置
spring.datasource.druid.initial-size=5
spring.datasource.druid.min-idle=5
spring.datasource.druid.max-active=50
spring.datasource.druid.max-wait=60000
spring.datasource.druid.time-between-eviction-runs-millis=60000
spring.datasource.druid.min-evictable-idle-time-millis=300000
spring.datasource.druid.validation-query=SELECT 1 FROM DUAL
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.test-on-borrow=false
spring.datasource.druid.test-on-return=false
spring.datasource.druid.pool-prepared-statements=true
spring.datasource.druid.max-pool-prepared-statement-per-connection-size=20

# ================== Redis 配置 ==================
spring.redis.host=${SPRING_REDIS_HOST:jsherp-redis}
spring.redis.port=${SPRING_REDIS_PORT:6379}
spring.redis.password=${SPRING_REDIS_PASSWORD:1234abcd}
spring.redis.database=0
spring.redis.timeout=3000ms
spring.redis.jedis.pool.max-active=50
spring.redis.jedis.pool.max-idle=10
spring.redis.jedis.pool.min-idle=5
spring.redis.jedis.pool.max-wait=-1ms

# ================== MyBatis-Plus 配置 ==================
mybatis-plus.mapper-locations=classpath*:/mapper_xml/*.xml
mybatis-plus.type-aliases-package=com.jsh.erp.datasource.entities
mybatis-plus.global-config.db-config.id-type=auto
mybatis-plus.global-config.db-config.logic-delete-field=deleteFlag
mybatis-plus.global-config.db-config.logic-delete-value=1
mybatis-plus.global-config.db-config.logic-not-delete-value=0
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.cache-enabled=false
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

# ================== 文件上传配置 ==================
spring.servlet.multipart.enabled=true
spring.servlet.multipart.file-size-threshold=2048
spring.servlet.multipart.max-file-size=104857600
spring.servlet.multipart.max-request-size=104857600
file.uploadPath=${FILE_UPLOAD_PATH:/opt/jshERP/upload/}

# ================== 日志配置 ==================
logging.level.com.jsh.erp=INFO
logging.level.root=WARN
logging.level.org.springframework.web=INFO
logging.pattern.console=%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr([%thread]){blue} %clr(%-5level) %clr(%logger{36}){cyan} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n

# ================== 应用配置 ==================
# JWT 配置
jwt.tokenHeader=Authorization
jwt.secret=jshERP-mySecret
jwt.expiration=604800

# 跨域配置
management.endpoints.web.cors.allowed-origins=*
management.endpoints.web.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
management.endpoints.web.cors.allowed-headers=*

# 健康检查配置
management.endpoints.web.exposure.include=health,info
management.endpoint.health.show-details=when-authorized
management.health.db.enabled=true
management.health.redis.enabled=true

# 系统配置
spring.application.name=jshERP
spring.profiles.active=docker
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8

# 容器化环境特殊配置
server.tomcat.basedir=/opt/tmp/tomcat
server.tomcat.accesslog.enabled=true
server.tomcat.accesslog.directory=/opt/tmp/tomcat/logs
server.tomcat.accesslog.pattern=%t %a "%r" %s (%D ms)

# 多租户配置
tenant.enable=true
tenant.default-tenant-id=63

# 文件存储配置
file.staticAccessPath=/file/**
file.uploadFolder=${FILE_UPLOAD_PATH:/opt/jshERP/upload/}