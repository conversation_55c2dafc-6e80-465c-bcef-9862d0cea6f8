# 测试环境配置文件
# 数据库配置 - 使用内存数据库H2进行测试
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# H2数据库控制台配置
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# JPA配置
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true

# MyBatis配置
mybatis.mapper-locations=classpath:mapper_xml/*.xml
mybatis.type-aliases-package=com.jsh.erp.datasource.entities

# 日志配置
logging.level.com.jsh.erp=DEBUG
logging.level.org.springframework=WARN
logging.level.org.mybatis=DEBUG

# 禁用Redis（测试环境不需要）
spring.redis.host=
spring.redis.port=

# 禁用安全配置（测试环境）
spring.security.enabled=false

# 测试环境标识
spring.profiles.active=test
