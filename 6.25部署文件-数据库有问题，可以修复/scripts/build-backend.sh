#!/bin/bash

# jshERP后端打包脚本
# 作者: Augment Code
# 版本: 1.0
# 用途: 构建符合官方部署标准的后端部署包

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目路径
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BACKEND_DIR="$PROJECT_ROOT/jshERP-boot"
BUILD_OUTPUT_DIR="$PROJECT_ROOT/dist/backend"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境
check_environment() {
    log_info "检查构建环境..."
    
    # 检查Java
    if ! command -v java &> /dev/null; then
        log_error "Java未安装，请先安装JDK 1.8+"
        exit 1
    fi
    
    local java_version=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1-2)
    if [[ "$java_version" < "1.8" ]]; then
        log_error "Java版本过低，需要1.8+，当前版本: $java_version"
        exit 1
    fi
    
    # 检查Maven
    if ! command -v mvn &> /dev/null; then
        log_error "Maven未安装，请先安装Maven 3.6+"
        exit 1
    fi
    
    # 检查后端目录
    if [ ! -d "$BACKEND_DIR" ]; then
        log_error "后端目录不存在: $BACKEND_DIR"
        exit 1
    fi
    
    # 检查pom.xml
    if [ ! -f "$BACKEND_DIR/pom.xml" ]; then
        log_error "pom.xml文件不存在"
        exit 1
    fi
    
    log_success "环境检查通过"
}

# 清理旧的构建文件
clean_build() {
    log_info "清理旧的构建文件..."
    
    cd "$BACKEND_DIR"
    
    # 清理Maven构建目录
    if [ -d "target" ]; then
        rm -rf target
        log_info "已清理target目录"
    fi
    
    # 清理dist目录
    if [ -d "dist" ]; then
        rm -rf dist
        log_info "已清理dist目录"
    fi
    
    # 清理输出目录
    if [ -d "$BUILD_OUTPUT_DIR" ]; then
        rm -rf "$BUILD_OUTPUT_DIR"
        log_info "已清理输出目录"
    fi
    
    # 创建输出目录
    mkdir -p "$BUILD_OUTPUT_DIR"
    
    log_success "清理完成"
}

# 创建生产环境配置
create_production_config() {
    log_info "创建生产环境配置..."
    
    cd "$BACKEND_DIR/src/main/resources"
    
    # 创建application-production.properties
    cat > application-production.properties << EOF
# jshERP生产环境配置
server.port=9999
server.servlet.session.timeout=36000
server.servlet.context-path=/jshERP-boot

# 数据库连接（生产环境）
spring.datasource.url=******************************************************************************************************************************************************************************************************************
spring.datasource.driverClassName=com.mysql.jdbc.Driver
spring.datasource.username=jsh_user
spring.datasource.password=123456

# MyBatis配置
mybatis-plus.mapper-locations=classpath:./mapper_xml/*.xml

# Redis配置（生产环境）
spring.redis.host=127.0.0.1
spring.redis.port=6379
spring.redis.password=1234abcd

# 租户配置
manage.roleId=10
tenant.userNumLimit=1000000
tenant.tryDayLimit=3000

# 文件上传配置（生产环境）
file.uploadType=1
file.path=/opt/jshERP/upload
server.tomcat.basedir=/opt/tmp/tomcat
spring.servlet.multipart.max-file-size=10485760
spring.servlet.multipart.max-request-size=10485760

# 日志配置（生产环境）
logging.level.com.jsh.erp=info
logging.level.root=warn
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
logging.file.name=/opt/jshERP/logs/jshERP.log
logging.file.max-size=100MB
logging.file.max-history=30

# 性能优化配置
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000
EOF
    
    log_success "生产环境配置创建完成"
}

# 编译项目
compile_project() {
    log_info "编译项目..."
    
    cd "$BACKEND_DIR"
    
    # 清理并编译
    log_info "执行Maven clean compile..."
    mvn clean compile -DskipTests
    
    log_success "项目编译完成"
}

# 运行测试（可选）
run_tests() {
    if [ "$1" = "--skip-tests" ]; then
        log_warning "跳过测试"
        return 0
    fi
    
    log_info "运行测试..."
    
    cd "$BACKEND_DIR"
    
    # 运行测试
    mvn test
    
    log_success "测试完成"
}

# 打包项目
package_project() {
    log_info "打包项目..."
    
    cd "$BACKEND_DIR"
    
    # 执行打包
    log_info "执行Maven package..."
    mvn package -DskipTests
    
    # 检查JAR文件
    if [ ! -f "target/jshERP.jar" ]; then
        log_error "JAR文件生成失败"
        exit 1
    fi
    
    # 检查assembly包
    if [ ! -f "dist/jshERP-bin.zip" ]; then
        log_error "Assembly包生成失败"
        exit 1
    fi
    
    log_success "项目打包完成"
}

# 验证打包结果
verify_package() {
    log_info "验证打包结果..."
    
    cd "$BACKEND_DIR"
    
    # 检查JAR文件
    local jar_file="target/jshERP.jar"
    if [ ! -f "$jar_file" ]; then
        log_error "JAR文件不存在"
        exit 1
    fi
    
    # 检查JAR文件大小
    local jar_size=$(du -sh "$jar_file" | cut -f1)
    log_info "JAR文件大小: $jar_size"
    
    # 检查assembly包
    local assembly_file="dist/jshERP-bin.zip"
    if [ ! -f "$assembly_file" ]; then
        log_error "Assembly包不存在"
        exit 1
    fi
    
    # 检查assembly包内容
    local file_count=$(unzip -l "$assembly_file" | grep -c "\.jar\|\.sh\|\.properties")
    if [ "$file_count" -lt 5 ]; then
        log_error "Assembly包内容不完整"
        exit 1
    fi
    
    local assembly_size=$(du -sh "$assembly_file" | cut -f1)
    log_info "Assembly包大小: $assembly_size"
    
    log_success "打包验证通过"
}

# 创建部署包
create_deployment_package() {
    log_info "创建部署包..."
    
    cd "$BACKEND_DIR"
    
    # 复制assembly包到输出目录
    cp "dist/jshERP-bin.zip" "$BUILD_OUTPUT_DIR/"
    
    # 复制生产环境配置
    cp "src/main/resources/application-production.properties" "$BUILD_OUTPUT_DIR/"
    
    log_success "部署包创建完成: $BUILD_OUTPUT_DIR/jshERP-bin.zip"
}

# 主函数
main() {
    echo "=================================================="
    echo "    jshERP后端打包脚本"
    echo "=================================================="
    echo ""
    
    # 解析参数
    local skip_tests=false
    for arg in "$@"; do
        case $arg in
            --skip-tests)
                skip_tests=true
                ;;
            --help|-h)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  --skip-tests    跳过测试"
                echo "  --help          显示此帮助信息"
                exit 0
                ;;
        esac
    done
    
    # 执行打包流程
    check_environment
    clean_build
    create_production_config
    compile_project
    
    if [ "$skip_tests" = true ]; then
        run_tests --skip-tests
    else
        run_tests
    fi
    
    package_project
    verify_package
    create_deployment_package
    
    echo ""
    echo "=================================================="
    log_success "后端打包完成！"
    echo ""
    echo "构建产物位置:"
    echo "- 部署包: $BUILD_OUTPUT_DIR/jshERP-bin.zip"
    echo "- 配置文件: $BUILD_OUTPUT_DIR/application-production.properties"
    echo ""
    echo "下一步:"
    echo "1. 将jshERP-bin.zip上传到服务器"
    echo "2. 解压到/home/<USER>/jshERP-boot目录"
    echo "3. 配置数据库和Redis连接"
    echo "4. 执行./start.sh启动服务"
    echo "=================================================="
}

# 执行主函数
main "$@"
