#!/bin/bash

# jshERP官方标准部署包构建脚本
# 作者: Augment Code
# 版本: 1.0
# 用途: 生成与官方jshERP3.5-最新包完全一致的部署包结构

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 项目路径
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
SCRIPTS_DIR="$PROJECT_ROOT/scripts"
DIST_DIR="$PROJECT_ROOT/dist"
OFFICIAL_PACKAGE_DIR="$DIST_DIR/jshERP3.5-最新包"

# 构建信息
BUILD_VERSION="3.5.0"
BUILD_DATE=$(date '+%Y-%m-%d %H:%M:%S')

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# 显示构建信息
show_build_info() {
    echo "=================================================="
    echo "    jshERP官方标准部署包构建"
    echo "=================================================="
    echo "版本: $BUILD_VERSION"
    echo "构建时间: $BUILD_DATE"
    echo "目标结构: 与官方jshERP3.5-最新包完全一致"
    echo "=================================================="
    echo ""
}

# 检查构建环境
check_build_environment() {
    log_step "检查构建环境..."
    
    # 检查必要的脚本文件
    local required_scripts=("build-frontend.sh" "build-backend.sh")
    for script in "${required_scripts[@]}"; do
        if [ ! -f "$SCRIPTS_DIR/$script" ]; then
            log_error "构建脚本不存在: $script"
            exit 1
        fi
        chmod +x "$SCRIPTS_DIR/$script"
    done
    
    # 检查项目结构
    if [ ! -d "$PROJECT_ROOT/jshERP-web" ]; then
        log_error "前端项目目录不存在"
        exit 1
    fi
    
    if [ ! -d "$PROJECT_ROOT/jshERP-boot" ]; then
        log_error "后端项目目录不存在"
        exit 1
    fi
    
    log_success "构建环境检查通过"
}

# 清理并创建官方目录结构
create_official_structure() {
    log_step "创建官方标准目录结构..."
    
    # 清理旧的构建目录
    if [ -d "$DIST_DIR" ]; then
        rm -rf "$DIST_DIR"
    fi
    
    # 创建官方标准目录结构
    mkdir -p "$OFFICIAL_PACKAGE_DIR"/{前端包,后端包,nginx配置文件-参考}
    
    log_success "官方目录结构创建完成"
}

# 构建前端项目
build_frontend() {
    log_step "构建前端项目..."
    
    cd "$SCRIPTS_DIR"
    
    # 执行前端构建脚本
    if [ "$1" = "--clean" ]; then
        ./build-frontend.sh --clean
    else
        ./build-frontend.sh
    fi
    
    # 检查构建结果
    if [ ! -f "$DIST_DIR/dist.zip" ]; then
        log_error "前端构建失败，dist.zip不存在"
        exit 1
    fi
    
    # 移动到官方目录结构
    mv "$DIST_DIR/dist.zip" "$OFFICIAL_PACKAGE_DIR/前端包/"
    
    log_success "前端构建完成"
}

# 构建后端项目
build_backend() {
    log_step "构建后端项目..."
    
    cd "$SCRIPTS_DIR"
    
    # 执行后端构建脚本
    if [ "$1" = "--skip-tests" ]; then
        ./build-backend.sh --skip-tests
    else
        ./build-backend.sh
    fi
    
    # 检查构建结果
    if [ ! -f "$DIST_DIR/backend/jshERP-bin.zip" ]; then
        log_error "后端构建失败，jshERP-bin.zip不存在"
        exit 1
    fi
    
    # 移动到官方目录结构
    mv "$DIST_DIR/backend/jshERP-bin.zip" "$OFFICIAL_PACKAGE_DIR/后端包/"
    
    log_success "后端构建完成"
}

# 创建nginx配置文件
create_nginx_config() {
    log_step "创建nginx配置文件..."
    
    # 复制官方nginx配置文件
    if [ -f "$PROJECT_ROOT/jshERP3.5-最新包/nginx配置文件-参考/nginx.conf" ]; then
        cp "$PROJECT_ROOT/jshERP3.5-最新包/nginx配置文件-参考/nginx.conf" "$OFFICIAL_PACKAGE_DIR/nginx配置文件-参考/"
        log_success "已复制官方nginx配置文件"
    else
        # 如果官方文件不存在，创建标准配置
        cat > "$OFFICIAL_PACKAGE_DIR/nginx配置文件-参考/nginx.conf" << 'EOF'

#user   nobody;
worker_processes  1;

#pid    logs/nginx.pid;


events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    index x.html;
    autoindex on;
    server_names_hash_bucket_size 64;
    client_max_body_size 10m;

    sendfile        on;
    keepalive_timeout  65;

    access_log  logs/access.log;

    server {
        listen 3000;

        server_name  localhost;

        gzip on;
        gzip_min_length 100;
        gzip_types text/plain text/css application/xml application/javascript;
        gzip_vary on;
        
        location / {
            root   /home/<USER>/jshERP-web;
            index  index.html index.htm;
            try_files $uri $uri/ /index.html;
        }
	
        location /jshERP-boot/ {
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header REMOTE-HOST $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_pass http://localhost:9999/jshERP-boot/;
        }
    }
}
EOF
        log_success "已创建标准nginx配置文件"
    fi
}

# 创建说明文件
create_readme() {
    log_step "创建说明文件..."
    
    cat > "$OFFICIAL_PACKAGE_DIR/说明.txt" << EOF
1、文件说明
前端包/dist.zip  前端压缩包
后端包/jshERP-bin.zip  后端压缩包
nginx配置文件-参考/nginx.conf  nginx配置参考文件
2、开发说明
前端开发需要用到nodeJS
安装node-v16.16.0-x64.msi或更高版本
3、部署说明
前端部署需要nginx进行代理
后端部署需要jdk1.8、redis、mysql
部署环境建议使用宝塔Linux面板来安装基础软件，服务器环境建议用linux的centOS7.5版本
4、构建信息
构建时间: $BUILD_DATE
构建版本: $BUILD_VERSION
构建工具: jshERP标准化部署方案
EOF
    
    log_success "说明文件创建完成"
}

# 复制用户手册
copy_user_manual() {
    log_step "复制用户手册..."
    
    # 检查是否存在用户手册
    local manual_file="$PROJECT_ROOT/jshERP3.5-最新包/管伊佳ERP-用户手册v3.5.pdf"
    if [ -f "$manual_file" ]; then
        cp "$manual_file" "$OFFICIAL_PACKAGE_DIR/"
        log_success "已复制用户手册"
    else
        log_warning "用户手册文件不存在，跳过复制"
    fi
}

# 验证部署包
verify_package() {
    log_step "验证部署包..."
    
    local errors=0
    
    # 检查必要文件
    local required_files=(
        "$OFFICIAL_PACKAGE_DIR/前端包/dist.zip"
        "$OFFICIAL_PACKAGE_DIR/后端包/jshERP-bin.zip"
        "$OFFICIAL_PACKAGE_DIR/nginx配置文件-参考/nginx.conf"
        "$OFFICIAL_PACKAGE_DIR/说明.txt"
    )
    
    for file in "${required_files[@]}"; do
        if [ -f "$file" ]; then
            log_success "✓ $(basename "$file")"
        else
            log_error "✗ $(basename "$file") 不存在"
            ((errors++))
        fi
    done
    
    # 检查文件大小
    local frontend_size=$(du -sh "$OFFICIAL_PACKAGE_DIR/前端包/dist.zip" 2>/dev/null | cut -f1 || echo "0")
    local backend_size=$(du -sh "$OFFICIAL_PACKAGE_DIR/后端包/jshERP-bin.zip" 2>/dev/null | cut -f1 || echo "0")
    
    log_info "前端包大小: $frontend_size"
    log_info "后端包大小: $backend_size"
    
    if [ $errors -eq 0 ]; then
        log_success "部署包验证通过"
        return 0
    else
        log_error "部署包验证失败，发现 $errors 个错误"
        return 1
    fi
}

# 创建最终压缩包
create_final_package() {
    log_step "创建最终压缩包..."
    
    cd "$DIST_DIR"
    
    # 创建tar.gz压缩包
    tar -czf "jshERP3.5-最新包.tar.gz" "jshERP3.5-最新包/"
    
    # 创建zip压缩包（Windows兼容）
    if command -v zip &> /dev/null; then
        zip -r "jshERP3.5-最新包.zip" "jshERP3.5-最新包/"
        log_info "已创建ZIP格式压缩包"
    fi
    
    # 计算文件大小
    local tar_size=$(du -sh "jshERP3.5-最新包.tar.gz" | cut -f1)
    
    log_success "最终压缩包创建完成"
    log_info "压缩包大小: $tar_size"
}

# 显示构建结果
show_build_result() {
    echo ""
    echo "=================================================="
    log_success "jshERP官方标准部署包构建完成！"
    echo "=================================================="
    echo "构建信息:"
    echo "- 版本: $BUILD_VERSION"
    echo "- 构建时间: $BUILD_DATE"
    echo "- 结构: 与官方jshERP3.5-最新包完全一致"
    echo ""
    echo "生成的文件:"
    echo "- 部署包目录: $OFFICIAL_PACKAGE_DIR"
    echo "- 压缩包: $DIST_DIR/jshERP3.5-最新包.tar.gz"
    if [ -f "$DIST_DIR/jshERP3.5-最新包.zip" ]; then
        echo "- ZIP压缩包: $DIST_DIR/jshERP3.5-最新包.zip"
    fi
    echo ""
    echo "部署包结构:"
    echo "jshERP3.5-最新包/"
    echo "├── 前端包/"
    echo "│   └── dist.zip"
    echo "├── 后端包/"
    echo "│   └── jshERP-bin.zip"
    echo "├── nginx配置文件-参考/"
    echo "│   └── nginx.conf"
    echo "├── 说明.txt"
    if [ -f "$OFFICIAL_PACKAGE_DIR/管伊佳ERP-用户手册v3.5.pdf" ]; then
        echo "└── 管伊佳ERP-用户手册v3.5.pdf"
    fi
    echo ""
    echo "下一步:"
    echo "1. 将压缩包上传到服务器"
    echo "2. 解压并按照说明.txt进行部署"
    echo "3. 使用宝塔面板配置环境"
    echo "=================================================="
}

# 主函数
main() {
    # 解析参数
    local clean_frontend=false
    local skip_tests=false
    
    for arg in "$@"; do
        case $arg in
            --clean)
                clean_frontend=true
                ;;
            --skip-tests)
                skip_tests=true
                ;;
            --help|-h)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  --clean       清理前端依赖后重新构建"
                echo "  --skip-tests  跳过后端测试"
                echo "  --help        显示此帮助信息"
                echo ""
                echo "此脚本将生成与官方jshERP3.5-最新包完全一致的部署包结构"
                exit 0
                ;;
        esac
    done
    
    # 显示构建信息
    show_build_info
    
    # 执行构建流程
    check_build_environment
    create_official_structure
    
    # 构建前端和后端
    if [ "$clean_frontend" = true ]; then
        build_frontend --clean
    else
        build_frontend
    fi
    
    if [ "$skip_tests" = true ]; then
        build_backend --skip-tests
    else
        build_backend
    fi
    
    # 创建配置和文档
    create_nginx_config
    create_readme
    copy_user_manual
    
    # 验证和打包
    verify_package
    create_final_package
    
    # 显示构建结果
    show_build_result
}

# 执行主函数
main "$@"
