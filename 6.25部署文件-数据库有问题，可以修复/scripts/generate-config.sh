#!/bin/bash

# jshERP配置生成脚本
# 作者: Augment Code
# 版本: 1.0
# 用途: 交互式生成生产环境配置文件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 项目路径
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
CONFIG_DIR="$PROJECT_ROOT/config"

# 默认配置值
DEFAULT_DB_HOST="127.0.0.1"
DEFAULT_DB_PORT="3306"
DEFAULT_DB_NAME="jsh_erp"
DEFAULT_DB_USER="jsh_user"
DEFAULT_DB_PASSWORD="123456"
DEFAULT_REDIS_HOST="127.0.0.1"
DEFAULT_REDIS_PORT="6379"
DEFAULT_REDIS_PASSWORD="1234abcd"
DEFAULT_APP_PORT="9999"
DEFAULT_NGINX_PORT="3000"
DEFAULT_DOMAIN="localhost"
DEFAULT_UPLOAD_PATH="/opt/jshERP/upload"
DEFAULT_LOG_PATH="/opt/jshERP/logs"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# 读取用户输入
read_input() {
    local prompt="$1"
    local default="$2"
    local value
    
    if [ -n "$default" ]; then
        read -p "$prompt [$default]: " value
        echo "${value:-$default}"
    else
        read -p "$prompt: " value
        echo "$value"
    fi
}

# 验证IP地址
validate_ip() {
    local ip="$1"
    if [[ $ip =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
        return 0
    else
        return 1
    fi
}

# 验证端口号
validate_port() {
    local port="$1"
    if [[ $port =~ ^[0-9]+$ ]] && [ "$port" -ge 1 ] && [ "$port" -le 65535 ]; then
        return 0
    else
        return 1
    fi
}

# 收集配置信息
collect_config() {
    echo "=================================================="
    echo "    jshERP生产环境配置向导"
    echo "=================================================="
    echo ""
    echo "请输入生产环境配置信息（直接回车使用默认值）："
    echo ""
    
    # 数据库配置
    log_step "数据库配置"
    DB_HOST=$(read_input "数据库主机地址" "$DEFAULT_DB_HOST")
    DB_PORT=$(read_input "数据库端口" "$DEFAULT_DB_PORT")
    DB_NAME=$(read_input "数据库名称" "$DEFAULT_DB_NAME")
    DB_USER=$(read_input "数据库用户名" "$DEFAULT_DB_USER")
    DB_PASSWORD=$(read_input "数据库密码" "$DEFAULT_DB_PASSWORD")
    
    # 验证数据库配置
    if ! validate_port "$DB_PORT"; then
        log_error "数据库端口无效: $DB_PORT"
        exit 1
    fi
    
    echo ""
    
    # Redis配置
    log_step "Redis配置"
    REDIS_HOST=$(read_input "Redis主机地址" "$DEFAULT_REDIS_HOST")
    REDIS_PORT=$(read_input "Redis端口" "$DEFAULT_REDIS_PORT")
    REDIS_PASSWORD=$(read_input "Redis密码" "$DEFAULT_REDIS_PASSWORD")
    
    # 验证Redis配置
    if ! validate_port "$REDIS_PORT"; then
        log_error "Redis端口无效: $REDIS_PORT"
        exit 1
    fi
    
    echo ""
    
    # 应用配置
    log_step "应用配置"
    APP_PORT=$(read_input "应用端口" "$DEFAULT_APP_PORT")
    DOMAIN=$(read_input "域名或IP" "$DEFAULT_DOMAIN")
    NGINX_PORT=$(read_input "Nginx端口" "$DEFAULT_NGINX_PORT")
    
    # 验证应用配置
    if ! validate_port "$APP_PORT"; then
        log_error "应用端口无效: $APP_PORT"
        exit 1
    fi
    
    if ! validate_port "$NGINX_PORT"; then
        log_error "Nginx端口无效: $NGINX_PORT"
        exit 1
    fi
    
    echo ""
    
    # 路径配置
    log_step "路径配置"
    UPLOAD_PATH=$(read_input "文件上传路径" "$DEFAULT_UPLOAD_PATH")
    LOG_PATH=$(read_input "日志文件路径" "$DEFAULT_LOG_PATH")
    
    echo ""
}

# 生成后端配置文件
generate_backend_config() {
    log_step "生成后端配置文件..."
    
    mkdir -p "$CONFIG_DIR"
    
    cat > "$CONFIG_DIR/application-production.properties" << EOF
# jshERP生产环境配置
# 生成时间: $(date '+%Y-%m-%d %H:%M:%S')

# 服务器配置
server.port=$APP_PORT
server.servlet.session.timeout=36000
server.servlet.context-path=/jshERP-boot

# 数据库连接
spring.datasource.url=***************************************************************************************************************************************************************************************************************************************************
spring.datasource.driverClassName=com.mysql.jdbc.Driver
spring.datasource.username=$DB_USER
spring.datasource.password=$DB_PASSWORD

# 连接池配置
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.leak-detection-threshold=60000

# MyBatis配置
mybatis-plus.mapper-locations=classpath:./mapper_xml/*.xml
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.cache-enabled=false
mybatis-plus.configuration.call-setters-on-nulls=true

# Redis配置
spring.redis.host=$REDIS_HOST
spring.redis.port=$REDIS_PORT
spring.redis.password=$REDIS_PASSWORD
spring.redis.database=0
spring.redis.timeout=3000ms
spring.redis.jedis.pool.max-active=50
spring.redis.jedis.pool.max-idle=10
spring.redis.jedis.pool.min-idle=5
spring.redis.jedis.pool.max-wait=-1ms

# 租户配置
manage.roleId=10
tenant.userNumLimit=1000000
tenant.tryDayLimit=3000

# 文件上传配置
file.uploadType=1
file.path=$UPLOAD_PATH
server.tomcat.basedir=/opt/tmp/tomcat
spring.servlet.multipart.max-file-size=10485760
spring.servlet.multipart.max-request-size=10485760

# 日志配置
logging.level.com.jsh.erp=info
logging.level.root=warn
logging.level.org.springframework=warn
logging.level.org.mybatis=warn
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
logging.file.name=$LOG_PATH/jshERP.log
logging.file.max-size=100MB
logging.file.max-history=30

# 性能优化配置
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8

# 安全配置
server.error.include-stacktrace=never
server.error.include-message=never
management.endpoints.enabled-by-default=false
management.endpoint.health.enabled=true
management.endpoint.info.enabled=true

# JVM优化配置（注释供参考）
# -Xms1024m -Xmx2048m -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m
# -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+PrintGCDetails -XX:+PrintGCTimeStamps
EOF
    
    log_success "后端配置文件生成完成: $CONFIG_DIR/application-production.properties"
}

# 生成Nginx配置文件
generate_nginx_config() {
    log_step "生成Nginx配置文件..."
    
    cat > "$CONFIG_DIR/nginx-production.conf" << EOF
# jshERP生产环境Nginx配置
# 生成时间: $(date '+%Y-%m-%d %H:%M:%S')
# 域名: $DOMAIN
# 端口: $NGINX_PORT

server {
    listen $NGINX_PORT;
    server_name $DOMAIN;
    
    # 字符集
    charset utf-8;
    
    # 启用gzip压缩
    gzip on;
    gzip_min_length 1000;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    gzip_vary on;
    gzip_disable "msie6";
    
    # 客户端配置
    client_max_body_size 10m;
    client_body_buffer_size 128k;
    client_header_timeout 30s;
    client_body_timeout 30s;
    
    # 前端静态资源
    location / {
        root /home/<USER>/jshERP-web;
        index index.html index.htm;
        try_files \$uri \$uri/ /index.html;
        
        # 安全头
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        
        # 静态资源缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            access_log off;
        }
        
        # HTML文件不缓存
        location ~* \.html$ {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
        }
    }
    
    # 后端API代理
    location /jshERP-boot/ {
        proxy_pass http://127.0.0.1:$APP_PORT/jshERP-boot/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # 代理超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        
        # WebSocket支持（如果需要）
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # 文件上传代理
    location /jshERP-boot/file/ {
        proxy_pass http://127.0.0.1:$APP_PORT/jshERP-boot/file/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        
        # 大文件上传配置
        client_max_body_size 100m;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    # 访问日志
    access_log $LOG_PATH/nginx_access.log;
    error_log $LOG_PATH/nginx_error.log;
}
EOF
    
    log_success "Nginx配置文件生成完成: $CONFIG_DIR/nginx-production.conf"
}

# 生成环境变量文件
generate_env_file() {
    log_step "生成环境变量文件..."
    
    cat > "$CONFIG_DIR/.env.production" << EOF
# jshERP生产环境变量
# 生成时间: $(date '+%Y-%m-%d %H:%M:%S')

# 数据库配置
DB_HOST=$DB_HOST
DB_PORT=$DB_PORT
DB_NAME=$DB_NAME
DB_USER=$DB_USER
DB_PASSWORD=$DB_PASSWORD

# Redis配置
REDIS_HOST=$REDIS_HOST
REDIS_PORT=$REDIS_PORT
REDIS_PASSWORD=$REDIS_PASSWORD

# 应用配置
APP_PORT=$APP_PORT
APP_CONTEXT_PATH=/jshERP-boot
DOMAIN_NAME=$DOMAIN
NGINX_PORT=$NGINX_PORT

# 路径配置
UPLOAD_PATH=$UPLOAD_PATH
LOG_PATH=$LOG_PATH
TEMP_PATH=/opt/tmp/tomcat

# 构建信息
BUILD_TIME=$(date '+%Y-%m-%d %H:%M:%S')
BUILD_VERSION=3.5.0
EOF
    
    log_success "环境变量文件生成完成: $CONFIG_DIR/.env.production"
}

# 验证配置文件
validate_config() {
    log_step "验证配置文件..."
    
    local errors=0
    
    # 检查配置文件是否存在
    local config_files=(
        "$CONFIG_DIR/application-production.properties"
        "$CONFIG_DIR/nginx-production.conf"
        "$CONFIG_DIR/.env.production"
    )
    
    for file in "${config_files[@]}"; do
        if [ ! -f "$file" ]; then
            log_error "配置文件不存在: $file"
            ((errors++))
        else
            log_info "✓ $file"
        fi
    done
    
    if [ $errors -eq 0 ]; then
        log_success "配置文件验证通过"
        return 0
    else
        log_error "配置文件验证失败，发现 $errors 个错误"
        return 1
    fi
}

# 显示配置摘要
show_config_summary() {
    echo ""
    echo "=================================================="
    log_success "配置生成完成！"
    echo "=================================================="
    echo "配置摘要:"
    echo "- 数据库: $DB_USER@$DB_HOST:$DB_PORT/$DB_NAME"
    echo "- Redis: $REDIS_HOST:$REDIS_PORT"
    echo "- 应用端口: $APP_PORT"
    echo "- Nginx端口: $NGINX_PORT"
    echo "- 域名: $DOMAIN"
    echo "- 上传路径: $UPLOAD_PATH"
    echo "- 日志路径: $LOG_PATH"
    echo ""
    echo "生成的配置文件:"
    echo "- $CONFIG_DIR/application-production.properties"
    echo "- $CONFIG_DIR/nginx-production.conf"
    echo "- $CONFIG_DIR/.env.production"
    echo ""
    echo "下一步:"
    echo "1. 检查配置文件内容"
    echo "2. 根据实际环境调整配置"
    echo "3. 执行部署脚本"
    echo "=================================================="
}

# 主函数
main() {
    # 解析参数
    for arg in "$@"; do
        case $arg in
            --help|-h)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  --help    显示此帮助信息"
                echo ""
                echo "此脚本将交互式生成jshERP生产环境配置文件"
                exit 0
                ;;
        esac
    done
    
    # 执行配置生成流程
    collect_config
    generate_backend_config
    generate_nginx_config
    generate_env_file
    validate_config
    show_config_summary
}

# 执行主函数
main "$@"
