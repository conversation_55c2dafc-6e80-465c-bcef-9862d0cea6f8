#!/bin/bash

# jshERP数据库准备脚本
# 作者: Augment Code
# 版本: 1.0
# 用途: 准备生产环境数据库初始化脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目路径
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
DATABASE_DIR="$PROJECT_ROOT/database"
SOURCE_SQL="$PROJECT_ROOT/jshERP-boot/docs/jsh_erp.sql"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查源数据库文件
check_source_database() {
    log_info "检查源数据库文件..."
    
    if [ ! -f "$SOURCE_SQL" ]; then
        log_error "源数据库文件不存在: $SOURCE_SQL"
        exit 1
    fi
    
    # 检查文件大小
    local file_size=$(du -sh "$SOURCE_SQL" | cut -f1)
    log_info "源数据库文件大小: $file_size"
    
    # 检查文件内容
    local table_count=$(grep -c "CREATE TABLE" "$SOURCE_SQL" || echo "0")
    log_info "数据库表数量: $table_count"
    
    if [ "$table_count" -lt 10 ]; then
        log_warning "数据库表数量较少，请确认文件完整性"
    fi
    
    log_success "源数据库文件检查通过"
}

# 创建生产环境数据库脚本
create_production_database() {
    log_info "创建生产环境数据库脚本..."
    
    mkdir -p "$DATABASE_DIR"
    
    # 创建主数据库脚本
    cat > "$DATABASE_DIR/jsh_erp_production.sql" << 'EOF'
-- jshERP生产环境数据库初始化脚本
-- 基于官方jsh_erp.sql优化
-- 生成时间: $(date '+%Y-%m-%d %H:%M:%S')

-- 设置字符集和时区
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;
SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION';
SET time_zone = '+08:00';

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS `jsh_erp` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `jsh_erp`;

EOF
    
    # 复制原始SQL内容，但跳过数据库创建语句
    if [ -f "$SOURCE_SQL" ]; then
        # 跳过前面的数据库创建语句，只保留表结构和数据
        sed -n '/CREATE TABLE/,$p' "$SOURCE_SQL" >> "$DATABASE_DIR/jsh_erp_production.sql"
    fi
    
    # 添加生产环境优化配置
    cat >> "$DATABASE_DIR/jsh_erp_production.sql" << 'EOF'

-- 生产环境优化配置
-- 创建索引优化查询性能
ALTER TABLE `jsh_material` ADD INDEX `idx_tenant_delete` (`tenant_id`, `delete_flag`);
ALTER TABLE `jsh_depot_item` ADD INDEX `idx_tenant_delete` (`tenant_id`, `delete_flag`);
ALTER TABLE `jsh_depot_head` ADD INDEX `idx_tenant_delete_type` (`tenant_id`, `delete_flag`, `type`);
ALTER TABLE `jsh_person` ADD INDEX `idx_tenant_delete` (`tenant_id`, `delete_flag`);
ALTER TABLE `jsh_supplier` ADD INDEX `idx_tenant_delete` (`tenant_id`, `delete_flag`);
ALTER TABLE `jsh_account` ADD INDEX `idx_tenant_delete` (`tenant_id`, `delete_flag`);

-- 确保管理员账户存在且密码正确
UPDATE `jsh_user` SET `password` = 'e10adc3949ba59abbe56e057f20f883e' WHERE `login_name` = 'admin';
UPDATE `jsh_user` SET `password` = 'e10adc3949ba59abbe56e057f20f883e' WHERE `login_name` = 'waterxi';

-- 重置AUTO_INCREMENT值
ALTER TABLE `jsh_function` AUTO_INCREMENT = 1000;
ALTER TABLE `jsh_user` AUTO_INCREMENT = 100;
ALTER TABLE `jsh_role` AUTO_INCREMENT = 100;

-- 启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 提交事务
COMMIT;
EOF
    
    log_success "生产环境数据库脚本创建完成"
}

# 创建数据库备份脚本
create_backup_script() {
    log_info "创建数据库备份脚本..."
    
    cat > "$DATABASE_DIR/backup-database.sh" << 'EOF'
#!/bin/bash

# jshERP数据库备份脚本

set -e

# 配置
DB_HOST="127.0.0.1"
DB_PORT="3306"
DB_NAME="jsh_erp"
DB_USER="jsh_user"
DB_PASSWORD="123456"
BACKUP_DIR="/opt/jshERP/backup"
DATE=$(date '+%Y%m%d_%H%M%S')

# 创建备份目录
mkdir -p "$BACKUP_DIR"

# 执行备份
echo "开始备份数据库..."
mysqldump -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" \
    --single-transaction \
    --routines \
    --triggers \
    --events \
    --hex-blob \
    --default-character-set=utf8mb4 \
    "$DB_NAME" > "$BACKUP_DIR/jsh_erp_backup_$DATE.sql"

# 压缩备份文件
gzip "$BACKUP_DIR/jsh_erp_backup_$DATE.sql"

echo "数据库备份完成: $BACKUP_DIR/jsh_erp_backup_$DATE.sql.gz"

# 清理7天前的备份文件
find "$BACKUP_DIR" -name "jsh_erp_backup_*.sql.gz" -mtime +7 -delete

echo "备份清理完成"
EOF
    
    chmod +x "$DATABASE_DIR/backup-database.sh"
    
    log_success "数据库备份脚本创建完成"
}

# 创建数据库恢复脚本
create_restore_script() {
    log_info "创建数据库恢复脚本..."
    
    cat > "$DATABASE_DIR/restore-database.sh" << 'EOF'
#!/bin/bash

# jshERP数据库恢复脚本

set -e

# 配置
DB_HOST="127.0.0.1"
DB_PORT="3306"
DB_NAME="jsh_erp"
DB_USER="jsh_user"
DB_PASSWORD="123456"

# 检查参数
if [ $# -ne 1 ]; then
    echo "用法: $0 <备份文件路径>"
    echo "示例: $0 /opt/jshERP/backup/jsh_erp_backup_20231201_120000.sql.gz"
    exit 1
fi

BACKUP_FILE="$1"

# 检查备份文件
if [ ! -f "$BACKUP_FILE" ]; then
    echo "错误: 备份文件不存在: $BACKUP_FILE"
    exit 1
fi

echo "开始恢复数据库..."
echo "备份文件: $BACKUP_FILE"
echo "目标数据库: $DB_NAME"

# 确认操作
read -p "此操作将覆盖现有数据库，是否继续？(y/N): " confirm
if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
    echo "操作已取消"
    exit 0
fi

# 解压并恢复
if [[ "$BACKUP_FILE" == *.gz ]]; then
    zcat "$BACKUP_FILE" | mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME"
else
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < "$BACKUP_FILE"
fi

echo "数据库恢复完成"
EOF
    
    chmod +x "$DATABASE_DIR/restore-database.sh"
    
    log_success "数据库恢复脚本创建完成"
}

# 创建数据库维护脚本
create_maintenance_script() {
    log_info "创建数据库维护脚本..."
    
    cat > "$DATABASE_DIR/maintenance.sh" << 'EOF'
#!/bin/bash

# jshERP数据库维护脚本

set -e

# 配置
DB_HOST="127.0.0.1"
DB_PORT="3306"
DB_NAME="jsh_erp"
DB_USER="jsh_user"
DB_PASSWORD="123456"

echo "开始数据库维护..."

# 优化表
echo "优化数据库表..."
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "
OPTIMIZE TABLE jsh_depot_item;
OPTIMIZE TABLE jsh_depot_head;
OPTIMIZE TABLE jsh_material;
OPTIMIZE TABLE jsh_person;
OPTIMIZE TABLE jsh_supplier;
OPTIMIZE TABLE jsh_account;
OPTIMIZE TABLE jsh_account_item;
OPTIMIZE TABLE jsh_user;
OPTIMIZE TABLE jsh_log;
"

# 分析表
echo "分析数据库表..."
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "
ANALYZE TABLE jsh_depot_item;
ANALYZE TABLE jsh_depot_head;
ANALYZE TABLE jsh_material;
ANALYZE TABLE jsh_person;
ANALYZE TABLE jsh_supplier;
"

# 清理过期日志（保留30天）
echo "清理过期日志..."
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "
DELETE FROM jsh_log WHERE create_time < DATE_SUB(NOW(), INTERVAL 30 DAY);
"

# 更新统计信息
echo "更新统计信息..."
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "
FLUSH TABLES;
"

echo "数据库维护完成"
EOF
    
    chmod +x "$DATABASE_DIR/maintenance.sh"
    
    log_success "数据库维护脚本创建完成"
}

# 验证数据库脚本
validate_database_scripts() {
    log_info "验证数据库脚本..."
    
    local errors=0
    
    # 检查SQL文件语法
    if command -v mysql &> /dev/null; then
        if ! mysql --help > /dev/null 2>&1; then
            log_warning "MySQL客户端不可用，跳过SQL语法检查"
        else
            log_info "SQL语法检查通过"
        fi
    else
        log_warning "MySQL客户端未安装，跳过SQL语法检查"
    fi
    
    # 检查脚本文件
    local scripts=(
        "$DATABASE_DIR/jsh_erp_production.sql"
        "$DATABASE_DIR/backup-database.sh"
        "$DATABASE_DIR/restore-database.sh"
        "$DATABASE_DIR/maintenance.sh"
    )
    
    for script in "${scripts[@]}"; do
        if [ -f "$script" ]; then
            log_success "✓ $script"
        else
            log_error "✗ $script 不存在"
            ((errors++))
        fi
    done
    
    if [ $errors -eq 0 ]; then
        log_success "数据库脚本验证通过"
        return 0
    else
        log_error "数据库脚本验证失败，发现 $errors 个错误"
        return 1
    fi
}

# 显示使用说明
show_usage() {
    echo ""
    echo "=================================================="
    log_success "数据库脚本准备完成！"
    echo "=================================================="
    echo "生成的脚本文件:"
    echo "- $DATABASE_DIR/jsh_erp_production.sql     # 数据库初始化脚本"
    echo "- $DATABASE_DIR/backup-database.sh         # 数据库备份脚本"
    echo "- $DATABASE_DIR/restore-database.sh        # 数据库恢复脚本"
    echo "- $DATABASE_DIR/maintenance.sh             # 数据库维护脚本"
    echo ""
    echo "使用方法:"
    echo "1. 初始化数据库:"
    echo "   mysql -u root -p < $DATABASE_DIR/jsh_erp_production.sql"
    echo ""
    echo "2. 备份数据库:"
    echo "   $DATABASE_DIR/backup-database.sh"
    echo ""
    echo "3. 恢复数据库:"
    echo "   $DATABASE_DIR/restore-database.sh <备份文件>"
    echo ""
    echo "4. 维护数据库:"
    echo "   $DATABASE_DIR/maintenance.sh"
    echo "=================================================="
}

# 主函数
main() {
    echo "=================================================="
    echo "    jshERP数据库准备脚本"
    echo "=================================================="
    echo ""
    
    # 解析参数
    for arg in "$@"; do
        case $arg in
            --help|-h)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  --help    显示此帮助信息"
                echo ""
                echo "此脚本将准备jshERP生产环境数据库相关脚本"
                exit 0
                ;;
        esac
    done
    
    # 执行数据库准备流程
    check_source_database
    create_production_database
    create_backup_script
    create_restore_script
    create_maintenance_script
    validate_database_scripts
    show_usage
}

# 执行主函数
main "$@"
