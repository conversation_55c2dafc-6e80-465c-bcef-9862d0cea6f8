<!-- Generated by SpecStory -->

# 修复claude code的API错误 (2025-06-23 17:13:51)

_**User**_

我正在我们项目中使用claude code，但他会提示这个问题：“API Error: 400 {"type":"error","error":{"type":"invalid_request_error","message
    ":"tools.83.custom.name: String should have at most 64 characters"}}”怎么修复claude code的错误。使用sequentialthinking_Sequential_thinking进行思考
使用interactive_feedback工具报告进度

---

_**Assistant**_



---

[MODE: RESEARCH]

让我使用顺序思维工具来深入分析这个Claude Code API错误问题。

---



---



---



---



---



---



---

现在让我检查项目中的MCP配置文件来定位具体问题：

---



---



---



---



---



---



---



---



---



---



---



---



---



---



---

现在我找到了可能的配置文件！让我检查这些文件：

---



---



---



---

让我再检查一下是否有Cursor专门的配置目录：

---



---



---



---



---

现在让我使用interactive_feedback工具向用户报告我的研究进展：

---



---



---

[MODE: INNOVATE]

基于用户的反馈，让我提供多种解决方案来处理这个Claude Code全局配置问题：

## 🔧 解决方案策略

### 方案一：定位Cursor全局配置
Cursor的全局配置通常位于：
- **macOS**: `~/Library/Application Support/Cursor/User/settings.json`
- **替代位置**: `~/.cursor/settings.json`
- **MCP配置**: `~/.cursor/mcp_servers.json` 或在settings.json中

### 方案二：清理过长的工具名称
1. **查找工具83**: 在配置中定位第83个工具定义
2. **名称缩短**: 将超长名称压缩到64字符内
3. **保持功能性**: 确保缩短后的名称仍具有描述性

### 方案三：临时绕过方案
1. **禁用MCP服务器**: 临时注释掉有问题的配置
2. **分批启用**: 逐步启用MCP服务器，找到问题源
3. **使用别名**: 为长名称创建短别名

### 方案四：配置重置与迁移
1. **备份现有配置**: 保存当前配置以防丢失
2. **重置配置**: 清空或重置Cursor配置
3. **选择性恢复**: 仅恢复必要的配置项

### 方案五：环境变量控制
1. **禁用特定MCP**: 通过环境变量控制MCP服务器加载
2. **调试模式**: 启用详细日志查看具体问题
3. **安全模式**: 以最小配置启动Cursor

## 🎯 推荐执行顺序

我建议先尝试**方案一**定位全局配置，然后根据发现的情况选择**方案二或方案三**进行修复。

您希望我从哪个方案开始？我可以帮您定位并检查Cursor的全局配置文件。

---



---



---



---

[MODE: PLAN]

好的！我将执行**方案四：配置重置与迁移**来解决Claude Code API错误。让我制定详细的执行计划：

## 实施计划：配置重置与迁移

### 准备阶段
1. **定位Cursor全局配置文件**
2. **完整备份现有配置**
3. **确认配置文件位置和内容**

### 执行阶段
4. **重置Cursor配置** 
5. **验证错误是否解决**
6. **选择性恢复必要配置**

### 验证阶段
7. **测试Claude Code功能**
8. **确认所有必要功能正常**
9. **文档化解决方案**

## Implementation Checklist:

1. **查找并备份Cursor全局配置文件**
   - 检查 `~/Library/Application Support/Cursor/User/settings.json`
   - 检查 `~/.cursor/` 目录及其所有配置文件
   - 创建完整配置备份

2. **分析当前配置内容**
   - 识别MCP服务器配置
   - 查找第83个工具定义
   - 记录重要的用户自定义设置

3. **重置配置方法选择**
   - 临时重命名配置文件夹
   - 或者清空配置文件内容
   - 保留备份以便恢复

4. **执行配置重置**
   - 停止Cursor进程
   - 重命名或清空配置文件
   - 重启Cursor验证

5. **测试Claude Code API**
   - 验证API错误是否消失
   - 测试基本Claude Code功能
   - 记录测试结果

6. **选择性配置恢复**
   - 恢复基本用户设置（非MCP相关）
   - 逐步恢复必要的MCP配置
   - 确保工具名称符合64字符限制

7. **验证修复效果**
   - 完整测试Claude Code功能
   - 确认不再出现API 400错误
   - 验证所有必要工具可用

8. **文档化解决方案**
   - 记录问题根因
   - 记录解决步骤
   - 创建预防措施

9. **清理临时文件**
   - 删除不需要的备份文件
   - 整理配置目录
   - 完成最终验证

---



---



---



---



---

