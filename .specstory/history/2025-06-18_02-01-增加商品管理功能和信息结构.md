<!-- Generated by SpecStory -->

# 增加商品管理功能和信息结构 (2025-06-18 10:01:12)

_**User**_

请为商品管理列表页增加2个功能：1、增加列表页多视图2、双击可以直接编辑产品名称、价格、库存（联动库存模块）、尺寸。同时，商品管理模块的图片上传功能修改为：每张图允许20M以内，图片数量20张，允许截图粘贴上传。**产品与服务信息结构：** 管理艺术品序号、条形码、名称、分类、尺寸、单位、**实时库存**等基础信息。详细记录工艺、底胎材质、配饰、包装装裱、产品标签、产品描述（或服务描述）。支持多图片上传。实现多维度价格体系：**成本价（自动核算）**、零售价、渠道价、服务定价。

---

_**Assistant**_



---

[MODE: RESEARCH]

根据您的需求，我需要对jshERP的商品管理模块进行深入研究，了解当前架构和实现方式。让我先收集相关信息，然后为您提供完整的解决方案。

---



---



---



---



---



---

## 对话摘要

本次对话聚焦于 jshERP 系统商品管理模块的功能增强需求。用户提出了以下核心功能要求：

### 🎯 主要功能需求

**1. 商品管理列表页多视图功能**
- 需要为现有商品管理列表页增加多种显示视图选项

**2. 双击编辑功能实现**
- 产品名称双击编辑
- 价格双击编辑  
- 库存双击编辑（需联动库存模块）
- 尺寸双击编辑

**3. 图片上传功能优化**
- 单张图片限制：≤20M
- 图片数量限制：≤20张
- 新增截图粘贴上传功能

### 📋 产品与服务信息架构

**基础信息管理：**
- 艺术品序号、条形码、名称
- 分类、尺寸、单位
- 实时库存显示

**详细信息记录：**
- 工艺、底胎材质、配饰
- 包装装裱、产品标签
- 产品/服务描述
- 多图片上传支持

**多维度价格体系：**
- 成本价（自动核算）
- 零售价、渠道价
- 服务定价

### 🔧 技术特点

这是一个针对 jshERP 成熟 ERP 系统的二次开发需求，需要：
- 遵循最小侵入原则
- 保持多租户架构兼容
- 确保与现有模块的无缝集成
- 维护系统稳定性和数据安全

用户的需求明确且具体，涉及前端交互优化、后端数据处理和文件管理等多个技术层面，是一个综合性的系统功能增强项目。

---

