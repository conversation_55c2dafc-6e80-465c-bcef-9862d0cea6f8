<!-- Generated by SpecStory -->

# 修复jshERP后端启动失败问题 (2025-06-21 17:40:57)

_**User**_

请修复jshERP后端启动失败问题。当前状态分析：

**错误现象**：
Maven Spring Boot插件执行失败，显示以下错误信息：
```
[ERROR] Failed to execute goal org.springframework.boot:spring-boot-maven-plugin:2.0.3.RELEASE:run (default-cli) on project jshERP-boot: Could not exec java: Application finished with exit code: 1
```

**当前进展**：
- ✅ 已成功移除插件主类冲突（CalendarSchedulePluginApplication）
- ✅ 已清理相关配置文件和Mapper扫描
- ✅ Docker容器已重启，Maven依赖下载完成
- ❌ 应用启动仍然失败，退出码为1

**技术要求**：
1. **深度诊断**：使用Maven调试模式（-X或-e参数）获取详细错误堆栈信息
2. **根因分析**：识别导致Java应用退出码1的具体原因（可能是MyBatis配置、依赖冲突、或残留引用）
3. **系统性修复**：基于错误根因制定针对性解决方案
4. **验证标准**：确保API端点 `http://localhost:9999/jshERP-boot/user/randomImage` 返回200状态码

**执行约束**：
- 继续遵循RIPER-5协议（当前处于EXECUTE阶段）
- 保持Docker开发环境配置不变
- 避免影响数据库和Redis服务
- 在每个关键诊断步骤使用interactive_feedback工具报告发现

**优先级操作**：
1. 获取详细的Maven错误日志和Java异常堆栈
2. 检查是否有残留的插件类引用或配置冲突
3. 验证MyBatis映射文件和实体类的完整性
4. 必要时清理Maven缓存和重新编译

请立即开始深度诊断并修复这个启动失败问题。

---

_**Assistant**_



---

好的，我将立即开始对jshERP后端启动失败问题进行深度诊断。

**核心任务**：通过Maven调试模式定位并解决导致应用启动失败（退出码1）的根本原因。

**信心评分 (执行前)**: 8/10 - 问题明确，有清晰的排查路径，但退出码1的可能性较多，需要依赖详细日志来定位。

**首要操作**：我将进入 `jshERP-boot` 目录，并执行带有 `-X` 调试标志的Maven命令，以捕获完整的错误堆栈信息。这是定位问题的关键第一步。

---

```bash
cd jshERP-boot && mvn spring-boot:run -X
```

```
p.apple.com|*.timestamp.apple.com|sequoia.apple.com|*.sequoia.apple.com|seed-sequoia.siri.apple.com|*.seed-s
equoia.siri.apple.com, selenium.version=3.9.1, glassfish-el.version=3.0.0, project.build.sourceEncoding=UTF-
8, user.dir=/Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot, ehcache.version=2.10.4, junit-jupiter.ver
sion=5.1.0, spring-session-bom.version=Apple-SR1, flyway.version=5.0.7, java.specification.version=23, java.
vendor.url=https://github.com/Homebrew/homebrew-core/issues, sun.boot.library.path=/opt/homebrew/Cellar/open
jdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib, sun.java.command=org.codehaus.plexus.classworlds.launcher.
Launcher spring-boot:run -X, jdk.debug=release, maven.version=3.9.9, java.specification.name=Java Platform A
PI Specification, env.HOMEBREW_REPOSITORY=/opt/homebrew, maven-install-plugin.version=2.5.2, byte-buddy.vers
ion=1.7.10, java.runtime.version=23.0.2, env.__CFBundleIdentifier=com.todesktop.230313mzl4w4u92, micrometer.
version=1.0.1, rxjava-adapter.version=1.2.1, java.io.tmpdir=/var/folders/x0/zwc9fkr91wx0965gbdhvmy5c0000gn/T
/, java.version=23.0.2, build-helper-maven-plugin.version=3.0.0, mockito.version=2.15.0, java.vm.specificati
on.name=Java Virtual Machine Specification, infinispan.version=9.1.6.Final, maven-assembly-plugin.version=3.
1.0, java.library.path=/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java
/Extensions:/System/Library/Java/Extensions:/usr/lib/java:., java.vendor=Homebrew, thymeleaf-extras-java8tim
e.version=3.0.1.RELEASE, undertow.version=1.4.22.Final, mariadb.version=2.2.2, thymeleaf-extras-data-attribu
te.version=2.0.1, env.LANG=zh_CN.UTF-8, https.proxyPort=7890, maven.compiler.source=1.8, javax-transaction.v
ersion=1.2, spring.version=5.0.4.RELEASE, reactive-streams.version=1.0.2, querydsl.version=4.1.4, thymeleaf-
layout-dialect.version=2.3.0, commons-pool.version=1.6, java.vm.name=OpenJDK 64-Bit Server VM, env.CURSOR_TR
ACE_ID=39b5d67894004439968cca52c07056a2, maven-help-plugin.version=2.2, json-path.version=2.4.0, os.arch=aar
ch64, env.MallocNanoZone=0, derby.version=10.14.1.0, javax-jsonb.version=1.0, junit-platform.version=1.1.0, 
rxjava2.version=2.1.10, maven-site-plugin.version=3.6, http.proxyPort=7890, env.VSCODE_GIT_ASKPASS_MAIN=/App
lications/Cursor.app/Contents/Resources/app/extensions/git/dist/askpass-main.js, h2.version=1.4.196, env.JAV
A_HOME=/opt/homebrew/opt/openjdk/libexec/openjdk.jdk/Contents/Home, java.vm.compressedOopsMode=Zero based, s
qlite-jdbc.version=********, activemq.version=5.15.3, jtds.version=1.3.1, env.LOGNAME=macmini, spring-securi
ty.version=5.0.3.RELEASE, hibernate-validator.version=6.0.7.Final, assertj.version=3.9.1, influxdb-java.vers
ion=2.9, maven.compiler.target=1.8, commons-pool2.version=2.5.0, spring-retry.version=1.2.2.RELEASE, maven-a
ntrun-plugin.version=1.8, maven.conf=/opt/homebrew/Cellar/maven/3.9.9/libexec/conf, sun.java.launcher=SUN_ST
ANDARD, env.VSCODE_GIT_IPC_HANDLE=/var/folders/x0/zwc9fkr91wx0965gbdhvmy5c0000gn/T/vscode-git-d25f46688f.soc
k, javax-json.version=1.1.2, jboss-logging.version=3.3.2.Final, maven-resources-plugin.version=3.0.1, javax-
validation.version=2.0.1.Final, netty.version=4.1.22.Final, env.VSCODE_GIT_ASKPASS_EXTRA_ARGS=, jetty.versio
n=9.4.8.v20171121, elasticsearch.version=5.6.8, rest-assured.version=3.0.7, log4j2.version=2.10.0, git-commi
t-id-plugin.version=2.2.3, hazelcast-hibernate5.version=1.2.3, htmlunit.version=2.29, jackson.version=2.9.4,
 sun.arch.data.model=64, thymeleaf.version=3.0.9.RELEASE, maven-jar-plugin.version=3.0.2, maven-compiler-plu
gin.version=3.7.0, java.specification.vendor=Oracle Corporation, bitronix.version=2.1.4, rabbit-amqp-client.
version=5.1.2, spring-amqp.version=2.0.2.RELEASE, spring-plugin.version=1.2.0.RELEASE, maven-war-plugin.vers
ion=3.1.0, thymeleaf-extras-springsecurity4.version=3.0.2.RELEASE, user.script=Hans, env.http_proxy=http://1
********:7890, jsonassert.version=1.5.0, file.encoding=UTF-8, nio-multipart-parser.version=1.1.0, env.SHLVL=
1, liquibase.version=3.5.5, httpcore.version=4.4.9, classworlds.conf=/opt/homebrew/Cellar/maven/3.9.9/libexe
c/bin/m2.conf, sun.io.unicode.encoding=UnicodeBig, joda-time.version=2.9.9, env.COMMAND_MODE=unix2003, sendg
rid.version=4.1.2, env.LaunchInstanceID=286D84D2-64D8-404A-A48E-9BAE475E597C, simple-json.version=1.1.1, env
.Q_TERM=1.12.1, http.nonProxyHosts=***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.1
*********/12|1********|localhost|*.localhost|local|*.local|timestamp.apple.com|*.timestamp.apple.com|sequoia
.apple.com|*.sequoia.apple.com|seed-sequoia.siri.apple.com|*.seed-sequoia.siri.apple.com, mongo-driver-react
ivestreams.version=1.7.1, reactor-bom.version=Bismuth-SR7, maven-enforcer-plugin.version=3.0.0-M1, jstl.vers
ion=1.2, quartz.version=2.3.0, env.no_proxy=localhost,1********,::1,*.local,*.lan, spring-hateoas.version=0.
24.0.RELEASE, stdout.encoding=UTF-8, path.separator=:, maven.multiModuleProjectDirectory=/Users/<USER>/Desk
top/jshERP-0612-Cursor/jshERP-boot, env.MAVEN_PROJECTBASEDIR=/Users/<USER>/Desktop/jshERP-0612-Cursor/jshER
P-boot, env.VSCODE_GIT_ASKPASS_NODE=/Applications/Cursor.app/Contents/Frameworks/Cursor Helper (Plugin).app/
Contents/MacOS/Cursor Helper (Plugin), xml-maven-plugin.version=1.0.1, slf4j.version=1.7.25, solr.version=6.
6.2, hikaricp.version=2.7.8, jersey.version=2.26, user.name=macmini, env.PIP_NO_INPUT=true, atomikos.version
=4.0.6, aspectj.version=1.8.13, webjars-locator-core.version=0.35, project.reporting.outputEncoding=UTF-8, x
ml-apis.version=1.4.01, env.INFOPATH=/opt/homebrew/share/info:/opt/homebrew/share/info:, env.OLDPWD=/Users/<USER>
acmini/Desktop/jshERP-0612-Cursor/jshERP-boot, johnzon-jsonb.version=1.1.6, java.vm.vendor=Homebrew, maven-j
avadoc-plugin.version=3.0.0-M1, artemis.version=2.4.0, sun.cpu.endian=little, versions-maven-plugin.version=
2.3, user.language=zh, javax-mail.version=1.6.1, javax-annotation.version=1.3.2, mysql.version=5.1.45, class
mate.version=1.3.4, antlr2.version=2.7.7, env.COMPOSER_NO_INTERACTION=1, jooq.version=3.10.5, java.vendor.ur
l.bug=https://github.com/Homebrew/homebrew-core/issues, java.vm.version=23.0.2, maven-eclipse-plugin.version
=2.10}
[INFO] Using 'UTF-8' encoding to copy filtered resources.
[DEBUG] resource with targetPath null
directory /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources
excludes []
includes [**/application*.yml, **/application*.yaml, **/application*.properties]
[DEBUG] ignoreDelta true
[INFO] Copying 3 resources
[DEBUG] Copying file application-local.properties
[DEBUG] file application-local.properties has a filtered file extension
[DEBUG] filtering /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/application-local
.properties to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/application-local.proper
ties
[DEBUG] Copying file application-docker.properties
[DEBUG] file application-docker.properties has a filtered file extension
[DEBUG] filtering /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/application-docke
r.properties to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/application-docker.prop
erties
[DEBUG] Copying file application.properties
[DEBUG] file application.properties has a filtered file extension
[DEBUG] filtering /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/application.prope
rties to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/application.properties
[DEBUG] resource with targetPath null
directory /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources
excludes [**/application*.yml, **/application*.yaml, **/application*.properties]
includes []
[DEBUG] ignoreDelta true
[INFO] Copying 60 resources
[DEBUG] Copying file mapper_xml/MaterialExtendMapperEx.xml
[DEBUG] file MaterialExtendMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialExt
endMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/MaterialE
xtendMapperEx.xml
[DEBUG] Copying file mapper_xml/FunctionMapperEx.xml
[DEBUG] file FunctionMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/FunctionMap
perEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/FunctionMapperE
x.xml
[DEBUG] Copying file mapper_xml/MaterialExtendMapper.xml
[DEBUG] file MaterialExtendMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialExt
endMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/MaterialExt
endMapper.xml
[DEBUG] Copying file mapper_xml/DepotItemMapperEx.xml
[DEBUG] file DepotItemMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/DepotItemMa
pperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/DepotItemMappe
rEx.xml
[DEBUG] Copying file mapper_xml/UserBusinessMapperEx.xml
[DEBUG] file UserBusinessMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/UserBusines
sMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/UserBusines
sMapperEx.xml
[DEBUG] Copying file mapper_xml/DepotItemMapper.xml
[DEBUG] file DepotItemMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/DepotItemMa
pper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/DepotItemMapper.
xml
[DEBUG] Copying file mapper_xml/RoleMapperEx.xml
[DEBUG] file RoleMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/RoleMapperE
x.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/RoleMapperEx.xml
[DEBUG] Copying file mapper_xml/SerialNumberMapper.xml
[DEBUG] file SerialNumberMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/SerialNumbe
rMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/SerialNumberM
apper.xml
[DEBUG] Copying file mapper_xml/MaterialInitialStockMapper.xml
[DEBUG] file MaterialInitialStockMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialIni
tialStockMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/Mater
ialInitialStockMapper.xml
[DEBUG] Copying file mapper_xml/PersonMapperEx.xml
[DEBUG] file PersonMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/PersonMappe
rEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/PersonMapperEx.xm
l
[DEBUG] Copying file mapper_xml/DepotMapperEx.xml
[DEBUG] file DepotMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/DepotMapper
Ex.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/DepotMapperEx.xml
[DEBUG] Copying file mapper_xml/PlatformConfigMapperEx.xml
[DEBUG] file PlatformConfigMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/PlatformCon
figMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/PlatformC
onfigMapperEx.xml
[DEBUG] Copying file mapper_xml/PlatformConfigMapper.xml
[DEBUG] file PlatformConfigMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/PlatformCon
figMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/PlatformCon
figMapper.xml
[DEBUG] Copying file mapper_xml/MaterialPropertyMapperEx.xml
[DEBUG] file MaterialPropertyMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialPro
pertyMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/Materia
lPropertyMapperEx.xml
[DEBUG] Copying file mapper_xml/InOutItemMapperEx.xml
[DEBUG] file InOutItemMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/InOutItemMa
pperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/InOutItemMappe
rEx.xml
[DEBUG] Copying file mapper_xml/UserBusinessMapper.xml
[DEBUG] file UserBusinessMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/UserBusines
sMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/UserBusinessM
apper.xml
[DEBUG] Copying file mapper_xml/LogMapper.xml
[DEBUG] file LogMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/LogMapper.x
ml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/LogMapper.xml
[DEBUG] Copying file mapper_xml/MaterialInitialStockMapperEx.xml
[DEBUG] file MaterialInitialStockMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialIni
tialStockMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/Mat
erialInitialStockMapperEx.xml
[DEBUG] Copying file mapper_xml/AccountHeadMapperEx.xml
[DEBUG] file AccountHeadMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/AccountHead
MapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/AccountHeadM
apperEx.xml
[DEBUG] Copying file mapper_xml/OrgaUserRelMapperEx.xml
[DEBUG] file OrgaUserRelMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/OrgaUserRel
MapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/OrgaUserRelM
apperEx.xml
[DEBUG] Copying file mapper_xml/SupplierMapper.xml
[DEBUG] file SupplierMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/SupplierMap
per.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/SupplierMapper.xm
l
[DEBUG] Copying file mapper_xml/OrganizationMapper.xml
[DEBUG] file OrganizationMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/Organizatio
nMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/OrganizationM
apper.xml
[DEBUG] Copying file mapper_xml/MaterialPropertyMapper.xml
[DEBUG] file MaterialPropertyMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialPro
pertyMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/MaterialP
ropertyMapper.xml
[DEBUG] Copying file mapper_xml/UserMapperEx.xml
[DEBUG] file UserMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/UserMapperE
x.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/UserMapperEx.xml
[DEBUG] Copying file mapper_xml/AccountMapperEx.xml
[DEBUG] file AccountMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/AccountMapp
erEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/AccountMapperEx.
xml
[DEBUG] Copying file mapper_xml/SupplierMapperEx.xml
[DEBUG] file SupplierMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/SupplierMap
perEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/SupplierMapperE
x.xml
[DEBUG] Copying file mapper_xml/AccountMapper.xml
[DEBUG] file AccountMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/AccountMapp
er.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/AccountMapper.xml
[DEBUG] Copying file mapper_xml/SystemConfigMapperEx.xml
[DEBUG] file SystemConfigMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/SystemConfi
gMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/SystemConfi
gMapperEx.xml
[DEBUG] Copying file mapper_xml/AccountHeadMapper.xml
[DEBUG] file AccountHeadMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/AccountHead
Mapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/AccountHeadMap
per.xml
[DEBUG] Copying file mapper_xml/MaterialMapper.xml
[DEBUG] file MaterialMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialMap
per.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/MaterialMapper.xm
l
[DEBUG] Copying file mapper_xml/UserMapper.xml
[DEBUG] file UserMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/UserMapper.
xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/UserMapper.xml
[DEBUG] Copying file mapper_xml/MaterialMapperEx.xml
[DEBUG] file MaterialMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialMap
perEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/MaterialMapperE
x.xml
[DEBUG] Copying file mapper_xml/MaterialCategoryMapperEx.xml
[DEBUG] file MaterialCategoryMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialCat
egoryMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/Materia
lCategoryMapperEx.xml
[DEBUG] Copying file mapper_xml/SystemConfigMapper.xml
[DEBUG] file SystemConfigMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/SystemConfi
gMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/SystemConfigM
apper.xml
[DEBUG] Copying file mapper_xml/MaterialAttributeMapper.xml
[DEBUG] file MaterialAttributeMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialAtt
ributeMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/Material
AttributeMapper.xml
[DEBUG] Copying file mapper_xml/LogMapperEx.xml
[DEBUG] file LogMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/LogMapperEx
.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/LogMapperEx.xml
[DEBUG] Copying file mapper_xml/AccountItemMapper.xml
[DEBUG] file AccountItemMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/AccountItem
Mapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/AccountItemMap
per.xml
[DEBUG] Copying file mapper_xml/MaterialCurrentStockMapper.xml
[DEBUG] file MaterialCurrentStockMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialCur
rentStockMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/Mater
ialCurrentStockMapper.xml
[DEBUG] Copying file mapper_xml/MsgMapper.xml
[DEBUG] file MsgMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MsgMapper.x
ml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/MsgMapper.xml
[DEBUG] Copying file mapper_xml/AccountItemMapperEx.xml
[DEBUG] file AccountItemMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/AccountItem
MapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/AccountItemM
apperEx.xml
[DEBUG] Copying file mapper_xml/FunctionMapper.xml
[DEBUG] file FunctionMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/FunctionMap
per.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/FunctionMapper.xm
l
[DEBUG] Copying file mapper_xml/MsgMapperEx.xml
[DEBUG] file MsgMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MsgMapperEx
.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/MsgMapperEx.xml
[DEBUG] Copying file mapper_xml/DepotMapper.xml
[DEBUG] file DepotMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/DepotMapper
.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/DepotMapper.xml
[DEBUG] Copying file mapper_xml/PersonMapper.xml
[DEBUG] file PersonMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/PersonMappe
r.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/PersonMapper.xml
[DEBUG] Copying file mapper_xml/UnitMapperEx.xml
[DEBUG] file UnitMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/UnitMapperE
x.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/UnitMapperEx.xml
[DEBUG] Copying file mapper_xml/MaterialCategoryMapper.xml
[DEBUG] file MaterialCategoryMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialCat
egoryMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/MaterialC
ategoryMapper.xml
[DEBUG] Copying file mapper_xml/OrganizationMapperEx.xml
[DEBUG] file OrganizationMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/Organizatio
nMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/Organizatio
nMapperEx.xml
[DEBUG] Copying file mapper_xml/SerialNumberMapperEx.xml
[DEBUG] file SerialNumberMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/SerialNumbe
rMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/SerialNumbe
rMapperEx.xml
[DEBUG] Copying file mapper_xml/OrgaUserRelMapper.xml
[DEBUG] file OrgaUserRelMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/OrgaUserRel
Mapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/OrgaUserRelMap
per.xml
[DEBUG] Copying file mapper_xml/TenantMapperEx.xml
[DEBUG] file TenantMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/TenantMappe
rEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/TenantMapperEx.xm
l
[DEBUG] Copying file mapper_xml/UnitMapper.xml
[DEBUG] file UnitMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/UnitMapper.
xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/UnitMapper.xml
[DEBUG] Copying file mapper_xml/SequenceMapperEx.xml
[DEBUG] file SequenceMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/SequenceMap
perEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/SequenceMapperE
x.xml
[DEBUG] Copying file mapper_xml/TenantMapper.xml
[DEBUG] file TenantMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/TenantMappe
r.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/TenantMapper.xml
[DEBUG] Copying file mapper_xml/MaterialAttributeMapperEx.xml
[DEBUG] file MaterialAttributeMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialAtt
ributeMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/Materi
alAttributeMapperEx.xml
[DEBUG] Copying file mapper_xml/InOutItemMapper.xml
[DEBUG] file InOutItemMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/InOutItemMa
pper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/InOutItemMapper.
xml
[DEBUG] Copying file mapper_xml/MaterialCurrentStockMapperEx.xml
[DEBUG] file MaterialCurrentStockMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialCur
rentStockMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/Mat
erialCurrentStockMapperEx.xml
[DEBUG] Copying file mapper_xml/DepotHeadMapper.xml
[DEBUG] file DepotHeadMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/DepotHeadMa
pper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/DepotHeadMapper.
xml
[DEBUG] Copying file mapper_xml/RoleMapper.xml
[DEBUG] file RoleMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/RoleMapper.
xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/RoleMapper.xml
[DEBUG] Copying file mapper_xml/DepotHeadMapperEx.xml
[DEBUG] file DepotHeadMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/DepotHeadMa
pperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/DepotHeadMappe
rEx.xml
[DEBUG] Copying file logback-spring.xml
[DEBUG] file logback-spring.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/logback-spring.xml to 
/Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/logback-spring.xml
[DEBUG] no use filter components
[INFO] 
[INFO] --- compiler:3.7.0:compile (default-compile) @ jshERP-boot ---
[DEBUG] Dependency collection stats {ConflictMarker.analyzeTime=60500, ConflictMarker.markTime=26083, Confli
ctMarker.nodeCount=118, ConflictIdSorter.graphTime=17416, ConflictIdSorter.topsortTime=21334, ConflictIdSort
er.conflictIdCount=45, ConflictIdSorter.conflictIdCycleCount=0, ConflictResolver.totalTime=235541, ConflictR
esolver.conflictItemCount=72, DfDependencyCollector.collectTime=58956125, DfDependencyCollector.transformTim
e=375000}
[DEBUG] org.apache.maven.plugins:maven-compiler-plugin:jar:3.7.0
[DEBUG]    org.apache.maven:maven-plugin-api:jar:3.0:compile
[DEBUG]       org.apache.maven:maven-model:jar:3.0:compile
[DEBUG]       org.sonatype.sisu:sisu-inject-plexus:jar:1.4.2:compile
[DEBUG]          org.sonatype.sisu:sisu-inject-bean:jar:1.4.2:compile
[DEBUG]             org.sonatype.sisu:sisu-guice:jar:noaop:2.1.7:compile
[DEBUG]    org.apache.maven:maven-artifact:jar:3.0:compile
[DEBUG]       org.codehaus.plexus:plexus-utils:jar:2.0.4:compile
[DEBUG]    org.apache.maven:maven-core:jar:3.0:compile
[DEBUG]       org.apache.maven:maven-settings:jar:3.0:compile
[DEBUG]       org.apache.maven:maven-settings-builder:jar:3.0:compile
[DEBUG]       org.apache.maven:maven-repository-metadata:jar:3.0:compile
[DEBUG]       org.apache.maven:maven-model-builder:jar:3.0:compile
[DEBUG]       org.apache.maven:maven-aether-provider:jar:3.0:runtime
[DEBUG]       org.sonatype.aether:aether-impl:jar:1.7:compile
[DEBUG]          org.sonatype.aether:aether-spi:jar:1.7:compile
[DEBUG]       org.sonatype.aether:aether-api:jar:1.7:compile
[DEBUG]       org.sonatype.aether:aether-util:jar:1.7:compile
[DEBUG]       org.codehaus.plexus:plexus-interpolation:jar:1.14:compile
[DEBUG]       org.codehaus.plexus:plexus-classworlds:jar:2.2.3:compile
[DEBUG]       org.codehaus.plexus:plexus-component-annotations:jar:1.6:compile (version managed from default
)
[DEBUG]       org.sonatype.plexus:plexus-sec-dispatcher:jar:1.3:compile
[DEBUG]          org.sonatype.plexus:plexus-cipher:jar:1.4:compile
[DEBUG]    org.apache.maven.shared:maven-shared-utils:jar:3.1.0:compile
[DEBUG]       commons-io:commons-io:jar:2.5:compile
[DEBUG]    org.apache.maven.shared:maven-shared-incremental:jar:1.1:compile
[DEBUG]    org.codehaus.plexus:plexus-java:jar:0.9.2:compile
[DEBUG]       org.ow2.asm:asm:jar:6.0_BETA:compile
[DEBUG]       com.thoughtworks.qdox:qdox:jar:2.0-M7:compile
[DEBUG]    org.codehaus.plexus:plexus-compiler-api:jar:2.8.2:compile
[DEBUG]    org.codehaus.plexus:plexus-compiler-manager:jar:2.8.2:compile
[DEBUG]    org.codehaus.plexus:plexus-compiler-javac:jar:2.8.2:runtime
[DEBUG] Created new class realm plugin>org.apache.maven.plugins:maven-compiler-plugin:3.7.0
[DEBUG] Importing foreign packages into class realm plugin>org.apache.maven.plugins:maven-compiler-plugin:3.
7.0
[DEBUG]   Imported:  < maven.api
[DEBUG] Populating class realm plugin>org.apache.maven.plugins:maven-compiler-plugin:3.7.0
[DEBUG]   Included: org.apache.maven.plugins:maven-compiler-plugin:jar:3.7.0
[DEBUG]   Included: org.sonatype.sisu:sisu-inject-bean:jar:1.4.2
[DEBUG]   Included: org.sonatype.sisu:sisu-guice:jar:noaop:2.1.7
[DEBUG]   Included: org.codehaus.plexus:plexus-utils:jar:2.0.4
[DEBUG]   Included: org.sonatype.aether:aether-util:jar:1.7
[DEBUG]   Included: org.codehaus.plexus:plexus-interpolation:jar:1.14
[DEBUG]   Included: org.codehaus.plexus:plexus-component-annotations:jar:1.6
[DEBUG]   Included: org.sonatype.plexus:plexus-sec-dispatcher:jar:1.3
[DEBUG]   Included: org.sonatype.plexus:plexus-cipher:jar:1.4
[DEBUG]   Included: org.apache.maven.shared:maven-shared-utils:jar:3.1.0
[DEBUG]   Included: commons-io:commons-io:jar:2.5
[DEBUG]   Included: org.apache.maven.shared:maven-shared-incremental:jar:1.1
[DEBUG]   Included: org.codehaus.plexus:plexus-java:jar:0.9.2
[DEBUG]   Included: org.ow2.asm:asm:jar:6.0_BETA
[DEBUG]   Included: com.thoughtworks.qdox:qdox:jar:2.0-M7
[DEBUG]   Included: org.codehaus.plexus:plexus-compiler-api:jar:2.8.2
[DEBUG]   Included: org.codehaus.plexus:plexus-compiler-manager:jar:2.8.2
[DEBUG]   Included: org.codehaus.plexus:plexus-compiler-javac:jar:2.8.2
[DEBUG] Loading mojo org.apache.maven.plugins:maven-compiler-plugin:3.7.0:compile from plugin realm ClassRea
lm[plugin>org.apache.maven.plugins:maven-compiler-plugin:3.7.0, parent: jdk.internal.loader.ClassLoaders$App
ClassLoader@18ff02e4]
[DEBUG] Configuring mojo execution 'org.apache.maven.plugins:maven-compiler-plugin:3.7.0:compile:default-com
pile' with basic configurator -->
[DEBUG]   (s) groupId = org.projectlombok
[DEBUG]   (s) artifactId = lombok
[DEBUG]   (s) version = 1.18.30
[DEBUG]   (f) annotationProcessorPaths = [org.projectlombok:lombok:1.18.30.jar]
[DEBUG]   (f) basedir = /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot
[DEBUG]   (f) buildDirectory = /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target
[DEBUG]   (f) compilePath = [/Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes, /Users/<USER>
cmini/.m2/repository/com/gitee/starblues/springboot-plugin-framework/2.2.1-RELEASE/springboot-plugin-framewo
rk-2.2.1-RELEASE.jar, /Users/<USER>/.m2/repository/org/pf4j/pf4j/3.1.0/pf4j-3.1.0.jar, /Users/<USER>/.m2/r
epository/com/github/zafarkhaja/java-semver/0.9.0/java-semver-0.9.0.jar, /Users/<USER>/.m2/repository/com/f
asterxml/jackson/core/jackson-databind/2.9.4/jackson-databind-2.9.4.jar, /Users/<USER>/.m2/repository/com/f
asterxml/jackson/core/jackson-annotations/2.9.0/jackson-annotations-2.9.0.jar, /Users/<USER>/.m2/repository
/com/fasterxml/jackson/core/jackson-core/2.9.4/jackson-core-2.9.4.jar, /Users/<USER>/.m2/repository/com/fas
terxml/jackson/dataformat/jackson-dataformat-yaml/2.9.4/jackson-dataformat-yaml-2.9.4.jar, /Users/<USER>/.m
2/repository/org/yaml/snakeyaml/1.19/snakeyaml-1.19.jar, /Users/<USER>/.m2/repository/com/gitee/starblues/s
pringboot-plugin-framework-extension-mybatis/2.2.1-RELEASE/springboot-plugin-framework-extension-mybatis-2.2
.1-RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.0.0.RELEASE
/spring-boot-starter-web-2.0.0.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/boot/spring-bo
ot-starter/2.0.0.RELEASE/spring-boot-starter-2.0.0.RELEASE.jar, /Users/<USER>/.m2/repository/org/springfram
ework/boot/spring-boot/2.0.0.RELEASE/spring-boot-2.0.0.RELEASE.jar, /Users/<USER>/.m2/repository/org/spring
framework/boot/spring-boot-starter-logging/2.0.0.RELEASE/spring-boot-starter-logging-2.0.0.RELEASE.jar, /Use
rs/macmini/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, /Users/<USER>/.m2
/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, /Users/<USER>/.m2/repository/javax/an
notation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2.jar, /Users/<USER>/.m2/repository/org/spring
framework/boot/spring-boot-starter-json/2.0.0.RELEASE/spring-boot-starter-json-2.0.0.RELEASE.jar, /Users/<USER>
mini/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.9.4/jackson-datatype-jdk8-2.9.4.j
ar, /Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.9.4/jackson-datat
ype-jsr310-2.9.4.jar, /Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-na
mes/2.9.4/jackson-module-parameter-names-2.9.4.jar, /Users/<USER>/.m2/repository/org/springframework/boot/s
pring-boot-starter-tomcat/2.0.0.RELEASE/spring-boot-starter-tomcat-2.0.0.RELEASE.jar, /Users/<USER>/.m2/rep
ository/org/apache/tomcat/embed/tomcat-embed-core/8.5.28/tomcat-embed-core-8.5.28.jar, /Users/<USER>/.m2/re
pository/org/apache/tomcat/embed/tomcat-embed-el/8.5.28/tomcat-embed-el-8.5.28.jar, /Users/<USER>/.m2/repos
itory/org/apache/tomcat/embed/tomcat-embed-websocket/8.5.28/tomcat-embed-websocket-8.5.28.jar, /Users/<USER>
i/.m2/repository/org/hibernate/validator/hibernate-validator/6.0.7.Final/hibernate-validator-6.0.7.Final.jar
, /Users/<USER>/.m2/repository/javax/validation/validation-api/2.0.1.Final/validation-api-2.0.1.Final.jar, 
/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.3.2.Final/jboss-logging-3.3.2.Final.jar, /Us
ers/macmini/.m2/repository/org/springframework/spring-web/5.0.4.RELEASE/spring-web-5.0.4.RELEASE.jar, /Users
/macmini/.m2/repository/org/springframework/spring-beans/5.0.4.RELEASE/spring-beans-5.0.4.RELEASE.jar, /User
s/macmini/.m2/repository/org/springframework/spring-webmvc/5.0.4.RELEASE/spring-webmvc-5.0.4.RELEASE.jar, /U
sers/macmini/.m2/repository/org/springframework/spring-aop/5.0.4.RELEASE/spring-aop-5.0.4.RELEASE.jar, /User
s/macmini/.m2/repository/org/springframework/spring-context/5.0.4.RELEASE/spring-context-5.0.4.RELEASE.jar, 
/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.0.4.RELEASE/spring-expression-5.0.4.RE
LEASE.jar, /Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.7.10/byte-buddy-1.7.10.jar, /Users/<USER>
ni/.m2/repository/org/springframework/spring-core/5.0.4.RELEASE/spring-core-5.0.4.RELEASE.jar, /Users/<USER>
i/.m2/repository/org/springframework/spring-jcl/5.0.4.RELEASE/spring-jcl-5.0.4.RELEASE.jar, /Users/<USER>/.
m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar, /Users/<USER>/.m2/repository/mysql/mysql-con
nector-java/8.0.28/mysql-connector-java-8.0.28.jar, /Users/<USER>/.m2/repository/com/google/protobuf/protob
uf-java/3.11.4/protobuf-java-3.11.4.jar, /Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/
4.5.2/httpclient-4.5.2.jar, /Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.9/httpcore-
4.4.9.jar, /Users/<USER>/.m2/repository/commons-codec/commons-codec/1.11/commons-codec-1.11.jar, /Users/<USER>
mini/.m2/repository/net/sourceforge/jexcelapi/jxl/2.6.12/jxl-2.6.12.jar, /Users/<USER>/.m2/repository/log4j
/log4j/1.2.14/log4j-1.2.14.jar, /Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.30/lombok-1.18.3
0.jar, /Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.15.0/log4j-to-slf4j-2.15.0.ja
r, /Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.25/slf4j-api-1.7.25.jar, /Users/<USER>/.m2/reposit
ory/org/apache/logging/log4j/log4j-api/2.10.0/log4j-api-2.10.0.jar, /Users/<USER>/.m2/repository/org/slf4j/
jul-to-slf4j/1.7.25/jul-to-slf4j-1.7.25.jar, /Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-st
arter/3.0.7.1/mybatis-plus-boot-starter-3.0.7.1.jar, /Users/<USER>/.m2/repository/com/baomidou/mybatis-plus
/3.0.7.1/mybatis-plus-3.0.7.1.jar, /Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.0.7.1
/mybatis-plus-extension-3.0.7.1.jar, /Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.0.7.1/my
batis-plus-core-3.0.7.1.jar, /Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.0.7.1/myba
tis-plus-annotation-3.0.7.1.jar, /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconf
igure/2.0.0.RELEASE/spring-boot-autoconfigure-2.0.0.RELEASE.jar, /Users/<USER>/.m2/repository/org/springfra
mework/boot/spring-boot-starter-jdbc/2.0.0.RELEASE/spring-boot-starter-jdbc-2.0.0.RELEASE.jar, /Users/<USER>
i/.m2/repository/com/zaxxer/HikariCP/2.7.8/HikariCP-2.7.8.jar, /Users/<USER>/.m2/repository/org/springframe
work/spring-jdbc/5.0.4.RELEASE/spring-jdbc-5.0.4.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframew
ork/boot/spring-boot-starter-redis/1.4.1.RELEASE/spring-boot-starter-redis-1.4.1.RELEASE.jar, /Users/<USER>
/.m2/repository/org/springframework/data/spring-data-redis/2.0.5.RELEASE/spring-data-redis-2.0.5.RELEASE.jar
, /Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.0.5.RELEASE/spring-data-keyv
alue-2.0.5.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.0.5.REL
EASE/spring-data-commons-2.0.5.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/spring-tx/5.0.
4.RELEASE/spring-tx-5.0.4.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/spring-oxm/5.0.4.RE
LEASE/spring-oxm-5.0.4.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/spring-context-support
/5.0.4.RELEASE/spring-context-support-5.0.4.RELEASE.jar, /Users/<USER>/.m2/repository/redis/clients/jedis/2
.9.0/jedis-2.9.0.jar, /Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.5.0/commons-pool2-2.5
.0.jar, /Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/2.7.0/springfox-swagger2-2.7.0.jar, /U
sers/macmini/.m2/repository/io/swagger/swagger-annotations/1.5.13/swagger-annotations-1.5.13.jar, /Users/<USER>
mini/.m2/repository/io/swagger/swagger-models/1.5.13/swagger-models-1.5.13.jar, /Users/<USER>/.m2/repositor
y/io/springfox/springfox-spi/2.7.0/springfox-spi-2.7.0.jar, /Users/<USER>/.m2/repository/io/springfox/sprin
gfox-core/2.7.0/springfox-core-2.7.0.jar, /Users/<USER>/.m2/repository/io/springfox/springfox-schema/2.7.0/
springfox-schema-2.7.0.jar, /Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/2.7.0/spring
fox-swagger-common-2.7.0.jar, /Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/2.7.0/springfo
x-spring-web-2.7.0.jar, /Users/<USER>/.m2/repository/org/reflections/reflections/0.9.11/reflections-0.9.11.
jar, /Users/<USER>/.m2/repository/org/javassist/javassist/3.21.0-GA/javassist-3.21.0-GA.jar, /Users/<USER>
/.m2/repository/com/google/guava/guava/18.0/guava-18.0.jar, /Users/<USER>/.m2/repository/com/fasterxml/clas
smate/1.3.4/classmate-1.3.4.jar, /Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core
/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/plugi
n/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar, /Users/<USER>/.m2/reposito
ry/org/mapstruct/mapstruct/1.1.0.Final/mapstruct-1.1.0.Final.jar, /Users/<USER>/.m2/repository/com/github/x
iaoymin/swagger-bootstrap-ui/1.6/swagger-bootstrap-ui-1.6.jar, /Users/<USER>/.m2/repository/com/aliyun/oss/
aliyun-sdk-oss/3.10.1/aliyun-sdk-oss-3.10.1.jar, /Users/<USER>/.m2/repository/org/jdom/jdom/1.1/jdom-1.1.ja
r, /Users/<USER>/.m2/repository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar, /Users/<USER>/.m2/rep
ository/stax/stax-api/1.0.1/stax-api-1.0.1.jar, /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-cor
e/3.4.0/aliyun-java-sdk-core-3.4.0.jar, /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ram/3.0.0/a
liyun-java-sdk-ram-3.0.0.jar, /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java
-sdk-sts-3.0.0.jar, /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4
.2.0.jar, /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-kms/2.7.0/aliyun-java-sdk-kms-2.7.0.jar, 
/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.2/gson-2.8.2.jar, /Users/<USER>/.m2/repository/
com/itextpdf/itextpdf/5.5.13.1/itextpdf-5.5.13.1.jar, /Users/<USER>/.m2/repository/commons-io/commons-io/1.
3.2/commons-io-1.3.2.jar, /Users/<USER>/.m2/repository/com/belerweb/pinyin4j/2.5.1/pinyin4j-2.5.1.jar, /Use
rs/macmini/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-starter/1.2.13/pagehelper-spring-boot
-starter-1.2.13.jar, /Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.1.1
/mybatis-spring-boot-starter-2.1.1.jar, /Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring
-boot-autoconfigure/2.1.1/mybatis-spring-boot-autoconfigure-2.1.1.jar, /Users/<USER>/.m2/repository/org/myb
atis/mybatis/3.5.3/mybatis-3.5.3.jar, /Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.0.3/mybatis
-spring-2.0.3.jar, /Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-autoconfigure/
1.2.13/pagehelper-spring-boot-autoconfigure-1.2.13.jar, /Users/<USER>/.m2/repository/com/github/pagehelper/
pagehelper/5.1.11/pagehelper-5.1.11.jar, /Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/2.0/
jsqlparser-2.0.jar]
[DEBUG]   (f) compileSourceRoots = [/Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java]
[DEBUG]   (f) compilerId = javac
[DEBUG]   (f) debug = true
[DEBUG]   (f) encoding = UTF-8
[DEBUG]   (f) failOnError = true
[DEBUG]   (f) failOnWarning = false
[DEBUG]   (f) forceJavacCompilerUse = false
[DEBUG]   (f) fork = false
[DEBUG]   (f) generatedSourcesDirectory = /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/gener
ated-sources/annotations
[DEBUG]   (f) mojoExecution = org.apache.maven.plugins:maven-compiler-plugin:3.7.0:compile {execution: defau
lt-compile}
[DEBUG]   (f) optimize = false
[DEBUG]   (f) outputDirectory = /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes
[DEBUG]   (f) parameters = true
[DEBUG]   (f) project = MavenProject: com.jsh:jshERP-boot:3.5-SNAPSHOT @ /Users/<USER>/Desktop/jshERP-0612-
Cursor/jshERP-boot/pom.xml
[DEBUG]   (f) projectArtifact = com.jsh:jshERP-boot:jar:3.5-SNAPSHOT
[DEBUG]   (f) session = org.apache.maven.execution.MavenSession@3a401749
[DEBUG]   (f) showDeprecation = false
[DEBUG]   (f) showWarnings = false
[DEBUG]   (f) skipMultiThreadWarning = false
[DEBUG]   (f) source = 1.8
[DEBUG]   (f) staleMillis = 0
[DEBUG]   (f) target = 1.8
[DEBUG]   (f) useIncrementalCompilation = true
[DEBUG]   (f) verbose = false
[DEBUG] -- end configuration --
[DEBUG] Using compiler 'javac'.
[DEBUG] Adding /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/generated-sources/annotations to
 compile source roots:
  /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java
[DEBUG] New compile source roots:
  /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java
  /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/generated-sources/annotations
[DEBUG] CompilerReuseStrategy: reuseCreated
[DEBUG] useIncrementalCompilation enabled
[INFO] Changes detected - recompiling the module!
[DEBUG] Classpath:
[DEBUG]  /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes
[DEBUG]  /Users/<USER>/.m2/repository/com/gitee/starblues/springboot-plugin-framework/2.2.1-RELEASE/springb
oot-plugin-framework-2.2.1-RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/pf4j/pf4j/3.1.0/pf4j-3.1.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/github/zafarkhaja/java-semver/0.9.0/java-semver-0.9.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.9.4/jackson-databind-2.
9.4.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.9.0/jackson-annotati
ons-2.9.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.9.4/jackson-core-2.9.4.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.9.4/jackso
n-dataformat-yaml-2.9.4.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.19/snakeyaml-1.19.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/gitee/starblues/springboot-plugin-framework-extension-mybatis/2.2
.1-RELEASE/springboot-plugin-framework-extension-mybatis-2.2.1-RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.0.0.RELEASE/spring
-boot-starter-web-2.0.0.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.0.0.RELEASE/spring-boo
t-starter-2.0.0.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.0.0.RELEASE/spring-boot-2.0.0.
RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.0.0.RELEASE/sp
ring-boot-starter-logging-2.0.0.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar
[DEBUG]  /Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar
[DEBUG]  /Users/<USER>/.m2/repository/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.
2.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.0.0.RELEASE/sprin
g-boot-starter-json-2.0.0.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.9.4/jackson-da
tatype-jdk8-2.9.4.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.9.4/jackson-
datatype-jsr310-2.9.4.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.9.4/jac
kson-module-parameter-names-2.9.4.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.0.0.RELEASE/spr
ing-boot-starter-tomcat-2.0.0.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/8.5.28/tomcat-embed-core-8.
5.28.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/8.5.28/tomcat-embed-el-8.5.28
.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/8.5.28/tomcat-embed-we
bsocket-8.5.28.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.0.7.Final/hibernate-val
idator-6.0.7.Final.jar
[DEBUG]  /Users/<USER>/.m2/repository/javax/validation/validation-api/2.0.1.Final/validation-api-2.0.1.Fina
l.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.3.2.Final/jboss-logging-3.3.2.Final
.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-web/5.0.4.RELEASE/spring-web-5.0.4.RELEASE
.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-beans/5.0.4.RELEASE/spring-beans-5.0.4.REL
EASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.0.4.RELEASE/spring-webmvc-5.0.4.R
ELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-aop/5.0.4.RELEASE/spring-aop-5.0.4.RELEASE
.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-context/5.0.4.RELEASE/spring-context-5.0.4
.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-expression/5.0.4.RELEASE/spring-expression
-5.0.4.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.7.10/byte-buddy-1.7.10.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-core/5.0.4.RELEASE/spring-core-5.0.4.RELEA
SE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.0.4.RELEASE/spring-jcl-5.0.4.RELEASE
.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar
[DEBUG]  /Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.28/mysql-connector-java-8.0.28.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.11.4/protobuf-java-3.11.4.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.2/httpclient-4.5.2.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.9/httpcore-4.4.9.jar
[DEBUG]  /Users/<USER>/.m2/repository/commons-codec/commons-codec/1.11/commons-codec-1.11.jar
[DEBUG]  /Users/<USER>/.m2/repository/net/sourceforge/jexcelapi/jxl/2.6.12/jxl-2.6.12.jar
[DEBUG]  /Users/<USER>/.m2/repository/log4j/log4j/1.2.14/log4j-1.2.14.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.30/lombok-1.18.30.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.15.0/log4j-to-slf4j-2.15.0.
jar
[DEBUG]  /Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.25/slf4j-api-1.7.25.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.10.0/log4j-api-2.10.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.25/jul-to-slf4j-1.7.25.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.0.7.1/mybatis-plus-boot-star
ter-3.0.7.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.0.7.1/mybatis-plus-3.0.7.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.0.7.1/mybatis-plus-extension-3.
0.7.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.0.7.1/mybatis-plus-core-3.0.7.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.0.7.1/mybatis-plus-annotation-
3.0.7.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.0.0.RELEASE/spri
ng-boot-autoconfigure-2.0.0.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.0.0.RELEASE/sprin
g-boot-starter-jdbc-2.0.0.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/zaxxer/HikariCP/2.7.8/HikariCP-2.7.8.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.0.4.RELEASE/spring-jdbc-5.0.4.RELEA
SE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-redis/1.4.1.RELEASE/spri
ng-boot-starter-redis-1.4.1.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/2.0.5.RELEASE/spring-data-
redis-2.0.5.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.0.5.RELEASE/spring-da
ta-keyvalue-2.0.5.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.0.5.RELEASE/spring-dat
a-commons-2.0.5.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-tx/5.0.4.RELEASE/spring-tx-5.0.4.RELEASE.j
ar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-oxm/5.0.4.RELEASE/spring-oxm-5.0.4.RELEASE
.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.0.4.RELEASE/spring-conte
xt-support-5.0.4.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/redis/clients/jedis/2.9.0/jedis-2.9.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.5.0/commons-pool2-2.5.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/2.7.0/springfox-swagger2-2.7.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.5.13/swagger-annotations-1.5.13.jar
[DEBUG]  /Users/<USER>/.m2/repository/io/swagger/swagger-models/1.5.13/swagger-models-1.5.13.jar
[DEBUG]  /Users/<USER>/.m2/repository/io/springfox/springfox-spi/2.7.0/springfox-spi-2.7.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/io/springfox/springfox-core/2.7.0/springfox-core-2.7.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/io/springfox/springfox-schema/2.7.0/springfox-schema-2.7.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/2.7.0/springfox-swagger-common-
2.7.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/2.7.0/springfox-spring-web-2.7.0.ja
r
[DEBUG]  /Users/<USER>/.m2/repository/org/reflections/reflections/0.9.11/reflections-0.9.11.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/javassist/javassist/3.21.0-GA/javassist-3.21.0-GA.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/google/guava/guava/18.0/guava-18.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/fasterxml/classmate/1.3.4/classmate-1.3.4.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-pl
ugin-core-1.2.0.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/sprin
g-plugin-metadata-1.2.0.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.1.0.Final/mapstruct-1.1.0.Final.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-bootstrap-ui/1.6/swagger-bootstrap-ui-1.6
.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/aliyun/oss/aliyun-sdk-oss/3.10.1/aliyun-sdk-oss-3.10.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/jdom/jdom/1.1/jdom-1.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-core/3.4.0/aliyun-java-sdk-core-3.4.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ram/3.0.0/aliyun-java-sdk-ram-3.0.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java-sdk-sts-3.0.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4.2.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-kms/2.7.0/aliyun-java-sdk-kms-2.7.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.2/gson-2.8.2.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/itextpdf/itextpdf/5.5.13.1/itextpdf-5.5.13.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/commons-io/commons-io/1.3.2/commons-io-1.3.2.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/belerweb/pinyin4j/2.5.1/pinyin4j-2.5.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-starter/1.2.13/pagehelpe
r-spring-boot-starter-1.2.13.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.1.1/mybatis-spr
ing-boot-starter-2.1.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/2.1.1/mybat
is-spring-boot-autoconfigure-2.1.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.3/mybatis-3.5.3.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.0.3/mybatis-spring-2.0.3.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-autoconfigure/1.2.13/pag
ehelper-spring-boot-autoconfigure-1.2.13.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper/5.1.11/pagehelper-5.1.11.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/2.0/jsqlparser-2.0.jar
[DEBUG] Source roots:
[DEBUG]  /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java
[DEBUG]  /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/generated-sources/annotations
[DEBUG] Command line options:
[DEBUG] -d /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes -classpath /Users/<USER>/De
sktop/jshERP-0612-Cursor/jshERP-boot/target/classes:/Users/<USER>/.m2/repository/com/gitee/starblues/spring
boot-plugin-framework/2.2.1-RELEASE/springboot-plugin-framework-2.2.1-RELEASE.jar:/Users/<USER>/.m2/reposit
ory/org/pf4j/pf4j/3.1.0/pf4j-3.1.0.jar:/Users/<USER>/.m2/repository/com/github/zafarkhaja/java-semver/0.9.0
/java-semver-0.9.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.9.4/jacks
on-databind-2.9.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.9.0/jac
kson-annotations-2.9.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.9.4/jacks
on-core-2.9.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.9
.4/jackson-dataformat-yaml-2.9.4.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.19/snakeyaml-1.19.ja
r:/Users/<USER>/.m2/repository/com/gitee/starblues/springboot-plugin-framework-extension-mybatis/2.2.1-RELE
ASE/springboot-plugin-framework-extension-mybatis-2.2.1-RELEASE.jar:/Users/<USER>/.m2/repository/org/spring
framework/boot/spring-boot-starter-web/2.0.0.RELEASE/spring-boot-starter-web-2.0.0.RELEASE.jar:/Users/<USER>
i/.m2/repository/org/springframework/boot/spring-boot-starter/2.0.0.RELEASE/spring-boot-starter-2.0.0.RELEAS
E.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.0.0.RELEASE/spring-boot-2.0.0.REL
EASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.0.0.RELEASE/sp
ring-boot-starter-logging-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2
.3/logback-classic-1.2.3.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.
2.3.jar:/Users/<USER>/.m2/repository/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2
.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.0.0.RELEASE/spring-bo
ot-starter-json-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datat
ype-jdk8/2.9.4/jackson-datatype-jdk8-2.9.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/
jackson-datatype-jsr310/2.9.4/jackson-datatype-jsr310-2.9.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/
jackson/module/jackson-module-parameter-names/2.9.4/jackson-module-parameter-names-2.9.4.jar:/Users/<USER>/
.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.0.0.RELEASE/spring-boot-starter-tomcat-
2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/8.5.28/tomcat-embe
d-core-8.5.28.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/8.5.28/tomcat-embed-
el-8.5.28.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/8.5.28/tomcat-emb
ed-websocket-8.5.28.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.0.7.Fina
l/hibernate-validator-6.0.7.Final.jar:/Users/<USER>/.m2/repository/javax/validation/validation-api/2.0.1.Fi
nal/validation-api-2.0.1.Final.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.3.2.Final
/jboss-logging-3.3.2.Final.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.0.4.RELEASE/sp
ring-web-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.0.4.RELEASE/spri
ng-beans-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.0.4.RELEASE/spr
ing-webmvc-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.0.4.RELEASE/spri
ng-aop-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.0.4.RELEASE/spri
ng-context-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.0.4.RELEA
SE/spring-expression-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.7.10/byte-bu
ddy-1.7.10.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.0.4.RELEASE/spring-core-5.0.4
.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.0.4.RELEASE/spring-jcl-5.0.4.REL
EASE.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar:/Users/<USER>/.m2/re
pository/mysql/mysql-connector-java/8.0.28/mysql-connector-java-8.0.28.jar:/Users/<USER>/.m2/repository/com
/google/protobuf/protobuf-java/3.11.4/protobuf-java-3.11.4.jar:/Users/<USER>/.m2/repository/org/apache/http
components/httpclient/4.5.2/httpclient-4.5.2.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/htt
pcore/4.4.9/httpcore-4.4.9.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.11/commons-codec-
1.11.jar:/Users/<USER>/.m2/repository/net/sourceforge/jexcelapi/jxl/2.6.12/jxl-2.6.12.jar:/Users/<USER>/.m
2/repository/log4j/log4j/1.2.14/log4j-1.2.14.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18
.30/lombok-1.18.30.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.15.0/log4j-to
-slf4j-2.15.0.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.25/slf4j-api-1.7.25.jar:/Users/<USER>
ni/.m2/repository/org/apache/logging/log4j/log4j-api/2.10.0/log4j-api-2.10.0.jar:/Users/<USER>/.m2/reposito
ry/org/slf4j/jul-to-slf4j/1.7.25/jul-to-slf4j-1.7.25.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-
plus-boot-starter/3.0.7.1/mybatis-plus-boot-starter-3.0.7.1.jar:/Users/<USER>/.m2/repository/com/baomidou/m
ybatis-plus/3.0.7.1/mybatis-plus-3.0.7.1.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extensi
on/3.0.7.1/mybatis-plus-extension-3.0.7.1.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3
.0.7.1/mybatis-plus-core-3.0.7.1.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.0.
7.1/mybatis-plus-annotation-3.0.7.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-a
utoconfigure/2.0.0.RELEASE/spring-boot-autoconfigure-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/spr
ingframework/boot/spring-boot-starter-jdbc/2.0.0.RELEASE/spring-boot-starter-jdbc-2.0.0.RELEASE.jar:/Users/<USER>
acmini/.m2/repository/com/zaxxer/HikariCP/2.7.8/HikariCP-2.7.8.jar:/Users/<USER>/.m2/repository/org/springf
ramework/spring-jdbc/5.0.4.RELEASE/spring-jdbc-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springfra
mework/boot/spring-boot-starter-redis/1.4.1.RELEASE/spring-boot-starter-redis-1.4.1.RELEASE.jar:/Users/<USER>
ni/.m2/repository/org/springframework/data/spring-data-redis/2.0.5.RELEASE/spring-data-redis-2.0.5.RELEASE.j
ar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.0.5.RELEASE/spring-data-key
value-2.0.5.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.0.5.REL
EASE/spring-data-commons-2.0.5.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.0.4
.RELEASE/spring-tx-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/5.0.4.RELE
ASE/spring-oxm-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.
0.4.RELEASE/spring-context-support-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/redis/clients/jedis/2.9.0
/jedis-2.9.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.5.0/commons-pool2-2.5.0.ja
r:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/2.7.0/springfox-swagger2-2.7.0.jar:/Users/<USER>
cmini/.m2/repository/io/swagger/swagger-annotations/1.5.13/swagger-annotations-1.5.13.jar:/Users/<USER>/.m2
/repository/io/swagger/swagger-models/1.5.13/swagger-models-1.5.13.jar:/Users/<USER>/.m2/repository/io/spri
ngfox/springfox-spi/2.7.0/springfox-spi-2.7.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-core/
2.7.0/springfox-core-2.7.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-schema/2.7.0/springfox-s
chema-2.7.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/2.7.0/springfox-swagger-
common-2.7.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/2.7.0/springfox-spring-web-
2.7.0.jar:/Users/<USER>/.m2/repository/org/reflections/reflections/0.9.11/reflections-0.9.11.jar:/Users/<USER>
mini/.m2/repository/org/javassist/javassist/3.21.0-GA/javassist-3.21.0-GA.jar:/Users/<USER>/.m2/repository/
com/google/guava/guava/18.0/guava-18.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.3.4/class
mate-1.3.4.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spr
ing-plugin-core-1.2.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-met
adata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/mapstruct/map
struct/1.1.0.Final/mapstruct-1.1.0.Final.jar:/Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-boots
trap-ui/1.6/swagger-bootstrap-ui-1.6.jar:/Users/<USER>/.m2/repository/com/aliyun/oss/aliyun-sdk-oss/3.10.1/
aliyun-sdk-oss-3.10.1.jar:/Users/<USER>/.m2/repository/org/jdom/jdom/1.1/jdom-1.1.jar:/Users/<USER>/.m2/re
pository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar:/Users/<USER>/.m2/repository/stax/stax-api/1.0
.1/stax-api-1.0.1.jar:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-core/3.4.0/aliyun-java-sdk-co
re-3.4.0.jar:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ram/3.0.0/aliyun-java-sdk-ram-3.0.0.ja
r:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java-sdk-sts-3.0.0.jar:/Users/<USER>
cmini/.m2/repository/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4.2.0.jar:/Users/<USER>/.m2/r
epository/com/aliyun/aliyun-java-sdk-kms/2.7.0/aliyun-java-sdk-kms-2.7.0.jar:/Users/<USER>/.m2/repository/c
om/google/code/gson/gson/2.8.2/gson-2.8.2.jar:/Users/<USER>/.m2/repository/com/itextpdf/itextpdf/5.5.13.1/i
textpdf-5.5.13.1.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/1.3.2/commons-io-1.3.2.jar:/Users/<USER>
acmini/.m2/repository/com/belerweb/pinyin4j/2.5.1/pinyin4j-2.5.1.jar:/Users/<USER>/.m2/repository/com/githu
b/pagehelper/pagehelper-spring-boot-starter/1.2.13/pagehelper-spring-boot-starter-1.2.13.jar:/Users/<USER>/
.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.1.1/mybatis-spring-boot-starter-2.1.1.j
ar:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/2.1.1/mybatis-spr
ing-boot-autoconfigure-2.1.1.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.3/mybatis-3.5.3.jar:/
Users/macmini/.m2/repository/org/mybatis/mybatis-spring/2.0.3/mybatis-spring-2.0.3.jar:/Users/<USER>/.m2/re
pository/com/github/pagehelper/pagehelper-spring-boot-autoconfigure/1.2.13/pagehelper-spring-boot-autoconfig
ure-1.2.13.jar:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper/5.1.11/pagehelper-5.1.11.jar:/
Users/macmini/.m2/repository/com/github/jsqlparser/jsqlparser/2.0/jsqlparser-2.0.jar: -sourcepath /Users/<USER>
mini/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java:/Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-b
oot/target/generated-sources/annotations: -s /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/ge
nerated-sources/annotations -processorpath /Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.30/lo
mbok-1.18.30.jar: -g -parameters -nowarn -target 1.8 -source 1.8 -encoding UTF-8
[DEBUG] incrementalBuildHelper#beforeRebuildExecution
[INFO] Compiling 277 source files to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes
[DEBUG] incrementalBuildHelper#afterRebuildExecution
[INFO] -------------------------------------------------------------
[ERROR] COMPILATION ERROR : 
[INFO] -------------------------------------------------------------
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/controller/WorkOrder
Controller.java:[6,29] 找不到符号
  符号:   类 ErpInfo
  位置: 程序包 com.jsh.erp.constants
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/controller/Productio
nController.java:[6,29] 找不到符号
  符号:   类 ErpInfo
  位置: 程序包 com.jsh.erp.constants
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[4,39] 找不到符号
  符号:   类 WorkOrderItemExample
  位置: 程序包 com.jsh.erp.datasource.entities
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[18,25] 找不到符号
  符号:   类 WorkOrderItemExample
  位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[23,25] 找不到符号
  符号:   类 WorkOrderItemExample
  位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[43,41] 找不到符号
  符号:   类 WorkOrderItemExample
  位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[53,91] 找不到符号
  符号:   类 WorkOrderItemExample
  位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[58,82] 找不到符号
  符号:   类 WorkOrderItemExample
  位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[INFO] 8 errors 
[INFO] -------------------------------------------------------------
[INFO] ------------------------------------------------------------------------
[INFO] BUILD FAILURE
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  5.740 s
[INFO] Finished at: 2025-06-21T17:41:42+08:00
[INFO] ------------------------------------------------------------------------
[ERROR] Failed to execute goal org.apache.maven.plugins:maven-compiler-plugin:3.7.0:compile (default-compile
) on project jshERP-boot: Compilation failure: Compilation failure: 
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/controller/WorkOrder
Controller.java:[6,29] 找不到符号
[ERROR]   符号:   类 ErpInfo
[ERROR]   位置: 程序包 com.jsh.erp.constants
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/controller/Productio
nController.java:[6,29] 找不到符号
[ERROR]   符号:   类 ErpInfo
[ERROR]   位置: 程序包 com.jsh.erp.constants
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[4,39] 找不到符号
[ERROR]   符号:   类 WorkOrderItemExample
[ERROR]   位置: 程序包 com.jsh.erp.datasource.entities
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[18,25] 找不到符号
[ERROR]   符号:   类 WorkOrderItemExample
[ERROR]   位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[23,25] 找不到符号
[ERROR]   符号:   类 WorkOrderItemExample
[ERROR]   位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[43,41] 找不到符号
[ERROR]   符号:   类 WorkOrderItemExample
[ERROR]   位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[53,91] 找不到符号
[ERROR]   符号:   类 WorkOrderItemExample
[ERROR]   位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[58,82] 找不到符号
[ERROR]   符号:   类 WorkOrderItemExample
[ERROR]   位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] -> [Help 1]
org.apache.maven.lifecycle.LifecycleExecutionException: Failed to execute goal org.apache.maven.plugins:mave
n-compiler-plugin:3.7.0:compile (default-compile) on project jshERP-boot: Compilation failure
    at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:333)
    at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
    at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
    at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
    at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
    at org.apache.maven.lifecycle.internal.MojoExecutor.executeForkedExecutions (MojoExecutor.java:448)
    at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:311)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
    at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
    at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
    at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
    at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:
105)
    at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:
73)
    at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreade
dBuilder.java:53)
    at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
    at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
    at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
    at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
    at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
    at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
    at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
    at jdk.internal.reflect.DirectMethodHandleAccessor.invoke (DirectMethodHandleAccessor.java:103)
    at java.lang.reflect.Method.invoke (Method.java:580)
    at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
    at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
    at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
    at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
Caused by: org.apache.maven.plugin.compiler.CompilationFailureException: Compilation failure
    at org.apache.maven.plugin.compiler.AbstractCompilerMojo.execute (AbstractCompilerMojo.java:1161)
    at org.apache.maven.plugin.compiler.CompilerMojo.execute (CompilerMojo.java:168)
    at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
    at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
    at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
    at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
    at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
    at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
    at org.apache.maven.lifecycle.internal.MojoExecutor.executeForkedExecutions (MojoExecutor.java:448)
    at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:311)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
    at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
    at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
    at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
    at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:
105)
    at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:
73)
    at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreade
dBuilder.java:53)
    at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
    at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
    at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
    at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
    at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
    at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
    at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
    at jdk.internal.reflect.DirectMethodHandleAccessor.invoke (DirectMethodHandleAccessor.java:103)
    at java.lang.reflect.Method.invoke (Method.java:580)
    at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
    at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
    at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
    at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
[ERROR] 
[ERROR] 
[ERROR] For more information about the errors and possible solutions, please read the following articles:
[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/MojoFailureException
[DEBUG] Shutting down adapter factory; available factories [file-lock, rwlock-local, semaphore-local, noop];
 available name mappers [discriminating, file-gav, file-hgav, file-static, gav, static]
[DEBUG] Shutting down 'file-lock' factory
[DEBUG] Shutting down 'rwlock-local' factory
[DEBUG] Shutting down 'semaphore-local' factory
[DEBUG] Shutting down 'noop' factory
macmini@liangchaoweideMac-mini jshERP-boot %
```

---



---

我注意到您已将Maven命令转入后台执行，这使我无法直接观察其完整的输出和最终的错误信息。为了有效诊断问题，我需要捕获完整的日志。

**下一步计划**：
我将重新执行相同的Maven调试命令，但会将所有输出（包括标准输出和错误流）重定向到一个日志文件 `maven_debug_log.txt` 中。这样可以确保我能分析完整的执行过程，并找到导致启动失败的具体原因。

请允许我执行以下命令来捕获日志。

---

```bash
cd jshERP-boot && mvn spring-boot:run -X > maven_debug_log.txt 2>&1
```

```
macmini@liangchaoweideMac-mini jshERP-0612-Cursor % cd jshERP-boot && mvn spring-boot:run -X > maven_debug_l
og.txt 2>&1
macmini@liangchaoweideMac-mini jshERP-boot %
```

---



---

好的，命令已在后台执行完毕，日志文件 `jshERP-boot/maven_debug_log.txt` 应该已经生成。

**下一步：分析日志**

我现在将读取这个日志文件的内容，以分析详细的错误堆栈信息，并找出导致应用启动失败的根本原因。

---

Model provided invalid arguments to read file tool.

---



---

抱歉，上一步工具调用出现了参数错误。我需要读取整个日志文件才能进行全面分析。

我现在将重新读取 `jshERP-boot/maven_debug_log.txt` 的全部内容。

---

Model provided invalid arguments to read file tool.

---



---

再次抱歉，工具调用又失败了。我将调整策略，直接查看日志文件的末尾部分，因为错误信息通常出现在那里。

**新策略：查看日志尾部**

我将使用 `tail` 命令来获取 `maven_debug_log.txt` 文件的最后500行内容。这能让我快速定位到关键的错误堆栈信息。

---

```bash
tail -n 500 jshERP-boot/maven_debug_log.txt
```

```
macmini@liangchaoweideMac-mini jshERP-0612-Cursor % tail -n 500 jshERP-boot/maven_debug_log.txt
[DEBUG] file PlatformConfigMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/PlatformCon
figMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/PlatformCon
figMapper.xml
[DEBUG] Copying file mapper_xml/MaterialPropertyMapperEx.xml
[DEBUG] file MaterialPropertyMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialPro
pertyMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/Materia
lPropertyMapperEx.xml
[DEBUG] Copying file mapper_xml/InOutItemMapperEx.xml
[DEBUG] file InOutItemMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/InOutItemMa
pperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/InOutItemMappe
rEx.xml
[DEBUG] Copying file mapper_xml/UserBusinessMapper.xml
[DEBUG] file UserBusinessMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/UserBusines
sMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/UserBusinessM
apper.xml
[DEBUG] Copying file mapper_xml/LogMapper.xml
[DEBUG] file LogMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/LogMapper.x
ml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/LogMapper.xml
[DEBUG] Copying file mapper_xml/MaterialInitialStockMapperEx.xml
[DEBUG] file MaterialInitialStockMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialIni
tialStockMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/Mat
erialInitialStockMapperEx.xml
[DEBUG] Copying file mapper_xml/AccountHeadMapperEx.xml
[DEBUG] file AccountHeadMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/AccountHead
MapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/AccountHeadM
apperEx.xml
[DEBUG] Copying file mapper_xml/OrgaUserRelMapperEx.xml
[DEBUG] file OrgaUserRelMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/OrgaUserRel
MapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/OrgaUserRelM
apperEx.xml
[DEBUG] Copying file mapper_xml/SupplierMapper.xml
[DEBUG] file SupplierMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/SupplierMap
per.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/SupplierMapper.xm
l
[DEBUG] Copying file mapper_xml/OrganizationMapper.xml
[DEBUG] file OrganizationMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/Organizatio
nMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/OrganizationM
apper.xml
[DEBUG] Copying file mapper_xml/MaterialPropertyMapper.xml
[DEBUG] file MaterialPropertyMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialPro
pertyMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/MaterialP
ropertyMapper.xml
[DEBUG] Copying file mapper_xml/UserMapperEx.xml
[DEBUG] file UserMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/UserMapperE
x.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/UserMapperEx.xml
[DEBUG] Copying file mapper_xml/AccountMapperEx.xml
[DEBUG] file AccountMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/AccountMapp
erEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/AccountMapperEx.
xml
[DEBUG] Copying file mapper_xml/SupplierMapperEx.xml
[DEBUG] file SupplierMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/SupplierMap
perEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/SupplierMapperE
x.xml
[DEBUG] Copying file mapper_xml/AccountMapper.xml
[DEBUG] file AccountMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/AccountMapp
er.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/AccountMapper.xml
[DEBUG] Copying file mapper_xml/SystemConfigMapperEx.xml
[DEBUG] file SystemConfigMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/SystemConfi
gMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/SystemConfi
gMapperEx.xml
[DEBUG] Copying file mapper_xml/AccountHeadMapper.xml
[DEBUG] file AccountHeadMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/AccountHead
Mapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/AccountHeadMap
per.xml
[DEBUG] Copying file mapper_xml/MaterialMapper.xml
[DEBUG] file MaterialMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialMap
per.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/MaterialMapper.xm
l
[DEBUG] Copying file mapper_xml/UserMapper.xml
[DEBUG] file UserMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/UserMapper.
xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/UserMapper.xml
[DEBUG] Copying file mapper_xml/MaterialMapperEx.xml
[DEBUG] file MaterialMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialMap
perEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/MaterialMapperE
x.xml
[DEBUG] Copying file mapper_xml/MaterialCategoryMapperEx.xml
[DEBUG] file MaterialCategoryMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialCat
egoryMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/Materia
lCategoryMapperEx.xml
[DEBUG] Copying file mapper_xml/SystemConfigMapper.xml
[DEBUG] file SystemConfigMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/SystemConfi
gMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/SystemConfigM
apper.xml
[DEBUG] Copying file mapper_xml/MaterialAttributeMapper.xml
[DEBUG] file MaterialAttributeMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialAtt
ributeMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/Material
AttributeMapper.xml
[DEBUG] Copying file mapper_xml/WorkOrderMapperEx.xml
[DEBUG] file WorkOrderMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/WorkOrderMa
pperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/WorkOrderMappe
rEx.xml
[DEBUG] Copying file mapper_xml/LogMapperEx.xml
[DEBUG] file LogMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/LogMapperEx
.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/LogMapperEx.xml
[DEBUG] Copying file mapper_xml/AccountItemMapper.xml
[DEBUG] file AccountItemMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/AccountItem
Mapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/AccountItemMap
per.xml
[DEBUG] Copying file mapper_xml/MaterialCurrentStockMapper.xml
[DEBUG] file MaterialCurrentStockMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialCur
rentStockMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/Mater
ialCurrentStockMapper.xml
[DEBUG] Copying file mapper_xml/MsgMapper.xml
[DEBUG] file MsgMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MsgMapper.x
ml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/MsgMapper.xml
[DEBUG] Copying file mapper_xml/AccountItemMapperEx.xml
[DEBUG] file AccountItemMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/AccountItem
MapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/AccountItemM
apperEx.xml
[DEBUG] Copying file mapper_xml/FunctionMapper.xml
[DEBUG] file FunctionMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/FunctionMap
per.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/FunctionMapper.xm
l
[DEBUG] Copying file mapper_xml/MsgMapperEx.xml
[DEBUG] file MsgMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MsgMapperEx
.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/MsgMapperEx.xml
[DEBUG] Copying file mapper_xml/DepotMapper.xml
[DEBUG] file DepotMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/DepotMapper
.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/DepotMapper.xml
[DEBUG] Copying file mapper_xml/PersonMapper.xml
[DEBUG] file PersonMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/PersonMappe
r.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/PersonMapper.xml
[DEBUG] Copying file mapper_xml/UnitMapperEx.xml
[DEBUG] file UnitMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/UnitMapperE
x.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/UnitMapperEx.xml
[DEBUG] Copying file mapper_xml/MaterialCategoryMapper.xml
[DEBUG] file MaterialCategoryMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialCat
egoryMapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/MaterialC
ategoryMapper.xml
[DEBUG] Copying file mapper_xml/OrganizationMapperEx.xml
[DEBUG] file OrganizationMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/Organizatio
nMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/Organizatio
nMapperEx.xml
[DEBUG] Copying file mapper_xml/SerialNumberMapperEx.xml
[DEBUG] file SerialNumberMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/SerialNumbe
rMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/SerialNumbe
rMapperEx.xml
[DEBUG] Copying file mapper_xml/OrgaUserRelMapper.xml
[DEBUG] file OrgaUserRelMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/OrgaUserRel
Mapper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/OrgaUserRelMap
per.xml
[DEBUG] Copying file mapper_xml/TenantMapperEx.xml
[DEBUG] file TenantMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/TenantMappe
rEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/TenantMapperEx.xm
l
[DEBUG] Copying file mapper_xml/UnitMapper.xml
[DEBUG] file UnitMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/UnitMapper.
xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/UnitMapper.xml
[DEBUG] Copying file mapper_xml/SequenceMapperEx.xml
[DEBUG] file SequenceMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/SequenceMap
perEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/SequenceMapperE
x.xml
[DEBUG] Copying file mapper_xml/TenantMapper.xml
[DEBUG] file TenantMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/TenantMappe
r.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/TenantMapper.xml
[DEBUG] Copying file mapper_xml/MaterialAttributeMapperEx.xml
[DEBUG] file MaterialAttributeMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialAtt
ributeMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/Materi
alAttributeMapperEx.xml
[DEBUG] Copying file mapper_xml/InOutItemMapper.xml
[DEBUG] file InOutItemMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/InOutItemMa
pper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/InOutItemMapper.
xml
[DEBUG] Copying file mapper_xml/MaterialCurrentStockMapperEx.xml
[DEBUG] file MaterialCurrentStockMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/MaterialCur
rentStockMapperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/Mat
erialCurrentStockMapperEx.xml
[DEBUG] Copying file mapper_xml/DepotHeadMapper.xml
[DEBUG] file DepotHeadMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/DepotHeadMa
pper.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/DepotHeadMapper.
xml
[DEBUG] Copying file mapper_xml/RoleMapper.xml
[DEBUG] file RoleMapper.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/RoleMapper.
xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/RoleMapper.xml
[DEBUG] Copying file mapper_xml/DepotHeadMapperEx.xml
[DEBUG] file DepotHeadMapperEx.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/mapper_xml/DepotHeadMa
pperEx.xml to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/mapper_xml/DepotHeadMappe
rEx.xml
[DEBUG] Copying file logback-spring.xml
[DEBUG] file logback-spring.xml has a filtered file extension
[DEBUG] copy /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/resources/logback-spring.xml to 
/Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes/logback-spring.xml
[DEBUG] no use filter components
[INFO] 
[INFO] --- compiler:3.7.0:compile (default-compile) @ jshERP-boot ---
[DEBUG] Dependency collection stats {ConflictMarker.analyzeTime=92292, ConflictMarker.markTime=72000, Confli
ctMarker.nodeCount=118, ConflictIdSorter.graphTime=36459, ConflictIdSorter.topsortTime=47458, ConflictIdSort
er.conflictIdCount=45, ConflictIdSorter.conflictIdCycleCount=0, ConflictResolver.totalTime=552917, ConflictR
esolver.conflictItemCount=72, DfDependencyCollector.collectTime=43051708, DfDependencyCollector.transformTim
e=820625}
[DEBUG] org.apache.maven.plugins:maven-compiler-plugin:jar:3.7.0
[DEBUG]    org.apache.maven:maven-plugin-api:jar:3.0:compile
[DEBUG]       org.apache.maven:maven-model:jar:3.0:compile
[DEBUG]       org.sonatype.sisu:sisu-inject-plexus:jar:1.4.2:compile
[DEBUG]          org.sonatype.sisu:sisu-inject-bean:jar:1.4.2:compile
[DEBUG]             org.sonatype.sisu:sisu-guice:jar:noaop:2.1.7:compile
[DEBUG]    org.apache.maven:maven-artifact:jar:3.0:compile
[DEBUG]       org.codehaus.plexus:plexus-utils:jar:2.0.4:compile
[DEBUG]    org.apache.maven:maven-core:jar:3.0:compile
[DEBUG]       org.apache.maven:maven-settings:jar:3.0:compile
[DEBUG]       org.apache.maven:maven-settings-builder:jar:3.0:compile
[DEBUG]       org.apache.maven:maven-repository-metadata:jar:3.0:compile
[DEBUG]       org.apache.maven:maven-model-builder:jar:3.0:compile
[DEBUG]       org.apache.maven:maven-aether-provider:jar:3.0:runtime
[DEBUG]       org.sonatype.aether:aether-impl:jar:1.7:compile
[DEBUG]          org.sonatype.aether:aether-spi:jar:1.7:compile
[DEBUG]       org.sonatype.aether:aether-api:jar:1.7:compile
[DEBUG]       org.sonatype.aether:aether-util:jar:1.7:compile
[DEBUG]       org.codehaus.plexus:plexus-interpolation:jar:1.14:compile
[DEBUG]       org.codehaus.plexus:plexus-classworlds:jar:2.2.3:compile
[DEBUG]       org.codehaus.plexus:plexus-component-annotations:jar:1.6:compile (version managed from default
)
[DEBUG]       org.sonatype.plexus:plexus-sec-dispatcher:jar:1.3:compile
[DEBUG]          org.sonatype.plexus:plexus-cipher:jar:1.4:compile
[DEBUG]    org.apache.maven.shared:maven-shared-utils:jar:3.1.0:compile
[DEBUG]       commons-io:commons-io:jar:2.5:compile
[DEBUG]    org.apache.maven.shared:maven-shared-incremental:jar:1.1:compile
[DEBUG]    org.codehaus.plexus:plexus-java:jar:0.9.2:compile
[DEBUG]       org.ow2.asm:asm:jar:6.0_BETA:compile
[DEBUG]       com.thoughtworks.qdox:qdox:jar:2.0-M7:compile
[DEBUG]    org.codehaus.plexus:plexus-compiler-api:jar:2.8.2:compile
[DEBUG]    org.codehaus.plexus:plexus-compiler-manager:jar:2.8.2:compile
[DEBUG]    org.codehaus.plexus:plexus-compiler-javac:jar:2.8.2:runtime
[DEBUG] Created new class realm plugin>org.apache.maven.plugins:maven-compiler-plugin:3.7.0
[DEBUG] Importing foreign packages into class realm plugin>org.apache.maven.plugins:maven-compiler-plugin:3.
7.0
[DEBUG]   Imported:  < maven.api
[DEBUG] Populating class realm plugin>org.apache.maven.plugins:maven-compiler-plugin:3.7.0
[DEBUG]   Included: org.apache.maven.plugins:maven-compiler-plugin:jar:3.7.0
[DEBUG]   Included: org.sonatype.sisu:sisu-inject-bean:jar:1.4.2
[DEBUG]   Included: org.sonatype.sisu:sisu-guice:jar:noaop:2.1.7
[DEBUG]   Included: org.codehaus.plexus:plexus-utils:jar:2.0.4
[DEBUG]   Included: org.sonatype.aether:aether-util:jar:1.7
[DEBUG]   Included: org.codehaus.plexus:plexus-interpolation:jar:1.14
[DEBUG]   Included: org.codehaus.plexus:plexus-component-annotations:jar:1.6
[DEBUG]   Included: org.sonatype.plexus:plexus-sec-dispatcher:jar:1.3
[DEBUG]   Included: org.sonatype.plexus:plexus-cipher:jar:1.4
[DEBUG]   Included: org.apache.maven.shared:maven-shared-utils:jar:3.1.0
[DEBUG]   Included: commons-io:commons-io:jar:2.5
[DEBUG]   Included: org.apache.maven.shared:maven-shared-incremental:jar:1.1
[DEBUG]   Included: org.codehaus.plexus:plexus-java:jar:0.9.2
[DEBUG]   Included: org.ow2.asm:asm:jar:6.0_BETA
[DEBUG]   Included: com.thoughtworks.qdox:qdox:jar:2.0-M7
[DEBUG]   Included: org.codehaus.plexus:plexus-compiler-api:jar:2.8.2
[DEBUG]   Included: org.codehaus.plexus:plexus-compiler-manager:jar:2.8.2
[DEBUG]   Included: org.codehaus.plexus:plexus-compiler-javac:jar:2.8.2
[DEBUG] Loading mojo org.apache.maven.plugins:maven-compiler-plugin:3.7.0:compile from plugin realm ClassRea
lm[plugin>org.apache.maven.plugins:maven-compiler-plugin:3.7.0, parent: jdk.internal.loader.ClassLoaders$App
ClassLoader@18ff02e4]
[DEBUG] Configuring mojo execution 'org.apache.maven.plugins:maven-compiler-plugin:3.7.0:compile:default-com
pile' with basic configurator -->
[DEBUG]   (s) groupId = org.projectlombok
[DEBUG]   (s) artifactId = lombok
[DEBUG]   (s) version = 1.18.30
[DEBUG]   (f) annotationProcessorPaths = [org.projectlombok:lombok:1.18.30.jar]
[DEBUG]   (f) basedir = /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot
[DEBUG]   (f) buildDirectory = /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target
[DEBUG]   (f) compilePath = [/Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes, /Users/<USER>
cmini/.m2/repository/com/gitee/starblues/springboot-plugin-framework/2.2.1-RELEASE/springboot-plugin-framewo
rk-2.2.1-RELEASE.jar, /Users/<USER>/.m2/repository/org/pf4j/pf4j/3.1.0/pf4j-3.1.0.jar, /Users/<USER>/.m2/r
epository/com/github/zafarkhaja/java-semver/0.9.0/java-semver-0.9.0.jar, /Users/<USER>/.m2/repository/com/f
asterxml/jackson/core/jackson-databind/2.9.4/jackson-databind-2.9.4.jar, /Users/<USER>/.m2/repository/com/f
asterxml/jackson/core/jackson-annotations/2.9.0/jackson-annotations-2.9.0.jar, /Users/<USER>/.m2/repository
/com/fasterxml/jackson/core/jackson-core/2.9.4/jackson-core-2.9.4.jar, /Users/<USER>/.m2/repository/com/fas
terxml/jackson/dataformat/jackson-dataformat-yaml/2.9.4/jackson-dataformat-yaml-2.9.4.jar, /Users/<USER>/.m
2/repository/org/yaml/snakeyaml/1.19/snakeyaml-1.19.jar, /Users/<USER>/.m2/repository/com/gitee/starblues/s
pringboot-plugin-framework-extension-mybatis/2.2.1-RELEASE/springboot-plugin-framework-extension-mybatis-2.2
.1-RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.0.0.RELEASE
/spring-boot-starter-web-2.0.0.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/boot/spring-bo
ot-starter/2.0.0.RELEASE/spring-boot-starter-2.0.0.RELEASE.jar, /Users/<USER>/.m2/repository/org/springfram
ework/boot/spring-boot/2.0.0.RELEASE/spring-boot-2.0.0.RELEASE.jar, /Users/<USER>/.m2/repository/org/spring
framework/boot/spring-boot-starter-logging/2.0.0.RELEASE/spring-boot-starter-logging-2.0.0.RELEASE.jar, /Use
rs/macmini/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, /Users/<USER>/.m2
/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, /Users/<USER>/.m2/repository/javax/an
notation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2.jar, /Users/<USER>/.m2/repository/org/spring
framework/boot/spring-boot-starter-json/2.0.0.RELEASE/spring-boot-starter-json-2.0.0.RELEASE.jar, /Users/<USER>
mini/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.9.4/jackson-datatype-jdk8-2.9.4.j
ar, /Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.9.4/jackson-datat
ype-jsr310-2.9.4.jar, /Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-na
mes/2.9.4/jackson-module-parameter-names-2.9.4.jar, /Users/<USER>/.m2/repository/org/springframework/boot/s
pring-boot-starter-tomcat/2.0.0.RELEASE/spring-boot-starter-tomcat-2.0.0.RELEASE.jar, /Users/<USER>/.m2/rep
ository/org/apache/tomcat/embed/tomcat-embed-core/8.5.28/tomcat-embed-core-8.5.28.jar, /Users/<USER>/.m2/re
pository/org/apache/tomcat/embed/tomcat-embed-el/8.5.28/tomcat-embed-el-8.5.28.jar, /Users/<USER>/.m2/repos
itory/org/apache/tomcat/embed/tomcat-embed-websocket/8.5.28/tomcat-embed-websocket-8.5.28.jar, /Users/<USER>
i/.m2/repository/org/hibernate/validator/hibernate-validator/6.0.7.Final/hibernate-validator-6.0.7.Final.jar
, /Users/<USER>/.m2/repository/javax/validation/validation-api/2.0.1.Final/validation-api-2.0.1.Final.jar, 
/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.3.2.Final/jboss-logging-3.3.2.Final.jar, /Us
ers/macmini/.m2/repository/org/springframework/spring-web/5.0.4.RELEASE/spring-web-5.0.4.RELEASE.jar, /Users
/macmini/.m2/repository/org/springframework/spring-beans/5.0.4.RELEASE/spring-beans-5.0.4.RELEASE.jar, /User
s/macmini/.m2/repository/org/springframework/spring-webmvc/5.0.4.RELEASE/spring-webmvc-5.0.4.RELEASE.jar, /U
sers/macmini/.m2/repository/org/springframework/spring-aop/5.0.4.RELEASE/spring-aop-5.0.4.RELEASE.jar, /User
s/macmini/.m2/repository/org/springframework/spring-context/5.0.4.RELEASE/spring-context-5.0.4.RELEASE.jar, 
/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.0.4.RELEASE/spring-expression-5.0.4.RE
LEASE.jar, /Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.7.10/byte-buddy-1.7.10.jar, /Users/<USER>
ni/.m2/repository/org/springframework/spring-core/5.0.4.RELEASE/spring-core-5.0.4.RELEASE.jar, /Users/<USER>
i/.m2/repository/org/springframework/spring-jcl/5.0.4.RELEASE/spring-jcl-5.0.4.RELEASE.jar, /Users/<USER>/.
m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar, /Users/<USER>/.m2/repository/mysql/mysql-con
nector-java/8.0.28/mysql-connector-java-8.0.28.jar, /Users/<USER>/.m2/repository/com/google/protobuf/protob
uf-java/3.11.4/protobuf-java-3.11.4.jar, /Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/
4.5.2/httpclient-4.5.2.jar, /Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.9/httpcore-
4.4.9.jar, /Users/<USER>/.m2/repository/commons-codec/commons-codec/1.11/commons-codec-1.11.jar, /Users/<USER>
mini/.m2/repository/net/sourceforge/jexcelapi/jxl/2.6.12/jxl-2.6.12.jar, /Users/<USER>/.m2/repository/log4j
/log4j/1.2.14/log4j-1.2.14.jar, /Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.30/lombok-1.18.3
0.jar, /Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.15.0/log4j-to-slf4j-2.15.0.ja
r, /Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.25/slf4j-api-1.7.25.jar, /Users/<USER>/.m2/reposit
ory/org/apache/logging/log4j/log4j-api/2.10.0/log4j-api-2.10.0.jar, /Users/<USER>/.m2/repository/org/slf4j/
jul-to-slf4j/1.7.25/jul-to-slf4j-1.7.25.jar, /Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-st
arter/3.0.7.1/mybatis-plus-boot-starter-3.0.7.1.jar, /Users/<USER>/.m2/repository/com/baomidou/mybatis-plus
/3.0.7.1/mybatis-plus-3.0.7.1.jar, /Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.0.7.1
/mybatis-plus-extension-3.0.7.1.jar, /Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.0.7.1/my
batis-plus-core-3.0.7.1.jar, /Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.0.7.1/myba
tis-plus-annotation-3.0.7.1.jar, /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconf
igure/2.0.0.RELEASE/spring-boot-autoconfigure-2.0.0.RELEASE.jar, /Users/<USER>/.m2/repository/org/springfra
mework/boot/spring-boot-starter-jdbc/2.0.0.RELEASE/spring-boot-starter-jdbc-2.0.0.RELEASE.jar, /Users/<USER>
i/.m2/repository/com/zaxxer/HikariCP/2.7.8/HikariCP-2.7.8.jar, /Users/<USER>/.m2/repository/org/springframe
work/spring-jdbc/5.0.4.RELEASE/spring-jdbc-5.0.4.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframew
ork/boot/spring-boot-starter-redis/1.4.1.RELEASE/spring-boot-starter-redis-1.4.1.RELEASE.jar, /Users/<USER>
/.m2/repository/org/springframework/data/spring-data-redis/2.0.5.RELEASE/spring-data-redis-2.0.5.RELEASE.jar
, /Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.0.5.RELEASE/spring-data-keyv
alue-2.0.5.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.0.5.REL
EASE/spring-data-commons-2.0.5.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/spring-tx/5.0.
4.RELEASE/spring-tx-5.0.4.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/spring-oxm/5.0.4.RE
LEASE/spring-oxm-5.0.4.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/spring-context-support
/5.0.4.RELEASE/spring-context-support-5.0.4.RELEASE.jar, /Users/<USER>/.m2/repository/redis/clients/jedis/2
.9.0/jedis-2.9.0.jar, /Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.5.0/commons-pool2-2.5
.0.jar, /Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/2.7.0/springfox-swagger2-2.7.0.jar, /U
sers/macmini/.m2/repository/io/swagger/swagger-annotations/1.5.13/swagger-annotations-1.5.13.jar, /Users/<USER>
mini/.m2/repository/io/swagger/swagger-models/1.5.13/swagger-models-1.5.13.jar, /Users/<USER>/.m2/repositor
y/io/springfox/springfox-spi/2.7.0/springfox-spi-2.7.0.jar, /Users/<USER>/.m2/repository/io/springfox/sprin
gfox-core/2.7.0/springfox-core-2.7.0.jar, /Users/<USER>/.m2/repository/io/springfox/springfox-schema/2.7.0/
springfox-schema-2.7.0.jar, /Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/2.7.0/spring
fox-swagger-common-2.7.0.jar, /Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/2.7.0/springfo
x-spring-web-2.7.0.jar, /Users/<USER>/.m2/repository/org/reflections/reflections/0.9.11/reflections-0.9.11.
jar, /Users/<USER>/.m2/repository/org/javassist/javassist/3.21.0-GA/javassist-3.21.0-GA.jar, /Users/<USER>
/.m2/repository/com/google/guava/guava/18.0/guava-18.0.jar, /Users/<USER>/.m2/repository/com/fasterxml/clas
smate/1.3.4/classmate-1.3.4.jar, /Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core
/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/plugi
n/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar, /Users/<USER>/.m2/reposito
ry/org/mapstruct/mapstruct/1.1.0.Final/mapstruct-1.1.0.Final.jar, /Users/<USER>/.m2/repository/com/github/x
iaoymin/swagger-bootstrap-ui/1.6/swagger-bootstrap-ui-1.6.jar, /Users/<USER>/.m2/repository/com/aliyun/oss/
aliyun-sdk-oss/3.10.1/aliyun-sdk-oss-3.10.1.jar, /Users/<USER>/.m2/repository/org/jdom/jdom/1.1/jdom-1.1.ja
r, /Users/<USER>/.m2/repository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar, /Users/<USER>/.m2/rep
ository/stax/stax-api/1.0.1/stax-api-1.0.1.jar, /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-cor
e/3.4.0/aliyun-java-sdk-core-3.4.0.jar, /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ram/3.0.0/a
liyun-java-sdk-ram-3.0.0.jar, /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java
-sdk-sts-3.0.0.jar, /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4
.2.0.jar, /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-kms/2.7.0/aliyun-java-sdk-kms-2.7.0.jar, 
/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.2/gson-2.8.2.jar, /Users/<USER>/.m2/repository/
com/itextpdf/itextpdf/5.5.13.1/itextpdf-5.5.13.1.jar, /Users/<USER>/.m2/repository/commons-io/commons-io/1.
3.2/commons-io-1.3.2.jar, /Users/<USER>/.m2/repository/com/belerweb/pinyin4j/2.5.1/pinyin4j-2.5.1.jar, /Use
rs/macmini/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-starter/1.2.13/pagehelper-spring-boot
-starter-1.2.13.jar, /Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.1.1
/mybatis-spring-boot-starter-2.1.1.jar, /Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring
-boot-autoconfigure/2.1.1/mybatis-spring-boot-autoconfigure-2.1.1.jar, /Users/<USER>/.m2/repository/org/myb
atis/mybatis/3.5.3/mybatis-3.5.3.jar, /Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.0.3/mybatis
-spring-2.0.3.jar, /Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-autoconfigure/
1.2.13/pagehelper-spring-boot-autoconfigure-1.2.13.jar, /Users/<USER>/.m2/repository/com/github/pagehelper/
pagehelper/5.1.11/pagehelper-5.1.11.jar, /Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/2.0/
jsqlparser-2.0.jar]
[DEBUG]   (f) compileSourceRoots = [/Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java]
[DEBUG]   (f) compilerId = javac
[DEBUG]   (f) debug = true
[DEBUG]   (f) encoding = UTF-8
[DEBUG]   (f) failOnError = true
[DEBUG]   (f) failOnWarning = false
[DEBUG]   (f) forceJavacCompilerUse = false
[DEBUG]   (f) fork = false
[DEBUG]   (f) generatedSourcesDirectory = /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/gener
ated-sources/annotations
[DEBUG]   (f) mojoExecution = org.apache.maven.plugins:maven-compiler-plugin:3.7.0:compile {execution: defau
lt-compile}
[DEBUG]   (f) optimize = false
[DEBUG]   (f) outputDirectory = /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes
[DEBUG]   (f) parameters = true
[DEBUG]   (f) project = MavenProject: com.jsh:jshERP-boot:3.5-SNAPSHOT @ /Users/<USER>/Desktop/jshERP-0612-
Cursor/jshERP-boot/pom.xml
[DEBUG]   (f) projectArtifact = com.jsh:jshERP-boot:jar:3.5-SNAPSHOT
[DEBUG]   (f) session = org.apache.maven.execution.MavenSession@62765aec
[DEBUG]   (f) showDeprecation = false
[DEBUG]   (f) showWarnings = false
[DEBUG]   (f) skipMultiThreadWarning = false
[DEBUG]   (f) source = 1.8
[DEBUG]   (f) staleMillis = 0
[DEBUG]   (f) target = 1.8
[DEBUG]   (f) useIncrementalCompilation = true
[DEBUG]   (f) verbose = false
[DEBUG] -- end configuration --
[DEBUG] Using compiler 'javac'.
[DEBUG] Adding /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/generated-sources/annotations to
 compile source roots:
  /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java
[DEBUG] New compile source roots:
  /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java
  /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/generated-sources/annotations
[DEBUG] CompilerReuseStrategy: reuseCreated
[DEBUG] useIncrementalCompilation enabled
[INFO] Changes detected - recompiling the module!
[DEBUG] Classpath:
[DEBUG]  /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes
[DEBUG]  /Users/<USER>/.m2/repository/com/gitee/starblues/springboot-plugin-framework/2.2.1-RELEASE/springb
oot-plugin-framework-2.2.1-RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/pf4j/pf4j/3.1.0/pf4j-3.1.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/github/zafarkhaja/java-semver/0.9.0/java-semver-0.9.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.9.4/jackson-databind-2.
9.4.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.9.0/jackson-annotati
ons-2.9.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.9.4/jackson-core-2.9.4.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.9.4/jackso
n-dataformat-yaml-2.9.4.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.19/snakeyaml-1.19.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/gitee/starblues/springboot-plugin-framework-extension-mybatis/2.2
.1-RELEASE/springboot-plugin-framework-extension-mybatis-2.2.1-RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.0.0.RELEASE/spring
-boot-starter-web-2.0.0.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.0.0.RELEASE/spring-boo
t-starter-2.0.0.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.0.0.RELEASE/spring-boot-2.0.0.
RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.0.0.RELEASE/sp
ring-boot-starter-logging-2.0.0.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar
[DEBUG]  /Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar
[DEBUG]  /Users/<USER>/.m2/repository/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.
2.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.0.0.RELEASE/sprin
g-boot-starter-json-2.0.0.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.9.4/jackson-da
tatype-jdk8-2.9.4.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.9.4/jackson-
datatype-jsr310-2.9.4.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.9.4/jac
kson-module-parameter-names-2.9.4.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.0.0.RELEASE/spr
ing-boot-starter-tomcat-2.0.0.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/8.5.28/tomcat-embed-core-8.
5.28.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/8.5.28/tomcat-embed-el-8.5.28
.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/8.5.28/tomcat-embed-we
bsocket-8.5.28.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.0.7.Final/hibernate-val
idator-6.0.7.Final.jar
[DEBUG]  /Users/<USER>/.m2/repository/javax/validation/validation-api/2.0.1.Final/validation-api-2.0.1.Fina
l.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.3.2.Final/jboss-logging-3.3.2.Final
.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-web/5.0.4.RELEASE/spring-web-5.0.4.RELEASE
.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-beans/5.0.4.RELEASE/spring-beans-5.0.4.REL
EASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.0.4.RELEASE/spring-webmvc-5.0.4.R
ELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-aop/5.0.4.RELEASE/spring-aop-5.0.4.RELEASE
.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-context/5.0.4.RELEASE/spring-context-5.0.4
.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-expression/5.0.4.RELEASE/spring-expression
-5.0.4.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.7.10/byte-buddy-1.7.10.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-core/5.0.4.RELEASE/spring-core-5.0.4.RELEA
SE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.0.4.RELEASE/spring-jcl-5.0.4.RELEASE
.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar
[DEBUG]  /Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.28/mysql-connector-java-8.0.28.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.11.4/protobuf-java-3.11.4.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.2/httpclient-4.5.2.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.9/httpcore-4.4.9.jar
[DEBUG]  /Users/<USER>/.m2/repository/commons-codec/commons-codec/1.11/commons-codec-1.11.jar
[DEBUG]  /Users/<USER>/.m2/repository/net/sourceforge/jexcelapi/jxl/2.6.12/jxl-2.6.12.jar
[DEBUG]  /Users/<USER>/.m2/repository/log4j/log4j/1.2.14/log4j-1.2.14.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.30/lombok-1.18.30.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.15.0/log4j-to-slf4j-2.15.0.
jar
[DEBUG]  /Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.25/slf4j-api-1.7.25.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.10.0/log4j-api-2.10.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.25/jul-to-slf4j-1.7.25.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.0.7.1/mybatis-plus-boot-star
ter-3.0.7.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.0.7.1/mybatis-plus-3.0.7.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.0.7.1/mybatis-plus-extension-3.
0.7.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.0.7.1/mybatis-plus-core-3.0.7.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.0.7.1/mybatis-plus-annotation-
3.0.7.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.0.0.RELEASE/spri
ng-boot-autoconfigure-2.0.0.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.0.0.RELEASE/sprin
g-boot-starter-jdbc-2.0.0.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/zaxxer/HikariCP/2.7.8/HikariCP-2.7.8.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.0.4.RELEASE/spring-jdbc-5.0.4.RELEA
SE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-redis/1.4.1.RELEASE/spri
ng-boot-starter-redis-1.4.1.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/2.0.5.RELEASE/spring-data-
redis-2.0.5.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.0.5.RELEASE/spring-da
ta-keyvalue-2.0.5.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.0.5.RELEASE/spring-dat
a-commons-2.0.5.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-tx/5.0.4.RELEASE/spring-tx-5.0.4.RELEASE.j
ar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-oxm/5.0.4.RELEASE/spring-oxm-5.0.4.RELEASE
.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.0.4.RELEASE/spring-conte
xt-support-5.0.4.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/redis/clients/jedis/2.9.0/jedis-2.9.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.5.0/commons-pool2-2.5.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/2.7.0/springfox-swagger2-2.7.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.5.13/swagger-annotations-1.5.13.jar
[DEBUG]  /Users/<USER>/.m2/repository/io/swagger/swagger-models/1.5.13/swagger-models-1.5.13.jar
[DEBUG]  /Users/<USER>/.m2/repository/io/springfox/springfox-spi/2.7.0/springfox-spi-2.7.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/io/springfox/springfox-core/2.7.0/springfox-core-2.7.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/io/springfox/springfox-schema/2.7.0/springfox-schema-2.7.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/2.7.0/springfox-swagger-common-
2.7.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/2.7.0/springfox-spring-web-2.7.0.ja
r
[DEBUG]  /Users/<USER>/.m2/repository/org/reflections/reflections/0.9.11/reflections-0.9.11.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/javassist/javassist/3.21.0-GA/javassist-3.21.0-GA.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/google/guava/guava/18.0/guava-18.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/fasterxml/classmate/1.3.4/classmate-1.3.4.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-pl
ugin-core-1.2.0.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/sprin
g-plugin-metadata-1.2.0.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.1.0.Final/mapstruct-1.1.0.Final.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-bootstrap-ui/1.6/swagger-bootstrap-ui-1.6
.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/aliyun/oss/aliyun-sdk-oss/3.10.1/aliyun-sdk-oss-3.10.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/jdom/jdom/1.1/jdom-1.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-core/3.4.0/aliyun-java-sdk-core-3.4.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ram/3.0.0/aliyun-java-sdk-ram-3.0.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java-sdk-sts-3.0.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4.2.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-kms/2.7.0/aliyun-java-sdk-kms-2.7.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.2/gson-2.8.2.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/itextpdf/itextpdf/5.5.13.1/itextpdf-5.5.13.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/commons-io/commons-io/1.3.2/commons-io-1.3.2.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/belerweb/pinyin4j/2.5.1/pinyin4j-2.5.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-starter/1.2.13/pagehelpe
r-spring-boot-starter-1.2.13.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.1.1/mybatis-spr
ing-boot-starter-2.1.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/2.1.1/mybat
is-spring-boot-autoconfigure-2.1.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.3/mybatis-3.5.3.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.0.3/mybatis-spring-2.0.3.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-autoconfigure/1.2.13/pag
ehelper-spring-boot-autoconfigure-1.2.13.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper/5.1.11/pagehelper-5.1.11.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/2.0/jsqlparser-2.0.jar
[DEBUG] Source roots:
[DEBUG]  /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java
[DEBUG]  /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/generated-sources/annotations
[DEBUG] Command line options:
[DEBUG] -d /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes -classpath /Users/<USER>/De
sktop/jshERP-0612-Cursor/jshERP-boot/target/classes:/Users/<USER>/.m2/repository/com/gitee/starblues/spring
boot-plugin-framework/2.2.1-RELEASE/springboot-plugin-framework-2.2.1-RELEASE.jar:/Users/<USER>/.m2/reposit
ory/org/pf4j/pf4j/3.1.0/pf4j-3.1.0.jar:/Users/<USER>/.m2/repository/com/github/zafarkhaja/java-semver/0.9.0
/java-semver-0.9.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.9.4/jacks
on-databind-2.9.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.9.0/jac
kson-annotations-2.9.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.9.4/jacks
on-core-2.9.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.9
.4/jackson-dataformat-yaml-2.9.4.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.19/snakeyaml-1.19.ja
r:/Users/<USER>/.m2/repository/com/gitee/starblues/springboot-plugin-framework-extension-mybatis/2.2.1-RELE
ASE/springboot-plugin-framework-extension-mybatis-2.2.1-RELEASE.jar:/Users/<USER>/.m2/repository/org/spring
framework/boot/spring-boot-starter-web/2.0.0.RELEASE/spring-boot-starter-web-2.0.0.RELEASE.jar:/Users/<USER>
i/.m2/repository/org/springframework/boot/spring-boot-starter/2.0.0.RELEASE/spring-boot-starter-2.0.0.RELEAS
E.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.0.0.RELEASE/spring-boot-2.0.0.REL
EASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.0.0.RELEASE/sp
ring-boot-starter-logging-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2
.3/logback-classic-1.2.3.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.
2.3.jar:/Users/<USER>/.m2/repository/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2
.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.0.0.RELEASE/spring-bo
ot-starter-json-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datat
ype-jdk8/2.9.4/jackson-datatype-jdk8-2.9.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/
jackson-datatype-jsr310/2.9.4/jackson-datatype-jsr310-2.9.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/
jackson/module/jackson-module-parameter-names/2.9.4/jackson-module-parameter-names-2.9.4.jar:/Users/<USER>/
.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.0.0.RELEASE/spring-boot-starter-tomcat-
2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/8.5.28/tomcat-embe
d-core-8.5.28.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/8.5.28/tomcat-embed-
el-8.5.28.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/8.5.28/tomcat-emb
ed-websocket-8.5.28.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.0.7.Fina
l/hibernate-validator-6.0.7.Final.jar:/Users/<USER>/.m2/repository/javax/validation/validation-api/2.0.1.Fi
nal/validation-api-2.0.1.Final.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.3.2.Final
/jboss-logging-3.3.2.Final.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.0.4.RELEASE/sp
ring-web-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.0.4.RELEASE/spri
ng-beans-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.0.4.RELEASE/spr
ing-webmvc-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.0.4.RELEASE/spri
ng-aop-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.0.4.RELEASE/spri
ng-context-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.0.4.RELEA
SE/spring-expression-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.7.10/byte-bu
ddy-1.7.10.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.0.4.RELEASE/spring-core-5.0.4
.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.0.4.RELEASE/spring-jcl-5.0.4.REL
EASE.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar:/Users/<USER>/.m2/re
pository/mysql/mysql-connector-java/8.0.28/mysql-connector-java-8.0.28.jar:/Users/<USER>/.m2/repository/com
/google/protobuf/protobuf-java/3.11.4/protobuf-java-3.11.4.jar:/Users/<USER>/.m2/repository/org/apache/http
components/httpclient/4.5.2/httpclient-4.5.2.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/htt
pcore/4.4.9/httpcore-4.4.9.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.11/commons-codec-
1.11.jar:/Users/<USER>/.m2/repository/net/sourceforge/jexcelapi/jxl/2.6.12/jxl-2.6.12.jar:/Users/<USER>/.m
2/repository/log4j/log4j/1.2.14/log4j-1.2.14.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18
.30/lombok-1.18.30.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.15.0/log4j-to
-slf4j-2.15.0.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.25/slf4j-api-1.7.25.jar:/Users/<USER>
ni/.m2/repository/org/apache/logging/log4j/log4j-api/2.10.0/log4j-api-2.10.0.jar:/Users/<USER>/.m2/reposito
ry/org/slf4j/jul-to-slf4j/1.7.25/jul-to-slf4j-1.7.25.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-
plus-boot-starter/3.0.7.1/mybatis-plus-boot-starter-3.0.7.1.jar:/Users/<USER>/.m2/repository/com/baomidou/m
ybatis-plus/3.0.7.1/mybatis-plus-3.0.7.1.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extensi
on/3.0.7.1/mybatis-plus-extension-3.0.7.1.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3
.0.7.1/mybatis-plus-core-3.0.7.1.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.0.
7.1/mybatis-plus-annotation-3.0.7.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-a
utoconfigure/2.0.0.RELEASE/spring-boot-autoconfigure-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/spr
ingframework/boot/spring-boot-starter-jdbc/2.0.0.RELEASE/spring-boot-starter-jdbc-2.0.0.RELEASE.jar:/Users/<USER>
acmini/.m2/repository/com/zaxxer/HikariCP/2.7.8/HikariCP-2.7.8.jar:/Users/<USER>/.m2/repository/org/springf
ramework/spring-jdbc/5.0.4.RELEASE/spring-jdbc-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springfra
mework/boot/spring-boot-starter-redis/1.4.1.RELEASE/spring-boot-starter-redis-1.4.1.RELEASE.jar:/Users/<USER>
ni/.m2/repository/org/springframework/data/spring-data-redis/2.0.5.RELEASE/spring-data-redis-2.0.5.RELEASE.j
ar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.0.5.RELEASE/spring-data-key
value-2.0.5.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.0.5.REL
EASE/spring-data-commons-2.0.5.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.0.4
.RELEASE/spring-tx-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/5.0.4.RELE
ASE/spring-oxm-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.
0.4.RELEASE/spring-context-support-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/redis/clients/jedis/2.9.0
/jedis-2.9.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.5.0/commons-pool2-2.5.0.ja
r:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/2.7.0/springfox-swagger2-2.7.0.jar:/Users/<USER>
cmini/.m2/repository/io/swagger/swagger-annotations/1.5.13/swagger-annotations-1.5.13.jar:/Users/<USER>/.m2
/repository/io/swagger/swagger-models/1.5.13/swagger-models-1.5.13.jar:/Users/<USER>/.m2/repository/io/spri
ngfox/springfox-spi/2.7.0/springfox-spi-2.7.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-core/
2.7.0/springfox-core-2.7.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-schema/2.7.0/springfox-s
chema-2.7.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/2.7.0/springfox-swagger-
common-2.7.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/2.7.0/springfox-spring-web-
2.7.0.jar:/Users/<USER>/.m2/repository/org/reflections/reflections/0.9.11/reflections-0.9.11.jar:/Users/<USER>
mini/.m2/repository/org/javassist/javassist/3.21.0-GA/javassist-3.21.0-GA.jar:/Users/<USER>/.m2/repository/
com/google/guava/guava/18.0/guava-18.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.3.4/class
mate-1.3.4.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spr
ing-plugin-core-1.2.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-met
adata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/mapstruct/map
struct/1.1.0.Final/mapstruct-1.1.0.Final.jar:/Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-boots
trap-ui/1.6/swagger-bootstrap-ui-1.6.jar:/Users/<USER>/.m2/repository/com/aliyun/oss/aliyun-sdk-oss/3.10.1/
aliyun-sdk-oss-3.10.1.jar:/Users/<USER>/.m2/repository/org/jdom/jdom/1.1/jdom-1.1.jar:/Users/<USER>/.m2/re
pository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar:/Users/<USER>/.m2/repository/stax/stax-api/1.0
.1/stax-api-1.0.1.jar:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-core/3.4.0/aliyun-java-sdk-co
re-3.4.0.jar:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ram/3.0.0/aliyun-java-sdk-ram-3.0.0.ja
r:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java-sdk-sts-3.0.0.jar:/Users/<USER>
cmini/.m2/repository/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4.2.0.jar:/Users/<USER>/.m2/r
epository/com/aliyun/aliyun-java-sdk-kms/2.7.0/aliyun-java-sdk-kms-2.7.0.jar:/Users/<USER>/.m2/repository/c
om/google/code/gson/gson/2.8.2/gson-2.8.2.jar:/Users/<USER>/.m2/repository/com/itextpdf/itextpdf/5.5.13.1/i
textpdf-5.5.13.1.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/1.3.2/commons-io-1.3.2.jar:/Users/<USER>
acmini/.m2/repository/com/belerweb/pinyin4j/2.5.1/pinyin4j-2.5.1.jar:/Users/<USER>/.m2/repository/com/githu
b/pagehelper/pagehelper-spring-boot-starter/1.2.13/pagehelper-spring-boot-starter-1.2.13.jar:/Users/<USER>/
.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.1.1/mybatis-spring-boot-starter-2.1.1.j
ar:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/2.1.1/mybatis-spr
ing-boot-autoconfigure-2.1.1.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.3/mybatis-3.5.3.jar:/
Users/macmini/.m2/repository/org/mybatis/mybatis-spring/2.0.3/mybatis-spring-2.0.3.jar:/Users/<USER>/.m2/re
pository/com/github/pagehelper/pagehelper-spring-boot-autoconfigure/1.2.13/pagehelper-spring-boot-autoconfig
ure-1.2.13.jar:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper/5.1.11/pagehelper-5.1.11.jar:/
Users/macmini/.m2/repository/com/github/jsqlparser/jsqlparser/2.0/jsqlparser-2.0.jar: -sourcepath /Users/<USER>
mini/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java:/Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-b
oot/target/generated-sources/annotations: -s /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/ge
nerated-sources/annotations -processorpath /Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.30/lo
mbok-1.18.30.jar: -g -parameters -nowarn -target 1.8 -source 1.8 -encoding UTF-8
[DEBUG] incrementalBuildHelper#beforeRebuildExecution
[INFO] Compiling 277 source files to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes
[DEBUG] incrementalBuildHelper#afterRebuildExecution
[INFO] -------------------------------------------------------------
[ERROR] COMPILATION ERROR : 
[INFO] -------------------------------------------------------------
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/controller/WorkOrder
Controller.java:[6,29] 找不到符号
  符号:   类 ErpInfo
  位置: 程序包 com.jsh.erp.constants
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/controller/Productio
nController.java:[6,29] 找不到符号
  符号:   类 ErpInfo
  位置: 程序包 com.jsh.erp.constants
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[4,39] 找不到符号
  符号:   类 WorkOrderItemExample
  位置: 程序包 com.jsh.erp.datasource.entities
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[18,25] 找不到符号
  符号:   类 WorkOrderItemExample
  位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[23,25] 找不到符号
  符号:   类 WorkOrderItemExample
  位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[43,41] 找不到符号
  符号:   类 WorkOrderItemExample
  位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[53,91] 找不到符号
  符号:   类 WorkOrderItemExample
  位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[58,82] 找不到符号
  符号:   类 WorkOrderItemExample
  位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[INFO] 8 errors 
[INFO] -------------------------------------------------------------
[INFO] ------------------------------------------------------------------------
[INFO] BUILD FAILURE
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  5.681 s
[INFO] Finished at: 2025-06-21T17:42:24+08:00
[INFO] ------------------------------------------------------------------------
[ERROR] Failed to execute goal org.apache.maven.plugins:maven-compiler-plugin:3.7.0:compile (default-compile
) on project jshERP-boot: Compilation failure: Compilation failure: 
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/controller/WorkOrder
Controller.java:[6,29] 找不到符号
[ERROR]   符号:   类 ErpInfo
[ERROR]   位置: 程序包 com.jsh.erp.constants
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/controller/Productio
nController.java:[6,29] 找不到符号
[ERROR]   符号:   类 ErpInfo
[ERROR]   位置: 程序包 com.jsh.erp.constants
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[4,39] 找不到符号
[ERROR]   符号:   类 WorkOrderItemExample
[ERROR]   位置: 程序包 com.jsh.erp.datasource.entities
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[18,25] 找不到符号
[ERROR]   符号:   类 WorkOrderItemExample
[ERROR]   位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[23,25] 找不到符号
[ERROR]   符号:   类 WorkOrderItemExample
[ERROR]   位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[43,41] 找不到符号
[ERROR]   符号:   类 WorkOrderItemExample
[ERROR]   位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[53,91] 找不到符号
[ERROR]   符号:   类 WorkOrderItemExample
[ERROR]   位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[58,82] 找不到符号
[ERROR]   符号:   类 WorkOrderItemExample
[ERROR]   位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] -> [Help 1]
org.apache.maven.lifecycle.LifecycleExecutionException: Failed to execute goal org.apache.maven.plugins:mave
n-compiler-plugin:3.7.0:compile (default-compile) on project jshERP-boot: Compilation failure
    at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:333)
    at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
    at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
    at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
    at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
    at org.apache.maven.lifecycle.internal.MojoExecutor.executeForkedExecutions (MojoExecutor.java:448)
    at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:311)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
    at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
    at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
    at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
    at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:
105)
    at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:
73)
    at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreade
dBuilder.java:53)
    at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
    at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
    at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
    at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
    at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
    at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
    at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
    at jdk.internal.reflect.DirectMethodHandleAccessor.invoke (DirectMethodHandleAccessor.java:103)
    at java.lang.reflect.Method.invoke (Method.java:580)
    at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
    at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
    at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
    at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
Caused by: org.apache.maven.plugin.compiler.CompilationFailureException: Compilation failure
    at org.apache.maven.plugin.compiler.AbstractCompilerMojo.execute (AbstractCompilerMojo.java:1161)
    at org.apache.maven.plugin.compiler.CompilerMojo.execute (CompilerMojo.java:168)
    at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
    at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
    at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
    at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
    at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
    at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
    at org.apache.maven.lifecycle.internal.MojoExecutor.executeForkedExecutions (MojoExecutor.java:448)
    at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:311)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
    at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
    at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
    at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
    at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:
105)
    at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:
73)
    at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreade
dBuilder.java:53)
    at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
    at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
    at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
    at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
    at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
    at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
    at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
    at jdk.internal.reflect.DirectMethodHandleAccessor.invoke (DirectMethodHandleAccessor.java:103)
    at java.lang.reflect.Method.invoke (Method.java:580)
    at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
    at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
    at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
    at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
[ERROR] 
[ERROR] 
[ERROR] For more information about the errors and possible solutions, please read the following articles:
[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/MojoFailureException
[DEBUG] Shutting down adapter factory; available factories [file-lock, rwlock-local, semaphore-local, noop];
 available name mappers [discriminating, file-gav, file-hgav, file-static, gav, static]
[DEBUG] Shutting down 'file-lock' factory
[DEBUG] Shutting down 'rwlock-local' factory
[DEBUG] Shutting down 'semaphore-local' factory
[DEBUG] Shutting down 'noop' factory
macmini@liangchaoweideMac-mini jshERP-0612-Cursor %
```

---



---

鉴于我无法直接查看命令输出，我将采用一种更具目标性的方法来分析日志文件。

**新诊断策略：搜索关键错误**

我将使用 `grep` 命令在 `maven_debug_log.txt` 文件中搜索 "ERROR" 关键字，并显示其周围的50行上下文。这能帮助我快速聚焦于最核心的错误信息，而不是浏览数千行调试日志。

---

```bash
grep -i -C 50 "ERROR" jshERP-boot/maven_debug_log.txt
```

```
macmini@liangchaoweideMac-mini jshERP-0612-Cursor % grep -i -C 50 "ERROR" jshERP-boot/maven_debug_log.txt
[DEBUG]   Imported: org.apache.maven.reporting < plexus.core
[DEBUG]   Imported: org.apache.maven.repository < plexus.core
[DEBUG]   Imported: org.apache.maven.rtinfo < plexus.core
[DEBUG]   Imported: org.apache.maven.settings < plexus.core
[DEBUG]   Imported: org.apache.maven.toolchain < plexus.core
[DEBUG]   Imported: org.apache.maven.usability < plexus.core
[DEBUG]   Imported: org.apache.maven.wagon.* < plexus.core
[DEBUG]   Imported: org.apache.maven.wagon.authentication < plexus.core
[DEBUG]   Imported: org.apache.maven.wagon.authorization < plexus.core
[DEBUG]   Imported: org.apache.maven.wagon.events < plexus.core
[DEBUG]   Imported: org.apache.maven.wagon.observers < plexus.core
[DEBUG]   Imported: org.apache.maven.wagon.proxy < plexus.core
[DEBUG]   Imported: org.apache.maven.wagon.repository < plexus.core
[DEBUG]   Imported: org.apache.maven.wagon.resource < plexus.core
[DEBUG]   Imported: org.codehaus.classworlds < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.* < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.classworlds < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.component < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.configuration < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.container < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.context < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.lifecycle < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.logging < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.personality < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.util.xml.Xpp3Dom < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.util.xml.pull.XmlPullParser < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.util.xml.pull.XmlPullParserException < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.util.xml.pull.XmlSerializer < plexus.core
[DEBUG]   Imported: org.eclipse.aether.* < plexus.core
[DEBUG]   Imported: org.eclipse.aether.artifact < plexus.core
[DEBUG]   Imported: org.eclipse.aether.collection < plexus.core
[DEBUG]   Imported: org.eclipse.aether.deployment < plexus.core
[DEBUG]   Imported: org.eclipse.aether.graph < plexus.core
[DEBUG]   Imported: org.eclipse.aether.impl < plexus.core
[DEBUG]   Imported: org.eclipse.aether.installation < plexus.core
[DEBUG]   Imported: org.eclipse.aether.internal.impl < plexus.core
[DEBUG]   Imported: org.eclipse.aether.metadata < plexus.core
[DEBUG]   Imported: org.eclipse.aether.repository < plexus.core
[DEBUG]   Imported: org.eclipse.aether.resolution < plexus.core
[DEBUG]   Imported: org.eclipse.aether.spi < plexus.core
[DEBUG]   Imported: org.eclipse.aether.transfer < plexus.core
[DEBUG]   Imported: org.eclipse.aether.util < plexus.core
[DEBUG]   Imported: org.eclipse.aether.version < plexus.core
[DEBUG]   Imported: org.fusesource.jansi.* < plexus.core
[DEBUG]   Imported: org.slf4j.* < plexus.core
[DEBUG]   Imported: org.slf4j.event.* < plexus.core
[DEBUG]   Imported: org.slf4j.helpers.* < plexus.core
[DEBUG]   Imported: org.slf4j.spi.* < plexus.core
[DEBUG] Populating class realm maven.api
[DEBUG] Created adapter factory; available factories [file-lock, rwlock-local, semaphore-local, noop]; avail
able name mappers [discriminating, file-gav, file-hgav, file-static, gav, static]
[INFO] Error stacktraces are turned on.
[DEBUG] Message scheme: color
[DEBUG] Message styles: debug info warning error success failure strong mojo project
[DEBUG] Reading global settings from /opt/homebrew/Cellar/maven/3.9.9/libexec/conf/settings.xml
[DEBUG] Reading user settings from /Users/<USER>/.m2/settings.xml
[DEBUG] Reading global toolchains from /opt/homebrew/Cellar/maven/3.9.9/libexec/conf/toolchains.xml
[DEBUG] Reading user toolchains from /Users/<USER>/.m2/toolchains.xml
[DEBUG] Using local repository at /Users/<USER>/.m2/repository
[DEBUG] Using manager EnhancedLocalRepositoryManager with priority 10.0 for /Users/<USER>/.m2/repository
[INFO] Scanning for projects...
[DEBUG] Creating adapter using nameMapper 'gav' and factory 'rwlock-local'
[DEBUG] Extension realms for project com.jsh:jshERP-boot:jar:3.5-SNAPSHOT: (none)
[DEBUG] Looking up lifecycle mappings for packaging jar from ClassRealm[plexus.core, parent: null]
[DEBUG] Extension realms for project org.springframework.boot:spring-boot-starter-parent:pom:2.0.0.RELEASE: 
(none)
[DEBUG] Looking up lifecycle mappings for packaging pom from ClassRealm[plexus.core, parent: null]
[DEBUG] Extension realms for project org.springframework.boot:spring-boot-dependencies:pom:2.0.0.RELEASE: (n
one)
[DEBUG] Looking up lifecycle mappings for packaging pom from ClassRealm[plexus.core, parent: null]
[DEBUG] Resolving plugin prefix spring-boot from [org.apache.maven.plugins, org.codehaus.mojo]
[DEBUG] Resolved plugin prefix spring-boot to org.springframework.boot:spring-boot-maven-plugin from POM com
.jsh:jshERP-boot:jar:3.5-SNAPSHOT
[DEBUG] === REACTOR BUILD PLAN ================================================
[DEBUG] Project: com.jsh:jshERP-boot:jar:3.5-SNAPSHOT
[DEBUG] Tasks:   [spring-boot:run]
[DEBUG] Style:   Regular
[DEBUG] =======================================================================
[INFO] 
[INFO] ------------------------< com.jsh:jshERP-boot >-------------------------
[INFO] Building jshERP-boot 3.5-SNAPSHOT
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[DEBUG] Resolving plugin prefix spring-boot from [org.apache.maven.plugins, org.codehaus.mojo]
[DEBUG] Resolved plugin prefix spring-boot to org.springframework.boot:spring-boot-maven-plugin from POM com
.jsh:jshERP-boot:jar:3.5-SNAPSHOT
[DEBUG] Lifecycle clean -> [pre-clean, clean, post-clean]
[DEBUG] Lifecycle default -> [validate, initialize, generate-sources, process-sources, generate-resources, p
rocess-resources, compile, process-classes, generate-test-sources, process-test-sources, generate-test-resou
rces, process-test-resources, test-compile, process-test-classes, test, prepare-package, package, pre-integr
ation-test, integration-test, post-integration-test, verify, install, deploy]
[DEBUG] Lifecycle site -> [pre-site, site, post-site, site-deploy]
[DEBUG] Lifecycle clean -> [pre-clean, clean, post-clean]
[DEBUG] Lifecycle default -> [validate, initialize, generate-sources, process-sources, generate-resources, p
rocess-resources, compile, process-classes, generate-test-sources, process-test-sources, generate-test-resou
rces, process-test-resources, test-compile, process-test-classes, test, prepare-package, package, pre-integr
ation-test, integration-test, post-integration-test, verify, install, deploy]
[DEBUG] Lifecycle site -> [pre-site, site, post-site, site-deploy]
[DEBUG] === PROJECT BUILD PLAN ================================================
[DEBUG] Project:       com.jsh:jshERP-boot:3.5-SNAPSHOT
[DEBUG] Dependencies (collect): []
[DEBUG] Dependencies (resolve): [test]
[DEBUG] Repositories (dependencies): [spring-milestone (https://repo.spring.io/milestone, default, releases)
, spring-snapshot (https://repo.spring.io/snapshot, default, releases+snapshots), rabbit-milestone (https://
dl.bintray.com/rabbitmq/maven-milestones, default, releases), central (https://repo.maven.apache.org/maven2,
 default, releases)]
[DEBUG] Repositories (plugins)     : [central (https://repo.maven.apache.org/maven2, default, releases)]
[DEBUG] --- init fork of com.jsh:jshERP-boot:3.5-SNAPSHOT for org.springframework.boot:spring-boot-maven-plu
gin:2.0.3.RELEASE:run (default-cli) ---
[DEBUG] Dependencies (collect): []
[DEBUG] Dependencies (resolve): [compile, test]
[DEBUG] -----------------------------------------------------------------------
[DEBUG] Goal:          org.springframework.boot:spring-boot-maven-plugin:2.0.3.RELEASE:build-info (build-inf
o)
[DEBUG] Style:         Regular
[DEBUG] Configuration: <?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <outputFile default-value="${project.build.outputDirectory}/META-INF/build-info.properties"/>
  <project default-value="${project}"/>
</configuration>
[DEBUG] -----------------------------------------------------------------------
[DEBUG] Goal:          org.apache.maven.plugins:maven-resources-plugin:3.0.1:resources (default-resources)
[DEBUG] Style:         Regular
[DEBUG] Configuration: <?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <addDefaultExcludes default-value="true"/>
  <buildFilters default-value="${project.build.filters}"/>
  <delimiters>
    <delimiter>@</delimiter>
  </delimiters>
  <encoding default-value="${project.build.sourceEncoding}"/>
  <escapeString default-value="\"/>
  <escapeWindowsPaths default-value="true"/>
  <fileNameFiltering default-value="false"/>
  <includeEmptyDirs default-value="false"/>
  <outputDirectory default-value="${project.build.outputDirectory}"/>
  <overwrite default-value="false"/>
  <project default-value="${project}"/>
  <resources default-value="${project.resources}"/>
  <session default-value="${session}"/>
  <skip default-value="false">${maven.resources.skip}</skip>
  <supportMultiLineFiltering default-value="false"/>
  <useBuildFilters default-value="true"/>
  <useDefaultDelimiters default-value="true">false</useDefaultDelimiters>
</configuration>
[DEBUG] -----------------------------------------------------------------------
[DEBUG] Goal:          org.apache.maven.plugins:maven-compiler-plugin:3.7.0:compile (default-compile)
[DEBUG] Style:         Regular
[DEBUG] Configuration: <?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <annotationProcessorPaths>
    <path>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>1.18.30</version>
    </path>
  </annotationProcessorPaths>
  <basedir default-value="${basedir}"/>
  <buildDirectory default-value="${project.build.directory}"/>
  <compilePath default-value="${project.compileClasspathElements}"/>
  <compileSourceRoots default-value="${project.compileSourceRoots}"/>
  <compilerId default-value="javac">${maven.compiler.compilerId}</compilerId>
  <compilerReuseStrategy default-value="${reuseCreated}">${maven.compiler.compilerReuseStrategy}</compilerRe
useStrategy>
  <compilerVersion>${maven.compiler.compilerVersion}</compilerVersion>
  <debug default-value="true">${maven.compiler.debug}</debug>
  <debuglevel>${maven.compiler.debuglevel}</debuglevel>
  <encoding default-value="${project.build.sourceEncoding}">${encoding}</encoding>
  <executable>${maven.compiler.executable}</executable>
  <failOnError default-value="true">${maven.compiler.failOnError}</failOnError>
  <failOnWarning default-value="false">${maven.compiler.failOnWarning}</failOnWarning>
  <forceJavacCompilerUse default-value="false">${maven.compiler.forceJavacCompilerUse}</forceJavacCompilerUs
e>
  <fork default-value="false">${maven.compiler.fork}</fork>
  <generatedSourcesDirectory default-value="${project.build.directory}/generated-sources/annotations"/>
  <maxmem>${maven.compiler.maxmem}</maxmem>
  <meminitial>${maven.compiler.meminitial}</meminitial>
  <mojoExecution default-value="${mojoExecution}"/>
  <optimize default-value="false">${maven.compiler.optimize}</optimize>
  <outputDirectory default-value="${project.build.outputDirectory}"/>
  <parameters default-value="false">true</parameters>
  <project default-value="${project}"/>
  <projectArtifact default-value="${project.artifact}"/>
  <release>${maven.compiler.release}</release>
  <session default-value="${session}"/>
  <showDeprecation default-value="false">${maven.compiler.showDeprecation}</showDeprecation>
  <showWarnings default-value="false">${maven.compiler.showWarnings}</showWarnings>
  <skipMain>${maven.main.skip}</skipMain>
  <skipMultiThreadWarning default-value="false">${maven.compiler.skipMultiThreadWarning}</skipMultiThreadWar
ning>
  <source default-value="1.5">1.8</source>
  <staleMillis default-value="0">${lastModGranularityMs}</staleMillis>
  <target default-value="1.5">1.8</target>
  <useIncrementalCompilation default-value="true">${maven.compiler.useIncrementalCompilation}</useIncrementa
lCompilation>
  <verbose default-value="false">${maven.compiler.verbose}</verbose>
</configuration>
[DEBUG] -----------------------------------------------------------------------
[DEBUG] Goal:          org.apache.maven.plugins:maven-resources-plugin:3.0.1:testResources (default-testReso
urces)
[DEBUG] Style:         Regular
[DEBUG] Configuration: <?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <addDefaultExcludes default-value="true"/>
  <buildFilters default-value="${project.build.filters}"/>
  <delimiters>
    <delimiter>@</delimiter>
  </delimiters>
  <encoding default-value="${project.build.sourceEncoding}"/>
  <escapeString default-value="\"/>
  <escapeWindowsPaths default-value="true"/>
  <fileNameFiltering default-value="false"/>
  <includeEmptyDirs default-value="false"/>
  <outputDirectory default-value="${project.build.testOutputDirectory}"/>
  <overwrite default-value="false"/>
  <project default-value="${project}"/>
  <resources default-value="${project.testResources}"/>
  <session default-value="${session}"/>
  <skip default-value="false">${maven.test.skip}</skip>
  <supportMultiLineFiltering default-value="false"/>
  <useBuildFilters default-value="true"/>
  <useDefaultDelimiters default-value="true">false</useDefaultDelimiters>
</configuration>
[DEBUG] -----------------------------------------------------------------------
[DEBUG] Goal:          org.apache.maven.plugins:maven-compiler-plugin:3.7.0:testCompile (default-testCompile
)
[DEBUG] Style:         Regular
[DEBUG] Configuration: <?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <annotationProcessorPaths>
    <path>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>1.18.30</version>
    </path>
  </annotationProcessorPaths>
  <basedir default-value="${basedir}"/>
  <buildDirectory default-value="${project.build.directory}"/>
  <compilePath default-value="${project.compileClasspathElements}"/>
  <compileSourceRoots default-value="${project.testCompileSourceRoots}"/>
  <compilerId default-value="javac">${maven.compiler.compilerId}</compilerId>
  <compilerReuseStrategy default-value="${reuseCreated}">${maven.compiler.compilerReuseStrategy}</compilerRe
useStrategy>
  <compilerVersion>${maven.compiler.compilerVersion}</compilerVersion>
  <debug default-value="true">${maven.compiler.debug}</debug>
  <debuglevel>${maven.compiler.debuglevel}</debuglevel>
  <encoding default-value="${project.build.sourceEncoding}">${encoding}</encoding>
  <executable>${maven.compiler.executable}</executable>
  <failOnError default-value="true">${maven.compiler.failOnError}</failOnError>
  <failOnWarning default-value="false">${maven.compiler.failOnWarning}</failOnWarning>
  <forceJavacCompilerUse default-value="false">${maven.compiler.forceJavacCompilerUse}</forceJavacCompilerUs
e>
  <fork default-value="false">${maven.compiler.fork}</fork>
  <generatedTestSourcesDirectory default-value="${project.build.directory}/generated-test-sources/test-annot
ations"/>
  <maxmem>${maven.compiler.maxmem}</maxmem>
  <meminitial>${maven.compiler.meminitial}</meminitial>
  <mojoExecution default-value="${mojoExecution}"/>
  <optimize default-value="false">${maven.compiler.optimize}</optimize>
  <outputDirectory default-value="${project.build.testOutputDirectory}"/>
  <parameters default-value="false">true</parameters>
  <project default-value="${project}"/>
  <release>${maven.compiler.release}</release>
  <session default-value="${session}"/>
  <showDeprecation default-value="false">${maven.compiler.showDeprecation}</showDeprecation>
  <showWarnings default-value="false">${maven.compiler.showWarnings}</showWarnings>
  <skip>${maven.test.skip}</skip>
  <skipMultiThreadWarning default-value="false">${maven.compiler.skipMultiThreadWarning}</skipMultiThreadWar
ning>
  <source default-value="1.5">1.8</source>
  <staleMillis default-value="0">${lastModGranularityMs}</staleMillis>
  <target default-value="1.5">1.8</target>
  <testPath default-value="${project.testClasspathElements}"/>
  <testRelease>${maven.compiler.testRelease}</testRelease>
  <testSource>${maven.compiler.testSource}</testSource>
  <testTarget>${maven.compiler.testTarget}</testTarget>
  <useIncrementalCompilation default-value="true">${maven.compiler.useIncrementalCompilation}</useIncrementa
lCompilation>
  <verbose default-value="false">${maven.compiler.verbose}</verbose>
</configuration>
[DEBUG] --- exit fork of com.jsh:jshERP-boot:3.5-SNAPSHOT for org.springframework.boot:spring-boot-maven-plu
gin:2.0.3.RELEASE:run (default-cli) ---
[DEBUG] -----------------------------------------------------------------------
[DEBUG] Goal:          org.springframework.boot:spring-boot-maven-plugin:2.0.3.RELEASE:run (default-cli)
[DEBUG] Style:         Regular
[DEBUG] Configuration: <?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <addResources default-value="false">${spring-boot.run.addResources}</addResources>
  <agent>${spring-boot.run.agent}</agent>
  <arguments>${spring-boot.run.arguments}</arguments>
  <classesDirectory default-value="${project.build.outputDirectory}"/>
  <excludeArtifactIds default-value="">${spring-boot.excludeArtifactIds}</excludeArtifactIds>
  <excludeGroupIds default-value="">${spring-boot.excludeGroupIds}</excludeGroupIds>
  <excludes>${spring-boot.excludes}</excludes>
  <folders>${spring-boot.run.folders}</folders>
  <fork>${spring-boot.run.fork}</fork>
  <includes>${spring-boot.includes}</includes>
  <jvmArguments>${spring-boot.run.jvmArguments}</jvmArguments>
  <mainClass>${start-class}</mainClass>
  <noverify>${spring-boot.run.noverify}</noverify>
  <profiles>${spring-boot.run.profiles}</profiles>
  <project default-value="${project}"/>
  <skip default-value="false">${spring-boot.run.skip}</skip>
  <useTestClasspath default-value="false">${spring-boot.run.useTestClasspath}</useTestClasspath>
--
[DEBUG]       org.sonatype.aether:aether-util:jar:1.7:compile
[DEBUG]       org.codehaus.plexus:plexus-interpolation:jar:1.14:compile
[DEBUG]       org.codehaus.plexus:plexus-classworlds:jar:2.2.3:compile
[DEBUG]       org.codehaus.plexus:plexus-component-annotations:jar:1.6:compile (version managed from default
)
[DEBUG]       org.sonatype.plexus:plexus-sec-dispatcher:jar:1.3:compile
[DEBUG]          org.sonatype.plexus:plexus-cipher:jar:1.4:compile
[DEBUG]    org.apache.maven.shared:maven-shared-utils:jar:3.1.0:compile
[DEBUG]       commons-io:commons-io:jar:2.5:compile
[DEBUG]    org.apache.maven.shared:maven-shared-incremental:jar:1.1:compile
[DEBUG]    org.codehaus.plexus:plexus-java:jar:0.9.2:compile
[DEBUG]       org.ow2.asm:asm:jar:6.0_BETA:compile
[DEBUG]       com.thoughtworks.qdox:qdox:jar:2.0-M7:compile
[DEBUG]    org.codehaus.plexus:plexus-compiler-api:jar:2.8.2:compile
[DEBUG]    org.codehaus.plexus:plexus-compiler-manager:jar:2.8.2:compile
[DEBUG]    org.codehaus.plexus:plexus-compiler-javac:jar:2.8.2:runtime
[DEBUG] Created new class realm plugin>org.apache.maven.plugins:maven-compiler-plugin:3.7.0
[DEBUG] Importing foreign packages into class realm plugin>org.apache.maven.plugins:maven-compiler-plugin:3.
7.0
[DEBUG]   Imported:  < maven.api
[DEBUG] Populating class realm plugin>org.apache.maven.plugins:maven-compiler-plugin:3.7.0
[DEBUG]   Included: org.apache.maven.plugins:maven-compiler-plugin:jar:3.7.0
[DEBUG]   Included: org.sonatype.sisu:sisu-inject-bean:jar:1.4.2
[DEBUG]   Included: org.sonatype.sisu:sisu-guice:jar:noaop:2.1.7
[DEBUG]   Included: org.codehaus.plexus:plexus-utils:jar:2.0.4
[DEBUG]   Included: org.sonatype.aether:aether-util:jar:1.7
[DEBUG]   Included: org.codehaus.plexus:plexus-interpolation:jar:1.14
[DEBUG]   Included: org.codehaus.plexus:plexus-component-annotations:jar:1.6
[DEBUG]   Included: org.sonatype.plexus:plexus-sec-dispatcher:jar:1.3
[DEBUG]   Included: org.sonatype.plexus:plexus-cipher:jar:1.4
[DEBUG]   Included: org.apache.maven.shared:maven-shared-utils:jar:3.1.0
[DEBUG]   Included: commons-io:commons-io:jar:2.5
[DEBUG]   Included: org.apache.maven.shared:maven-shared-incremental:jar:1.1
[DEBUG]   Included: org.codehaus.plexus:plexus-java:jar:0.9.2
[DEBUG]   Included: org.ow2.asm:asm:jar:6.0_BETA
[DEBUG]   Included: com.thoughtworks.qdox:qdox:jar:2.0-M7
[DEBUG]   Included: org.codehaus.plexus:plexus-compiler-api:jar:2.8.2
[DEBUG]   Included: org.codehaus.plexus:plexus-compiler-manager:jar:2.8.2
[DEBUG]   Included: org.codehaus.plexus:plexus-compiler-javac:jar:2.8.2
[DEBUG] Loading mojo org.apache.maven.plugins:maven-compiler-plugin:3.7.0:compile from plugin realm ClassRea
lm[plugin>org.apache.maven.plugins:maven-compiler-plugin:3.7.0, parent: jdk.internal.loader.ClassLoaders$App
ClassLoader@18ff02e4]
[DEBUG] Configuring mojo execution 'org.apache.maven.plugins:maven-compiler-plugin:3.7.0:compile:default-com
pile' with basic configurator -->
[DEBUG]   (s) groupId = org.projectlombok
[DEBUG]   (s) artifactId = lombok
[DEBUG]   (s) version = 1.18.30
[DEBUG]   (f) annotationProcessorPaths = [org.projectlombok:lombok:1.18.30.jar]
[DEBUG]   (f) basedir = /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot
[DEBUG]   (f) buildDirectory = /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target
[DEBUG]   (f) compilePath = [/Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes, /Users/<USER>
cmini/.m2/repository/com/gitee/starblues/springboot-plugin-framework/2.2.1-RELEASE/springboot-plugin-framewo
rk-2.2.1-RELEASE.jar, /Users/<USER>/.m2/repository/org/pf4j/pf4j/3.1.0/pf4j-3.1.0.jar, /Users/<USER>/.m2/r
epository/com/github/zafarkhaja/java-semver/0.9.0/java-semver-0.9.0.jar, /Users/<USER>/.m2/repository/com/f
asterxml/jackson/core/jackson-databind/2.9.4/jackson-databind-2.9.4.jar, /Users/<USER>/.m2/repository/com/f
asterxml/jackson/core/jackson-annotations/2.9.0/jackson-annotations-2.9.0.jar, /Users/<USER>/.m2/repository
/com/fasterxml/jackson/core/jackson-core/2.9.4/jackson-core-2.9.4.jar, /Users/<USER>/.m2/repository/com/fas
terxml/jackson/dataformat/jackson-dataformat-yaml/2.9.4/jackson-dataformat-yaml-2.9.4.jar, /Users/<USER>/.m
2/repository/org/yaml/snakeyaml/1.19/snakeyaml-1.19.jar, /Users/<USER>/.m2/repository/com/gitee/starblues/s
pringboot-plugin-framework-extension-mybatis/2.2.1-RELEASE/springboot-plugin-framework-extension-mybatis-2.2
.1-RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.0.0.RELEASE
/spring-boot-starter-web-2.0.0.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/boot/spring-bo
ot-starter/2.0.0.RELEASE/spring-boot-starter-2.0.0.RELEASE.jar, /Users/<USER>/.m2/repository/org/springfram
ework/boot/spring-boot/2.0.0.RELEASE/spring-boot-2.0.0.RELEASE.jar, /Users/<USER>/.m2/repository/org/spring
framework/boot/spring-boot-starter-logging/2.0.0.RELEASE/spring-boot-starter-logging-2.0.0.RELEASE.jar, /Use
rs/macmini/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, /Users/<USER>/.m2
/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, /Users/<USER>/.m2/repository/javax/an
notation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2.jar, /Users/<USER>/.m2/repository/org/spring
framework/boot/spring-boot-starter-json/2.0.0.RELEASE/spring-boot-starter-json-2.0.0.RELEASE.jar, /Users/<USER>
mini/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.9.4/jackson-datatype-jdk8-2.9.4.j
ar, /Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.9.4/jackson-datat
ype-jsr310-2.9.4.jar, /Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-na
mes/2.9.4/jackson-module-parameter-names-2.9.4.jar, /Users/<USER>/.m2/repository/org/springframework/boot/s
pring-boot-starter-tomcat/2.0.0.RELEASE/spring-boot-starter-tomcat-2.0.0.RELEASE.jar, /Users/<USER>/.m2/rep
ository/org/apache/tomcat/embed/tomcat-embed-core/8.5.28/tomcat-embed-core-8.5.28.jar, /Users/<USER>/.m2/re
pository/org/apache/tomcat/embed/tomcat-embed-el/8.5.28/tomcat-embed-el-8.5.28.jar, /Users/<USER>/.m2/repos
itory/org/apache/tomcat/embed/tomcat-embed-websocket/8.5.28/tomcat-embed-websocket-8.5.28.jar, /Users/<USER>
i/.m2/repository/org/hibernate/validator/hibernate-validator/6.0.7.Final/hibernate-validator-6.0.7.Final.jar
, /Users/<USER>/.m2/repository/javax/validation/validation-api/2.0.1.Final/validation-api-2.0.1.Final.jar, 
/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.3.2.Final/jboss-logging-3.3.2.Final.jar, /Us
ers/macmini/.m2/repository/org/springframework/spring-web/5.0.4.RELEASE/spring-web-5.0.4.RELEASE.jar, /Users
/macmini/.m2/repository/org/springframework/spring-beans/5.0.4.RELEASE/spring-beans-5.0.4.RELEASE.jar, /User
s/macmini/.m2/repository/org/springframework/spring-webmvc/5.0.4.RELEASE/spring-webmvc-5.0.4.RELEASE.jar, /U
sers/macmini/.m2/repository/org/springframework/spring-aop/5.0.4.RELEASE/spring-aop-5.0.4.RELEASE.jar, /User
s/macmini/.m2/repository/org/springframework/spring-context/5.0.4.RELEASE/spring-context-5.0.4.RELEASE.jar, 
/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.0.4.RELEASE/spring-expression-5.0.4.RE
LEASE.jar, /Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.7.10/byte-buddy-1.7.10.jar, /Users/<USER>
ni/.m2/repository/org/springframework/spring-core/5.0.4.RELEASE/spring-core-5.0.4.RELEASE.jar, /Users/<USER>
i/.m2/repository/org/springframework/spring-jcl/5.0.4.RELEASE/spring-jcl-5.0.4.RELEASE.jar, /Users/<USER>/.
m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar, /Users/<USER>/.m2/repository/mysql/mysql-con
nector-java/8.0.28/mysql-connector-java-8.0.28.jar, /Users/<USER>/.m2/repository/com/google/protobuf/protob
uf-java/3.11.4/protobuf-java-3.11.4.jar, /Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/
4.5.2/httpclient-4.5.2.jar, /Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.9/httpcore-
4.4.9.jar, /Users/<USER>/.m2/repository/commons-codec/commons-codec/1.11/commons-codec-1.11.jar, /Users/<USER>
mini/.m2/repository/net/sourceforge/jexcelapi/jxl/2.6.12/jxl-2.6.12.jar, /Users/<USER>/.m2/repository/log4j
/log4j/1.2.14/log4j-1.2.14.jar, /Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.30/lombok-1.18.3
0.jar, /Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.15.0/log4j-to-slf4j-2.15.0.ja
r, /Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.25/slf4j-api-1.7.25.jar, /Users/<USER>/.m2/reposit
ory/org/apache/logging/log4j/log4j-api/2.10.0/log4j-api-2.10.0.jar, /Users/<USER>/.m2/repository/org/slf4j/
jul-to-slf4j/1.7.25/jul-to-slf4j-1.7.25.jar, /Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-st
arter/3.0.7.1/mybatis-plus-boot-starter-3.0.7.1.jar, /Users/<USER>/.m2/repository/com/baomidou/mybatis-plus
/3.0.7.1/mybatis-plus-3.0.7.1.jar, /Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.0.7.1
/mybatis-plus-extension-3.0.7.1.jar, /Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.0.7.1/my
batis-plus-core-3.0.7.1.jar, /Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.0.7.1/myba
tis-plus-annotation-3.0.7.1.jar, /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconf
igure/2.0.0.RELEASE/spring-boot-autoconfigure-2.0.0.RELEASE.jar, /Users/<USER>/.m2/repository/org/springfra
mework/boot/spring-boot-starter-jdbc/2.0.0.RELEASE/spring-boot-starter-jdbc-2.0.0.RELEASE.jar, /Users/<USER>
i/.m2/repository/com/zaxxer/HikariCP/2.7.8/HikariCP-2.7.8.jar, /Users/<USER>/.m2/repository/org/springframe
work/spring-jdbc/5.0.4.RELEASE/spring-jdbc-5.0.4.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframew
ork/boot/spring-boot-starter-redis/1.4.1.RELEASE/spring-boot-starter-redis-1.4.1.RELEASE.jar, /Users/<USER>
/.m2/repository/org/springframework/data/spring-data-redis/2.0.5.RELEASE/spring-data-redis-2.0.5.RELEASE.jar
, /Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.0.5.RELEASE/spring-data-keyv
alue-2.0.5.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.0.5.REL
EASE/spring-data-commons-2.0.5.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/spring-tx/5.0.
4.RELEASE/spring-tx-5.0.4.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/spring-oxm/5.0.4.RE
LEASE/spring-oxm-5.0.4.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/spring-context-support
/5.0.4.RELEASE/spring-context-support-5.0.4.RELEASE.jar, /Users/<USER>/.m2/repository/redis/clients/jedis/2
.9.0/jedis-2.9.0.jar, /Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.5.0/commons-pool2-2.5
.0.jar, /Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/2.7.0/springfox-swagger2-2.7.0.jar, /U
sers/macmini/.m2/repository/io/swagger/swagger-annotations/1.5.13/swagger-annotations-1.5.13.jar, /Users/<USER>
mini/.m2/repository/io/swagger/swagger-models/1.5.13/swagger-models-1.5.13.jar, /Users/<USER>/.m2/repositor
y/io/springfox/springfox-spi/2.7.0/springfox-spi-2.7.0.jar, /Users/<USER>/.m2/repository/io/springfox/sprin
gfox-core/2.7.0/springfox-core-2.7.0.jar, /Users/<USER>/.m2/repository/io/springfox/springfox-schema/2.7.0/
springfox-schema-2.7.0.jar, /Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/2.7.0/spring
fox-swagger-common-2.7.0.jar, /Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/2.7.0/springfo
x-spring-web-2.7.0.jar, /Users/<USER>/.m2/repository/org/reflections/reflections/0.9.11/reflections-0.9.11.
jar, /Users/<USER>/.m2/repository/org/javassist/javassist/3.21.0-GA/javassist-3.21.0-GA.jar, /Users/<USER>
/.m2/repository/com/google/guava/guava/18.0/guava-18.0.jar, /Users/<USER>/.m2/repository/com/fasterxml/clas
smate/1.3.4/classmate-1.3.4.jar, /Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core
/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar, /Users/<USER>/.m2/repository/org/springframework/plugi
n/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar, /Users/<USER>/.m2/reposito
ry/org/mapstruct/mapstruct/1.1.0.Final/mapstruct-1.1.0.Final.jar, /Users/<USER>/.m2/repository/com/github/x
iaoymin/swagger-bootstrap-ui/1.6/swagger-bootstrap-ui-1.6.jar, /Users/<USER>/.m2/repository/com/aliyun/oss/
aliyun-sdk-oss/3.10.1/aliyun-sdk-oss-3.10.1.jar, /Users/<USER>/.m2/repository/org/jdom/jdom/1.1/jdom-1.1.ja
r, /Users/<USER>/.m2/repository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar, /Users/<USER>/.m2/rep
ository/stax/stax-api/1.0.1/stax-api-1.0.1.jar, /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-cor
e/3.4.0/aliyun-java-sdk-core-3.4.0.jar, /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ram/3.0.0/a
liyun-java-sdk-ram-3.0.0.jar, /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java
-sdk-sts-3.0.0.jar, /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4
.2.0.jar, /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-kms/2.7.0/aliyun-java-sdk-kms-2.7.0.jar, 
/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.2/gson-2.8.2.jar, /Users/<USER>/.m2/repository/
com/itextpdf/itextpdf/5.5.13.1/itextpdf-5.5.13.1.jar, /Users/<USER>/.m2/repository/commons-io/commons-io/1.
3.2/commons-io-1.3.2.jar, /Users/<USER>/.m2/repository/com/belerweb/pinyin4j/2.5.1/pinyin4j-2.5.1.jar, /Use
rs/macmini/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-starter/1.2.13/pagehelper-spring-boot
-starter-1.2.13.jar, /Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.1.1
/mybatis-spring-boot-starter-2.1.1.jar, /Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring
-boot-autoconfigure/2.1.1/mybatis-spring-boot-autoconfigure-2.1.1.jar, /Users/<USER>/.m2/repository/org/myb
atis/mybatis/3.5.3/mybatis-3.5.3.jar, /Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.0.3/mybatis
-spring-2.0.3.jar, /Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-autoconfigure/
1.2.13/pagehelper-spring-boot-autoconfigure-1.2.13.jar, /Users/<USER>/.m2/repository/com/github/pagehelper/
pagehelper/5.1.11/pagehelper-5.1.11.jar, /Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/2.0/
jsqlparser-2.0.jar]
[DEBUG]   (f) compileSourceRoots = [/Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java]
[DEBUG]   (f) compilerId = javac
[DEBUG]   (f) debug = true
[DEBUG]   (f) encoding = UTF-8
[DEBUG]   (f) failOnError = true
[DEBUG]   (f) failOnWarning = false
[DEBUG]   (f) forceJavacCompilerUse = false
[DEBUG]   (f) fork = false
[DEBUG]   (f) generatedSourcesDirectory = /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/gener
ated-sources/annotations
[DEBUG]   (f) mojoExecution = org.apache.maven.plugins:maven-compiler-plugin:3.7.0:compile {execution: defau
lt-compile}
[DEBUG]   (f) optimize = false
[DEBUG]   (f) outputDirectory = /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes
[DEBUG]   (f) parameters = true
[DEBUG]   (f) project = MavenProject: com.jsh:jshERP-boot:3.5-SNAPSHOT @ /Users/<USER>/Desktop/jshERP-0612-
Cursor/jshERP-boot/pom.xml
[DEBUG]   (f) projectArtifact = com.jsh:jshERP-boot:jar:3.5-SNAPSHOT
[DEBUG]   (f) session = org.apache.maven.execution.MavenSession@62765aec
[DEBUG]   (f) showDeprecation = false
[DEBUG]   (f) showWarnings = false
[DEBUG]   (f) skipMultiThreadWarning = false
[DEBUG]   (f) source = 1.8
[DEBUG]   (f) staleMillis = 0
[DEBUG]   (f) target = 1.8
[DEBUG]   (f) useIncrementalCompilation = true
[DEBUG]   (f) verbose = false
[DEBUG] -- end configuration --
[DEBUG] Using compiler 'javac'.
[DEBUG] Adding /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/generated-sources/annotations to
 compile source roots:
  /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java
[DEBUG] New compile source roots:
  /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java
  /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/generated-sources/annotations
[DEBUG] CompilerReuseStrategy: reuseCreated
[DEBUG] useIncrementalCompilation enabled
[INFO] Changes detected - recompiling the module!
[DEBUG] Classpath:
[DEBUG]  /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes
[DEBUG]  /Users/<USER>/.m2/repository/com/gitee/starblues/springboot-plugin-framework/2.2.1-RELEASE/springb
oot-plugin-framework-2.2.1-RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/pf4j/pf4j/3.1.0/pf4j-3.1.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/github/zafarkhaja/java-semver/0.9.0/java-semver-0.9.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.9.4/jackson-databind-2.
9.4.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.9.0/jackson-annotati
ons-2.9.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.9.4/jackson-core-2.9.4.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.9.4/jackso
n-dataformat-yaml-2.9.4.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.19/snakeyaml-1.19.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/gitee/starblues/springboot-plugin-framework-extension-mybatis/2.2
.1-RELEASE/springboot-plugin-framework-extension-mybatis-2.2.1-RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.0.0.RELEASE/spring
-boot-starter-web-2.0.0.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.0.0.RELEASE/spring-boo
t-starter-2.0.0.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.0.0.RELEASE/spring-boot-2.0.0.
RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.0.0.RELEASE/sp
ring-boot-starter-logging-2.0.0.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar
[DEBUG]  /Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar
[DEBUG]  /Users/<USER>/.m2/repository/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.
2.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.0.0.RELEASE/sprin
g-boot-starter-json-2.0.0.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.9.4/jackson-da
tatype-jdk8-2.9.4.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.9.4/jackson-
datatype-jsr310-2.9.4.jar
--
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-oxm/5.0.4.RELEASE/spring-oxm-5.0.4.RELEASE
.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.0.4.RELEASE/spring-conte
xt-support-5.0.4.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/redis/clients/jedis/2.9.0/jedis-2.9.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.5.0/commons-pool2-2.5.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/2.7.0/springfox-swagger2-2.7.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.5.13/swagger-annotations-1.5.13.jar
[DEBUG]  /Users/<USER>/.m2/repository/io/swagger/swagger-models/1.5.13/swagger-models-1.5.13.jar
[DEBUG]  /Users/<USER>/.m2/repository/io/springfox/springfox-spi/2.7.0/springfox-spi-2.7.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/io/springfox/springfox-core/2.7.0/springfox-core-2.7.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/io/springfox/springfox-schema/2.7.0/springfox-schema-2.7.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/2.7.0/springfox-swagger-common-
2.7.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/2.7.0/springfox-spring-web-2.7.0.ja
r
[DEBUG]  /Users/<USER>/.m2/repository/org/reflections/reflections/0.9.11/reflections-0.9.11.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/javassist/javassist/3.21.0-GA/javassist-3.21.0-GA.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/google/guava/guava/18.0/guava-18.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/fasterxml/classmate/1.3.4/classmate-1.3.4.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-pl
ugin-core-1.2.0.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/sprin
g-plugin-metadata-1.2.0.RELEASE.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.1.0.Final/mapstruct-1.1.0.Final.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-bootstrap-ui/1.6/swagger-bootstrap-ui-1.6
.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/aliyun/oss/aliyun-sdk-oss/3.10.1/aliyun-sdk-oss-3.10.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/jdom/jdom/1.1/jdom-1.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-core/3.4.0/aliyun-java-sdk-core-3.4.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ram/3.0.0/aliyun-java-sdk-ram-3.0.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java-sdk-sts-3.0.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4.2.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-kms/2.7.0/aliyun-java-sdk-kms-2.7.0.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.2/gson-2.8.2.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/itextpdf/itextpdf/5.5.13.1/itextpdf-5.5.13.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/commons-io/commons-io/1.3.2/commons-io-1.3.2.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/belerweb/pinyin4j/2.5.1/pinyin4j-2.5.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-starter/1.2.13/pagehelpe
r-spring-boot-starter-1.2.13.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.1.1/mybatis-spr
ing-boot-starter-2.1.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/2.1.1/mybat
is-spring-boot-autoconfigure-2.1.1.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.3/mybatis-3.5.3.jar
[DEBUG]  /Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.0.3/mybatis-spring-2.0.3.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-autoconfigure/1.2.13/pag
ehelper-spring-boot-autoconfigure-1.2.13.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper/5.1.11/pagehelper-5.1.11.jar
[DEBUG]  /Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/2.0/jsqlparser-2.0.jar
[DEBUG] Source roots:
[DEBUG]  /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java
[DEBUG]  /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/generated-sources/annotations
[DEBUG] Command line options:
[DEBUG] -d /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes -classpath /Users/<USER>/De
sktop/jshERP-0612-Cursor/jshERP-boot/target/classes:/Users/<USER>/.m2/repository/com/gitee/starblues/spring
boot-plugin-framework/2.2.1-RELEASE/springboot-plugin-framework-2.2.1-RELEASE.jar:/Users/<USER>/.m2/reposit
ory/org/pf4j/pf4j/3.1.0/pf4j-3.1.0.jar:/Users/<USER>/.m2/repository/com/github/zafarkhaja/java-semver/0.9.0
/java-semver-0.9.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.9.4/jacks
on-databind-2.9.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.9.0/jac
kson-annotations-2.9.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.9.4/jacks
on-core-2.9.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.9
.4/jackson-dataformat-yaml-2.9.4.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.19/snakeyaml-1.19.ja
r:/Users/<USER>/.m2/repository/com/gitee/starblues/springboot-plugin-framework-extension-mybatis/2.2.1-RELE
ASE/springboot-plugin-framework-extension-mybatis-2.2.1-RELEASE.jar:/Users/<USER>/.m2/repository/org/spring
framework/boot/spring-boot-starter-web/2.0.0.RELEASE/spring-boot-starter-web-2.0.0.RELEASE.jar:/Users/<USER>
i/.m2/repository/org/springframework/boot/spring-boot-starter/2.0.0.RELEASE/spring-boot-starter-2.0.0.RELEAS
E.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.0.0.RELEASE/spring-boot-2.0.0.REL
EASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.0.0.RELEASE/sp
ring-boot-starter-logging-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2
.3/logback-classic-1.2.3.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.
2.3.jar:/Users/<USER>/.m2/repository/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2
.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.0.0.RELEASE/spring-bo
ot-starter-json-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datat
ype-jdk8/2.9.4/jackson-datatype-jdk8-2.9.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/
jackson-datatype-jsr310/2.9.4/jackson-datatype-jsr310-2.9.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/
jackson/module/jackson-module-parameter-names/2.9.4/jackson-module-parameter-names-2.9.4.jar:/Users/<USER>/
.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.0.0.RELEASE/spring-boot-starter-tomcat-
2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/8.5.28/tomcat-embe
d-core-8.5.28.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/8.5.28/tomcat-embed-
el-8.5.28.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/8.5.28/tomcat-emb
ed-websocket-8.5.28.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.0.7.Fina
l/hibernate-validator-6.0.7.Final.jar:/Users/<USER>/.m2/repository/javax/validation/validation-api/2.0.1.Fi
nal/validation-api-2.0.1.Final.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.3.2.Final
/jboss-logging-3.3.2.Final.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.0.4.RELEASE/sp
ring-web-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.0.4.RELEASE/spri
ng-beans-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.0.4.RELEASE/spr
ing-webmvc-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.0.4.RELEASE/spri
ng-aop-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.0.4.RELEASE/spri
ng-context-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.0.4.RELEA
SE/spring-expression-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.7.10/byte-bu
ddy-1.7.10.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.0.4.RELEASE/spring-core-5.0.4
.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.0.4.RELEASE/spring-jcl-5.0.4.REL
EASE.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar:/Users/<USER>/.m2/re
pository/mysql/mysql-connector-java/8.0.28/mysql-connector-java-8.0.28.jar:/Users/<USER>/.m2/repository/com
/google/protobuf/protobuf-java/3.11.4/protobuf-java-3.11.4.jar:/Users/<USER>/.m2/repository/org/apache/http
components/httpclient/4.5.2/httpclient-4.5.2.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/htt
pcore/4.4.9/httpcore-4.4.9.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.11/commons-codec-
1.11.jar:/Users/<USER>/.m2/repository/net/sourceforge/jexcelapi/jxl/2.6.12/jxl-2.6.12.jar:/Users/<USER>/.m
2/repository/log4j/log4j/1.2.14/log4j-1.2.14.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18
.30/lombok-1.18.30.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.15.0/log4j-to
-slf4j-2.15.0.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.25/slf4j-api-1.7.25.jar:/Users/<USER>
ni/.m2/repository/org/apache/logging/log4j/log4j-api/2.10.0/log4j-api-2.10.0.jar:/Users/<USER>/.m2/reposito
ry/org/slf4j/jul-to-slf4j/1.7.25/jul-to-slf4j-1.7.25.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-
plus-boot-starter/3.0.7.1/mybatis-plus-boot-starter-3.0.7.1.jar:/Users/<USER>/.m2/repository/com/baomidou/m
ybatis-plus/3.0.7.1/mybatis-plus-3.0.7.1.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extensi
on/3.0.7.1/mybatis-plus-extension-3.0.7.1.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3
.0.7.1/mybatis-plus-core-3.0.7.1.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.0.
7.1/mybatis-plus-annotation-3.0.7.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-a
utoconfigure/2.0.0.RELEASE/spring-boot-autoconfigure-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/spr
ingframework/boot/spring-boot-starter-jdbc/2.0.0.RELEASE/spring-boot-starter-jdbc-2.0.0.RELEASE.jar:/Users/<USER>
acmini/.m2/repository/com/zaxxer/HikariCP/2.7.8/HikariCP-2.7.8.jar:/Users/<USER>/.m2/repository/org/springf
ramework/spring-jdbc/5.0.4.RELEASE/spring-jdbc-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springfra
mework/boot/spring-boot-starter-redis/1.4.1.RELEASE/spring-boot-starter-redis-1.4.1.RELEASE.jar:/Users/<USER>
ni/.m2/repository/org/springframework/data/spring-data-redis/2.0.5.RELEASE/spring-data-redis-2.0.5.RELEASE.j
ar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.0.5.RELEASE/spring-data-key
value-2.0.5.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.0.5.REL
EASE/spring-data-commons-2.0.5.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.0.4
.RELEASE/spring-tx-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/5.0.4.RELE
ASE/spring-oxm-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.
0.4.RELEASE/spring-context-support-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/redis/clients/jedis/2.9.0
/jedis-2.9.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.5.0/commons-pool2-2.5.0.ja
r:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/2.7.0/springfox-swagger2-2.7.0.jar:/Users/<USER>
cmini/.m2/repository/io/swagger/swagger-annotations/1.5.13/swagger-annotations-1.5.13.jar:/Users/<USER>/.m2
/repository/io/swagger/swagger-models/1.5.13/swagger-models-1.5.13.jar:/Users/<USER>/.m2/repository/io/spri
ngfox/springfox-spi/2.7.0/springfox-spi-2.7.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-core/
2.7.0/springfox-core-2.7.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-schema/2.7.0/springfox-s
chema-2.7.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/2.7.0/springfox-swagger-
common-2.7.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/2.7.0/springfox-spring-web-
2.7.0.jar:/Users/<USER>/.m2/repository/org/reflections/reflections/0.9.11/reflections-0.9.11.jar:/Users/<USER>
mini/.m2/repository/org/javassist/javassist/3.21.0-GA/javassist-3.21.0-GA.jar:/Users/<USER>/.m2/repository/
com/google/guava/guava/18.0/guava-18.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.3.4/class
mate-1.3.4.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spr
ing-plugin-core-1.2.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-met
adata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/mapstruct/map
struct/1.1.0.Final/mapstruct-1.1.0.Final.jar:/Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-boots
trap-ui/1.6/swagger-bootstrap-ui-1.6.jar:/Users/<USER>/.m2/repository/com/aliyun/oss/aliyun-sdk-oss/3.10.1/
aliyun-sdk-oss-3.10.1.jar:/Users/<USER>/.m2/repository/org/jdom/jdom/1.1/jdom-1.1.jar:/Users/<USER>/.m2/re
pository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar:/Users/<USER>/.m2/repository/stax/stax-api/1.0
.1/stax-api-1.0.1.jar:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-core/3.4.0/aliyun-java-sdk-co
re-3.4.0.jar:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ram/3.0.0/aliyun-java-sdk-ram-3.0.0.ja
r:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java-sdk-sts-3.0.0.jar:/Users/<USER>
cmini/.m2/repository/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4.2.0.jar:/Users/<USER>/.m2/r
epository/com/aliyun/aliyun-java-sdk-kms/2.7.0/aliyun-java-sdk-kms-2.7.0.jar:/Users/<USER>/.m2/repository/c
om/google/code/gson/gson/2.8.2/gson-2.8.2.jar:/Users/<USER>/.m2/repository/com/itextpdf/itextpdf/5.5.13.1/i
textpdf-5.5.13.1.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/1.3.2/commons-io-1.3.2.jar:/Users/<USER>
acmini/.m2/repository/com/belerweb/pinyin4j/2.5.1/pinyin4j-2.5.1.jar:/Users/<USER>/.m2/repository/com/githu
b/pagehelper/pagehelper-spring-boot-starter/1.2.13/pagehelper-spring-boot-starter-1.2.13.jar:/Users/<USER>/
.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.1.1/mybatis-spring-boot-starter-2.1.1.j
ar:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/2.1.1/mybatis-spr
ing-boot-autoconfigure-2.1.1.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.3/mybatis-3.5.3.jar:/
Users/macmini/.m2/repository/org/mybatis/mybatis-spring/2.0.3/mybatis-spring-2.0.3.jar:/Users/<USER>/.m2/re
pository/com/github/pagehelper/pagehelper-spring-boot-autoconfigure/1.2.13/pagehelper-spring-boot-autoconfig
ure-1.2.13.jar:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper/5.1.11/pagehelper-5.1.11.jar:/
Users/macmini/.m2/repository/com/github/jsqlparser/jsqlparser/2.0/jsqlparser-2.0.jar: -sourcepath /Users/<USER>
mini/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java:/Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-b
oot/target/generated-sources/annotations: -s /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/ge
nerated-sources/annotations -processorpath /Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.30/lo
mbok-1.18.30.jar: -g -parameters -nowarn -target 1.8 -source 1.8 -encoding UTF-8
[DEBUG] incrementalBuildHelper#beforeRebuildExecution
[INFO] Compiling 277 source files to /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/target/classes
[DEBUG] incrementalBuildHelper#afterRebuildExecution
[INFO] -------------------------------------------------------------
[ERROR] COMPILATION ERROR : 
[INFO] -------------------------------------------------------------
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/controller/WorkOrder
Controller.java:[6,29] 找不到符号
  符号:   类 ErpInfo
  位置: 程序包 com.jsh.erp.constants
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/controller/Productio
nController.java:[6,29] 找不到符号
  符号:   类 ErpInfo
  位置: 程序包 com.jsh.erp.constants
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[4,39] 找不到符号
  符号:   类 WorkOrderItemExample
  位置: 程序包 com.jsh.erp.datasource.entities
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[18,25] 找不到符号
  符号:   类 WorkOrderItemExample
  位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[23,25] 找不到符号
  符号:   类 WorkOrderItemExample
  位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[43,41] 找不到符号
  符号:   类 WorkOrderItemExample
  位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[53,91] 找不到符号
  符号:   类 WorkOrderItemExample
  位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[58,82] 找不到符号
  符号:   类 WorkOrderItemExample
  位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[INFO] 8 errors 
[INFO] -------------------------------------------------------------
[INFO] ------------------------------------------------------------------------
[INFO] BUILD FAILURE
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  5.681 s
[INFO] Finished at: 2025-06-21T17:42:24+08:00
[INFO] ------------------------------------------------------------------------
[ERROR] Failed to execute goal org.apache.maven.plugins:maven-compiler-plugin:3.7.0:compile (default-compile
) on project jshERP-boot: Compilation failure: Compilation failure: 
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/controller/WorkOrder
Controller.java:[6,29] 找不到符号
[ERROR]   符号:   类 ErpInfo
[ERROR]   位置: 程序包 com.jsh.erp.constants
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/controller/Productio
nController.java:[6,29] 找不到符号
[ERROR]   符号:   类 ErpInfo
[ERROR]   位置: 程序包 com.jsh.erp.constants
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[4,39] 找不到符号
[ERROR]   符号:   类 WorkOrderItemExample
[ERROR]   位置: 程序包 com.jsh.erp.datasource.entities
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[18,25] 找不到符号
[ERROR]   符号:   类 WorkOrderItemExample
[ERROR]   位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[23,25] 找不到符号
[ERROR]   符号:   类 WorkOrderItemExample
[ERROR]   位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[43,41] 找不到符号
[ERROR]   符号:   类 WorkOrderItemExample
[ERROR]   位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[53,91] 找不到符号
[ERROR]   符号:   类 WorkOrderItemExample
[ERROR]   位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] /Users/<USER>/Desktop/jshERP-0612-Cursor/jshERP-boot/src/main/java/com/jsh/erp/datasource/mappers/W
orkOrderItemMapper.java:[58,82] 找不到符号
[ERROR]   符号:   类 WorkOrderItemExample
[ERROR]   位置: 接口 com.jsh.erp.datasource.mappers.WorkOrderItemMapper
[ERROR] -> [Help 1]
org.apache.maven.lifecycle.LifecycleExecutionException: Failed to execute goal org.apache.maven.plugins:mave
n-compiler-plugin:3.7.0:compile (default-compile) on project jshERP-boot: Compilation failure
    at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:333)
    at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
    at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
    at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
    at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
    at org.apache.maven.lifecycle.internal.MojoExecutor.executeForkedExecutions (MojoExecutor.java:448)
    at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:311)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
    at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
    at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
    at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
    at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:
105)
    at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:
73)
    at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreade
dBuilder.java:53)
    at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
    at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
    at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
    at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
    at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
    at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
    at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
    at jdk.internal.reflect.DirectMethodHandleAccessor.invoke (DirectMethodHandleAccessor.java:103)
    at java.lang.reflect.Method.invoke (Method.java:580)
    at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
    at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
    at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
    at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
Caused by: org.apache.maven.plugin.compiler.CompilationFailureException: Compilation failure
    at org.apache.maven.plugin.compiler.AbstractCompilerMojo.execute (AbstractCompilerMojo.java:1161)
    at org.apache.maven.plugin.compiler.CompilerMojo.execute (CompilerMojo.java:168)
    at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
    at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
    at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
    at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
    at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
    at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
    at org.apache.maven.lifecycle.internal.MojoExecutor.executeForkedExecutions (MojoExecutor.java:448)
    at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:311)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
    at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
    at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
    at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
    at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:
105)
    at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:
73)
    at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreade
dBuilder.java:53)
    at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
    at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
    at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
    at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
    at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
    at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
    at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
    at jdk.internal.reflect.DirectMethodHandleAccessor.invoke (DirectMethodHandleAccessor.java:103)
    at java.lang.reflect.Method.invoke (Method.java:580)
    at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
    at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
    at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
    at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
[ERROR] 
[ERROR] 
[ERROR] For more information about the errors and possible solutions, please read the following articles:
[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/MojoFailureException
[DEBUG] Shutting down adapter factory; available factories [file-lock, rwlock-local, semaphore-local, noop];
 available name mappers [discriminating, file-gav, file-hgav, file-static, gav, static]
[DEBUG] Shutting down 'file-lock' factory
[DEBUG] Shutting down 'rwlock-local' factory
[DEBUG] Shutting down 'semaphore-local' factory
[DEBUG] Shutting down 'noop' factory
macmini@liangchaoweideMac-mini jshERP-0612-Cursor %
```

---



---

由于无法直接查看终端输出，我将切换策略，使用更可靠的文件读取工具来分析已生成的日志文件 `maven_debug_log.txt`。根据您的开发规范，我将优先使用 `Desktop Commander` 系列工具。

**下一步：读取并分析日志文件**

我将使用 `mcp_Desktop_Commander_read_file` 工具读取 `maven_debug_log.txt` 文件的最后2000行。错误信息和堆栈跟踪通常位于日志的末尾，这种方法可以让我高效地获取最关键的诊断信息。

---



---



---

好的，日志文件已成功读取。

**诊断分析**：
日志清晰地指出了问题所在：**编译失败 (Compilation Failure)**，而不是应用运行时的错误。这意味着在启动Spring Boot应用之前，Java源代码编译阶段就失败了。

**核心错误**：
编译器找不到几个关键的类（找不到符号），具体如下：
1.  **`com.jsh.erp.constants.ErpInfo`**: 在 `WorkOrderController.java` 和 `ProductionController.java` 中被引用。
2.  **`com.jsh.erp.datasource.entities.WorkOrderItemExample`**: 在MyBatis的Mapper接口 `WorkOrderItemMapper.java` 中被多次引用。

**根本原因推断**：
这些错误强烈表明，与“工单(WorkOrder)”和“生产(Production)”功能相关的代码被不完整地移除了。很可能相关的实体类（如 `ErpInfo`, `WorkOrderItemExample`）和可能它们的Mapper XML文件已经被删除，但是引用这些文件的Controller和Mapper接口却依然存在，导致编译时无法找到对应的符号。

**下一步行动计划**：
我将分两步来解决这个问题：
1.  **定位并移除残留的Java文件**：我会查找并建议删除所有与“WorkOrder”和“Production”功能相关的、现在已无用的Controller、Service和Mapper接口文件。
2.  **定位并移除残留的MyBatis配置文件**：我会检查 `mapper_xml` 目录，确保与 `WorkOrderItemMapper` 相关的XML文件也已经被清理。

我将首先使用 `mcp_Desktop_Commander_search_files` 工具，在 `jshERP-boot` 模块中搜索所有文件名包含 `WorkOrder` 或 `Production` 的Java文件和XML文件，以全面评估清理范围。

---



---

我将并行搜索`Production`相关文件，以全面了解情况。

---



---

好的，文件搜索已经完成。

**诊断结论**：
搜索结果证实了我的推断。系统中存在大量与`WorkOrder`（工单）和`Production`（生产）功能相关的文件，包括Controllers, Services, Mappers, 和 Entities。

然而，编译错误 `找不到符号: 类 WorkOrderItemExample` 和 `找不到符号: 类 ErpInfo` 表明，尽管这些引用了问题类的文件还存在，但 `WorkOrderItemExample.java` 和 `ErpInfo.java` 文件本身似乎已经被删除了。这种不一致的状态是导致编译失败的直接原因。

**修复方案**：
最直接且正确的做法是彻底移除整个“工单”和“生产”模块的所有残留组件，以确保系统的一致性。

**建议删除以下文件**：

**WorkOrder 相关文件:**
*   `controller/WorkOrderController.java`
*   `datasource/entities/WorkOrder.java`
*   `datasource/entities/WorkOrderExample.java`
*   `datasource/entities/WorkOrderItem.java`
*   `datasource/entities/WorkOrderItemExample.java` (此文件可能已不存在，但若存在也应删除)
*   `datasource/mappers/WorkOrderItemMapper.java`
*   `datasource/mappers/WorkOrderItemMapperEx.java`
*   `datasource/mappers/WorkOrderMapper.java`
*   `datasource/mappers/WorkOrderMapperEx.java`
*   `service/WorkOrderService.java`
*   `service/WorkOrderServiceImpl.java`

**Production 相关文件:**
*   `controller/ProductionController.java`
*   `datasource/entities/ProductionOrder.java`
*   `datasource/entities/ProductionOrderExample.java`
*   `datasource/mappers/ProductionOrderMapper.java`
*   `datasource/mappers/ProductionOrderMapperEx.java`
*   `service/ProductionService.java`
*   `service/ProductionServiceImpl.java`

**寻求用户确认**
我将通过 `interactive_feedback` 工具向您呈现此诊断摘要和文件删除列表。**请在确认后授权我执行删除操作**。这将系统性地解决编译错误。

---



---

