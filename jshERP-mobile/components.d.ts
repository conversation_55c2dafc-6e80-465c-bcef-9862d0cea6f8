/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    BaseButton: typeof import('./src/components/base/BaseButton.vue')['default']
    ERPDataCard: typeof import('./src/components/erp/ERPDataCard.vue')['default']
    ERPFunctionGrid: typeof import('./src/components/erp/ERPFunctionGrid.vue')['default']
    ERPMenuList: typeof import('./src/components/erp/ERPMenuList.vue')['default']
    ERPSectionTitle: typeof import('./src/components/erp/ERPSectionTitle.vue')['default']
    ERPStatPanel: typeof import('./src/components/erp/ERPStatPanel.vue')['default']
    ERPTabBar: typeof import('./src/components/erp/ERPTabBar.vue')['default']
    ERPUserCard: typeof import('./src/components/erp/ERPUserCard.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    VanBadge: typeof import('vant/es')['Badge']
    VanButton: typeof import('vant/es')['Button']
    VanCell: typeof import('vant/es')['Cell']
    VanCellGroup: typeof import('vant/es')['CellGroup']
    VanCheckbox: typeof import('vant/es')['Checkbox']
    VanField: typeof import('vant/es')['Field']
    VanFloatingBubble: typeof import('vant/es')['FloatingBubble']
    VanForm: typeof import('vant/es')['Form']
    VanGrid: typeof import('vant/es')['Grid']
    VanGridItem: typeof import('vant/es')['GridItem']
    VanIcon: typeof import('vant/es')['Icon']
    VanImage: typeof import('vant/es')['Image']
    VanList: typeof import('vant/es')['List']
    VanNavBar: typeof import('vant/es')['NavBar']
    VanPullRefresh: typeof import('vant/es')['PullRefresh']
    VanSearch: typeof import('vant/es')['Search']
    VanTabbar: typeof import('vant/es')['Tabbar']
    VanTabbarItem: typeof import('vant/es')['TabbarItem']
  }
}
