{"name": "jsherp-mobile", "version": "1.0.0", "private": true, "type": "module", "scripts": {"build": "run-p type-check \"build-only {@}\" --", "build-only": "vite build", "dev": "vite", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "preview": "vite preview", "type-check": "vue-tsc --build --force", "prepare": "husky"}, "dependencies": {"axios": "^1.10.0", "dayjs": "^1.11.13", "pinia": "^2.1.0", "vant": "^4.8.0", "vue": "^3.4.0", "vue-router": "^4.2.0"}, "devDependencies": {"@rushstack/eslint-patch": "^1.3.3", "@tsconfig/node20": "^20.1.2", "@types/node": "^20.8.10", "@vitejs/plugin-vue": "^4.4.0", "@vitejs/plugin-vue-jsx": "^3.0.2", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.4.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "husky": "^8.0.3", "lint-staged": "^15.2.0", "npm-run-all2": "^6.1.1", "prettier": "^3.0.3", "terser": "^5.43.1", "typescript": "~5.3.0", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.0", "vue-tsc": "^1.8.19"}, "lint-staged": {"*.{js,ts,vue}": ["eslint --fix", "prettier --write"]}}