<!--
  个人中心页面

  包含用户信息卡片、功能菜单和底部提示
-->
<template>
  <div class="erp-page erp-page--no-padding">
    <!-- 用户信息卡片 -->
    <ERPUserCard :user-info="userInfo" :trial-info="trialInfo" />

    <!-- 功能菜单列表 -->
    <div class="profile-menu">
      <ERPMenuList :items="menuItems" @item-click="handleMenuClick" />
    </div>

    <!-- 底部提示 -->
    <div class="profile-footer">
      <p class="profile-footer__text">
        更多功能访问电脑端：https://cloud.gyjerp.com
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue'
import { useRouter } from 'vue-router'
import { showDialog, showToast } from 'vant'
import { useAuthStore } from '@/stores'
import ERPUserCard from '@/components/erp/ERPUserCard.vue'
import ERPMenuList from '@/components/erp/ERPMenuList.vue'
import type { UserInfo, TrialInfo, MenuItem } from '@/types/erp'

const router = useRouter()
const authStore = useAuthStore()

/**
 * 用户信息
 */
const userInfo = reactive<UserInfo>({
  username: 'waterxigg',
  userType: '员工',
  avatar: '' // 可以设置头像URL
})

/**
 * 试用信息
 */
const trialInfo = reactive<TrialInfo>({
  isTrialUser: true,
  endDate: '2025-06-30',
  currentUsers: 1,
  maxUsers: 2
})

/**
 * 功能菜单配置
 */
const menuItems: MenuItem[] = [
  { id: 'about', icon: 'info-o', label: '关于我们' },
  { id: 'password', icon: 'lock', label: '登录密码' },
  { id: 'cache', icon: 'delete-o', label: '缓存链接' },
  { id: 'profile', icon: 'contact', label: '个人信息' },
  { id: 'logout', icon: 'sign-out', label: '退出登录', danger: true }
]

/**
 * 处理菜单项点击
 */
const handleMenuClick = async (item: MenuItem): Promise<void> => {
  console.log('Menu item clicked:', item)

  switch (item.id) {
    case 'about':
      showToast('关于我们')
      // TODO: 跳转到关于我们页面
      break

    case 'password':
      showToast('修改密码')
      // TODO: 跳转到修改密码页面
      break

    case 'cache':
      showToast('清理缓存')
      // TODO: 执行清理缓存操作
      break

    case 'profile':
      showToast('个人信息')
      // TODO: 跳转到个人信息编辑页面
      break

    case 'logout':
      await handleLogout()
      break

    default:
      showToast(`点击了${item.label}`)
      break
  }
}

/**
 * 处理退出登录
 */
const handleLogout = async (): Promise<void> => {
  try {
    await showDialog({
      title: '退出登录',
      message: '确定要退出登录吗？',
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    })

    // 执行退出登录
    await authStore.logout()
    showToast({ type: 'success', message: '已退出登录' })

    // 跳转到登录页面
    router.push('/auth/login')
  } catch (error) {
    // 用户取消或退出失败
    console.log('Logout cancelled or failed:', error)
  }
}
</script>

<style scoped lang="less">
@import '@/styles/variables.less';
@import '@/styles/common.less';

.erp-page--no-padding {
  padding-bottom: 60px; // Tab栏高度

  .erp-header {
    margin: 0;
  }
}

.profile-menu {
  margin-top: var(--erp-spacing-lg);
}

.profile-footer {
  margin: var(--erp-spacing-xl) var(--erp-spacing-md) var(--erp-spacing-lg);
  text-align: center;

  &__text {
    font-size: var(--erp-font-size-xs);
    color: var(--erp-text-tertiary);
    line-height: var(--erp-line-height-normal);
    margin: 0;
  }
}

// 响应式适配
@media (max-width: @erp-mobile-xs) {
  .profile-footer {
    margin: var(--erp-spacing-lg) var(--erp-spacing-sm) var(--erp-spacing-md);

    &__text {
      font-size: 10px;
    }
  }
}
</style>
