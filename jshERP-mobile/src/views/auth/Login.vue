<template>
  <div class="login-page">
    <van-form @submit="handleLogin">
      <van-cell-group inset>
        <van-field
          v-model="form.username"
          name="username"
          label="用户名"
          placeholder="请输入用户名"
          :rules="[{ required: true, message: '请输入用户名' }]"
        />
        <van-field
          v-model="form.password"
          type="password"
          name="password"
          label="密码"
          placeholder="请输入密码"
          :rules="[{ required: true, message: '请输入密码' }]"
        />
      </van-cell-group>
      
      <div class="login-page__actions">
        <van-checkbox v-model="form.rememberMe">记住密码</van-checkbox>
      </div>
      
      <div class="login-page__submit">
        <van-button
          round
          block
          type="primary"
          native-type="submit"
          :loading="loading"
        >
          登录
        </van-button>
      </div>
    </van-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores'
import { Toast } from 'vant'

const router = useRouter()
const authStore = useAuthStore()
const loading = ref(false)

const form = reactive({
  username: '',
  password: '',
  rememberMe: false
})

const handleLogin = async () => {
  loading.value = true
  try {
    await authStore.login(form)
    Toast.success('登录成功')
    router.push('/dashboard')
  } catch (error) {
    Toast.fail('登录失败')
    console.error('Login error:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-page {
  padding: var(--spacing-lg);
}

.login-page__actions {
  padding: var(--spacing-md) 0;
  text-align: center;
}

.login-page__submit {
  margin-top: var(--spacing-lg);
}
</style>