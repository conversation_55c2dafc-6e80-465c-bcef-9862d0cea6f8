<!--
  销售订单列表页面
  
  功能：
  - 订单列表展示
  - 搜索和筛选
  - 分页加载
  - 订单状态管理
-->
<template>
  <div class="erp-page">
    <!-- 页面头部 -->
    <div class="erp-header">
      <van-nav-bar
        title="销售订单"
        left-arrow
        @click-left="$router.back()"
      >
        <template #right>
          <div class="nav-actions">
            <van-icon name="scan" @click="handleScan" />
          </div>
        </template>
      </van-nav-bar>
    </div>

    <!-- 搜索栏 -->
    <div class="search-section">
      <div class="search-container">
        <van-search
          v-model="searchForm.keyword"
          placeholder="条码/名称/助记码/规格/型号等"
          @search="handleSearch"
          @clear="handleClearSearch"
        />
        <div class="search-actions">
          <van-icon name="scan" @click="handleScanSearch" />
          <van-icon name="filter-o" @click="showAdvancedFilter" />
        </div>
      </div>
    </div>

    <!-- 筛选栏 -->
    <div class="filter-section">
      <van-dropdown-menu>
        <van-dropdown-item
          v-model="searchForm.status"
          :options="statusOptions"
          @change="handleFilterChange"
        />
        <van-dropdown-item
          v-model="searchForm.dateRange"
          :options="dateRangeOptions"
          @change="handleFilterChange"
        />
      </van-dropdown-menu>
    </div>

    <!-- 订单列表 -->
    <div class="order-list">
      <van-pull-refresh
        v-model="refreshing"
        @refresh="handleRefresh"
      >
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="handleLoadMore"
        >
          <div
            v-for="order in orderList"
            :key="order.id"
            class="order-item"
            @click="handleOrderClick(order)"
          >
            <van-card
              :title="order.orderNo"
              :desc="order.customerName"
              :thumb="order.customerAvatar"
            >
              <template #tags>
                <van-tag
                  :type="getStatusType(order.status)"
                >
                  {{ getStatusText(order.status) }}
                </van-tag>
              </template>
              
              <template #footer>
                <div class="order-footer">
                  <div class="order-info">
                    <div class="order-amount">
                      ¥{{ formatCurrency(order.totalAmount) }}
                    </div>
                    <div class="order-date">
                      {{ formatDate(order.orderDate) }}
                    </div>
                  </div>
                  <div class="order-actions">
                    <van-button
                      v-if="order.status === 'draft'"
                      size="small"
                      type="primary"
                      @click.stop="handleEditOrder(order)"
                    >
                      编辑
                    </van-button>
                    <van-button
                      v-if="order.status === 'pending'"
                      size="small"
                      type="success"
                      @click.stop="handleApproveOrder(order)"
                    >
                      审核
                    </van-button>
                    <van-button
                      size="small"
                      type="default"
                      @click.stop="handlePrintOrder(order)"
                    >
                      打印
                    </van-button>
                    <van-button
                      size="small"
                      type="default"
                      @click.stop="handleShareOrder(order)"
                    >
                      分享
                    </van-button>
                  </div>
                </div>
              </template>
            </van-card>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>

    <!-- 空状态 -->
    <van-empty
      v-if="!loading && orderList.length === 0"
      description="暂无订单数据"
    />

    <!-- 悬浮新增按钮 -->
    <van-floating-bubble
      axis="xy"
      icon="plus"
      @click="handleAddOrder"
    />

    <!-- 高级筛选弹窗 -->
    <van-popup
      v-model:show="showAdvancedFilterPopup"
      position="bottom"
      :style="{ height: '70%' }"
      round
      closeable
    >
      <div class="filter-popup">
        <div class="filter-header">
          <h3>商品信息</h3>
          <van-icon name="scan" @click="handleFilterScan" />
        </div>

        <div class="filter-content">
          <van-form>
            <van-field
              v-model="advancedFilter.startDate"
              label="开始日期"
              placeholder="2025-03-26"
              readonly
              @click="showStartDatePicker = true"
            />

            <van-field
              v-model="advancedFilter.endDate"
              label="结束日期"
              placeholder="2025-06-26"
              readonly
              @click="showEndDatePicker = true"
            />

            <van-field
              v-model="advancedFilter.customer"
              label="客户"
              placeholder="请选择客户"
              readonly
              @click="showCustomerPicker = true"
            />

            <van-field
              v-model="advancedFilter.operator"
              label="操作员"
              placeholder="请选择操作员"
              readonly
              @click="showOperatorPicker = true"
            />

            <van-field
              v-model="advancedFilter.status"
              label="单据状态"
              placeholder="请选择单据状态"
              readonly
              @click="showStatusPicker = true"
            />

            <van-field
              v-model="advancedFilter.remark"
              label="单据备注"
              placeholder="请输入单据备注"
              type="textarea"
              rows="2"
            />
          </van-form>
        </div>

        <div class="filter-actions">
          <van-button
            type="default"
            size="large"
            @click="handleFilterCancel"
          >
            返回
          </van-button>
          <van-button
            type="default"
            size="large"
            @click="handleFilterReset"
          >
            重置
          </van-button>
          <van-button
            type="primary"
            size="large"
            @click="handleFilterConfirm"
          >
            确定
          </van-button>
        </div>
      </div>
    </van-popup>

    <!-- 日期选择器 -->
    <van-date-picker
      v-model:show="showStartDatePicker"
      v-model="startDateValue"
      title="选择开始日期"
      @confirm="handleStartDateConfirm"
    />

    <van-date-picker
      v-model:show="showEndDatePicker"
      v-model="endDateValue"
      title="选择结束日期"
      @confirm="handleEndDateConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'

/**
 * 销售订单接口
 */
interface SalesOrder {
  id: string
  orderNo: string
  customerName: string
  customerAvatar?: string
  totalAmount: number
  orderDate: string
  status: 'draft' | 'pending' | 'approved' | 'shipped' | 'completed' | 'cancelled'
  itemCount: number
  remark?: string
}

/**
 * 搜索表单接口
 */
interface SearchForm {
  keyword: string
  status: string
  dateRange: string
}

const router = useRouter()

// 响应式数据
const orderList = ref<SalesOrder[]>([])
const loading = ref(false)
const refreshing = ref(false)
const finished = ref(false)
const currentPage = ref(1)

// 搜索表单
const searchForm = reactive<SearchForm>({
  keyword: '',
  status: '',
  dateRange: ''
})

// 高级筛选相关
const showAdvancedFilterPopup = ref<boolean>(false)
const showStartDatePicker = ref<boolean>(false)
const showEndDatePicker = ref<boolean>(false)
const showCustomerPicker = ref<boolean>(false)
const showOperatorPicker = ref<boolean>(false)
const showStatusPicker = ref<boolean>(false)

const startDateValue = ref<Date>(new Date())
const endDateValue = ref<Date>(new Date())

const advancedFilter = reactive({
  startDate: '',
  endDate: '',
  customer: '',
  operator: '',
  status: '',
  remark: ''
})

// 状态选项
const statusOptions = [
  { text: '全部状态', value: '' },
  { text: '草稿', value: 'draft' },
  { text: '待审核', value: 'pending' },
  { text: '已审核', value: 'approved' },
  { text: '已发货', value: 'shipped' },
  { text: '已完成', value: 'completed' },
  { text: '已取消', value: 'cancelled' }
]

// 日期范围选项
const dateRangeOptions = [
  { text: '全部时间', value: '' },
  { text: '今天', value: 'today' },
  { text: '本周', value: 'week' },
  { text: '本月', value: 'month' },
  { text: '本季度', value: 'quarter' }
]

/**
 * 获取状态类型
 */
const getStatusType = (status: string): string => {
  const typeMap = {
    draft: 'default' as const,
    pending: 'warning' as const,
    approved: 'primary' as const,
    shipped: 'success' as const,
    completed: 'success' as const,
    cancelled: 'danger' as const
  }
  return typeMap[status as keyof typeof typeMap] || 'default' as const
}

/**
 * 获取状态文本
 */
const getStatusText = (status: string): string => {
  const textMap: Record<string, string> = {
    draft: '草稿',
    pending: '待审核',
    approved: '已审核',
    shipped: '已发货',
    completed: '已完成',
    cancelled: '已取消'
  }
  return textMap[status] || '未知'
}

/**
 * 格式化货币
 */
const formatCurrency = (amount: number): string => {
  return amount.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

/**
 * 格式化日期
 */
const formatDate = (dateStr: string): string => {
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN')
}

/**
 * 模拟数据加载
 */
const loadOrderData = async (page: number = 1): Promise<SalesOrder[]> => {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  // 模拟订单数据
  const mockOrders: SalesOrder[] = Array.from({ length: 10 }, (_, index) => ({
    id: `order_${page}_${index + 1}`,
    orderNo: `SO${String(page).padStart(2, '0')}${String(index + 1).padStart(3, '0')}`,
    customerName: `客户${page}${index + 1}`,
    customerAvatar: undefined,
    totalAmount: Math.random() * 10000 + 1000,
    orderDate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
    status: ['draft', 'pending', 'approved', 'shipped', 'completed'][Math.floor(Math.random() * 5)] as any,
    itemCount: Math.floor(Math.random() * 10) + 1,
    remark: Math.random() > 0.5 ? `订单备注${index + 1}` : undefined
  }))
  
  return mockOrders
}

/**
 * 加载更多数据
 */
const handleLoadMore = async (): Promise<void> => {
  if (loading.value) return
  
  loading.value = true
  try {
    const newOrders = await loadOrderData(currentPage.value)
    
    if (newOrders.length === 0) {
      finished.value = true
    } else {
      orderList.value.push(...newOrders)
      currentPage.value++
    }
  } catch (error) {
    showToast({ type: 'fail', message: '加载失败' })
  } finally {
    loading.value = false
  }
}

/**
 * 下拉刷新
 */
const handleRefresh = async (): Promise<void> => {
  refreshing.value = true
  try {
    currentPage.value = 1
    finished.value = false
    const newOrders = await loadOrderData(1)
    orderList.value = newOrders
    showToast({ type: 'success', message: '刷新成功' })
  } catch (error) {
    showToast({ type: 'fail', message: '刷新失败' })
  } finally {
    refreshing.value = false
  }
}

/**
 * 搜索处理
 */
const handleSearch = (): void => {
  showToast(`搜索: ${searchForm.keyword}`)
  // TODO: 实现搜索逻辑
}

/**
 * 清除搜索
 */
const handleClearSearch = (): void => {
  searchForm.keyword = ''
  // TODO: 重新加载数据
}

/**
 * 筛选变化处理
 */
const handleFilterChange = (): void => {
  showToast('筛选条件已更新')
  // TODO: 根据筛选条件重新加载数据
}

/**
 * 订单点击处理
 */
const handleOrderClick = (order: SalesOrder): void => {
  router.push(`/orders/sales-order/${order.id}`)
}

/**
 * 新增订单
 */
const handleAddOrder = (): void => {
  router.push('/orders/sales-order/add')
}

/**
 * 扫码功能
 */
const handleScan = (): void => {
  // TODO: 实现扫码功能
  console.log('扫码功能')
}

/**
 * 搜索栏扫码
 */
const handleScanSearch = (): void => {
  // TODO: 实现搜索栏扫码功能
  console.log('搜索栏扫码功能')
}

/**
 * 显示高级筛选
 */
const showAdvancedFilter = (): void => {
  showAdvancedFilterPopup.value = true
}

/**
 * 筛选弹窗扫码
 */
const handleFilterScan = (): void => {
  // TODO: 实现筛选弹窗扫码功能
  console.log('筛选弹窗扫码功能')
}

/**
 * 筛选取消
 */
const handleFilterCancel = (): void => {
  showAdvancedFilterPopup.value = false
}

/**
 * 筛选重置
 */
const handleFilterReset = (): void => {
  advancedFilter.startDate = ''
  advancedFilter.endDate = ''
  advancedFilter.customer = ''
  advancedFilter.operator = ''
  advancedFilter.status = ''
  advancedFilter.remark = ''
}

/**
 * 筛选确定
 */
const handleFilterConfirm = (): void => {
  // TODO: 应用筛选条件
  console.log('应用筛选条件:', advancedFilter)
  showAdvancedFilterPopup.value = false
  // 重新加载数据
  handleRefresh()
}

/**
 * 开始日期确认
 */
const handleStartDateConfirm = (value: Date): void => {
  advancedFilter.startDate = formatDate(value)
  showStartDatePicker.value = false
}

/**
 * 结束日期确认
 */
const handleEndDateConfirm = (value: Date): void => {
  advancedFilter.endDate = formatDate(value)
  showEndDatePicker.value = false
}

/**
 * 打印订单
 */
const handlePrintOrder = (order: any): void => {
  // TODO: 实现打印功能
  console.log('打印订单:', order)
}

/**
 * 分享订单
 */
const handleShareOrder = (order: any): void => {
  // TODO: 实现分享功能
  console.log('分享订单:', order)
}

/**
 * 编辑订单
 */
const handleEditOrder = (order: SalesOrder): void => {
  router.push(`/orders/sales-order/edit/${order.id}`)
}

/**
 * 审核订单
 */
const handleApproveOrder = (order: SalesOrder): void => {
  showToast(`审核订单: ${order.orderNo}`)
  // TODO: 实现审核逻辑
}

// 组件挂载时加载数据
onMounted(() => {
  handleLoadMore()
})
</script>

<style lang="less" scoped>
.search-section {
  padding: 12px 16px;
  background: #fff;

  .search-container {
    display: flex;
    align-items: center;
    gap: 12px;

    .van-search {
      flex: 1;
    }
  }

  .search-actions {
    display: flex;
    align-items: center;
    gap: 12px;

    .van-icon {
      font-size: 18px;
      color: #969799;
      cursor: pointer;
      padding: 4px;

      &:hover {
        color: #1989fa;
      }

      &:active {
        color: #0570d9;
      }
    }
  }
}

.filter-section {
  background: #fff;
  border-bottom: 1px solid #eee;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 12px;

  .van-icon {
    font-size: 18px;
    color: white;
    cursor: pointer;
  }
}

.order-list {
  padding: 0 16px;
}

.order-item {
  margin-bottom: 12px;
  
  .van-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.order-info {
  flex: 1;
}

.order-amount {
  font-size: 16px;
  font-weight: bold;
  color: #ff6b35;
}

.order-date {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.order-actions {
  display: flex;
  gap: 8px;
}

.filter-popup {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;

  .filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }

    .van-icon {
      font-size: 20px;
      color: #1989fa;
      cursor: pointer;
    }
  }

  .filter-content {
    flex: 1;
    overflow-y: auto;

    .van-field {
      margin-bottom: 12px;
    }
  }

  .filter-actions {
    display: flex;
    gap: 12px;
    padding-top: 16px;
    border-top: 1px solid #eee;

    .van-button {
      flex: 1;
    }
  }
}
</style>
