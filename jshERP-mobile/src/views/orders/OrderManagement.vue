<!--
  开单管理页面

  包含特殊功能和多个业务模块分组
-->
<template>
  <div class="erp-page">
    <!-- 页面头部 -->
    <div class="erp-header erp-header--simple">
      <h1 class="erp-header__title">开单</h1>
    </div>

    <!-- 特殊功能：拆卸单 -->
    <div class="special-function">
      <div class="erp-card erp-card--no-margin">
        <div class="erp-card__body">
          <ERPFunctionGrid
            :items="[specialFunction]"
            :columns="1"
            @item-click="handleFunctionClick"
          />
        </div>
      </div>
    </div>

    <!-- 业务模块分组 -->
    <div v-for="section in sections" :key="section.title" class="erp-section">
      <ERPSectionTitle :title="section.title" />
      <div class="erp-section__content">
        <ERPFunctionGrid
          :items="section.items"
          :columns="getGridColumns(section.items.length)"
          @item-click="handleFunctionClick"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { showToast } from 'vant'
import ERPSectionTitle from '@/components/erp/ERPSectionTitle.vue'
import ERPFunctionGrid from '@/components/erp/ERPFunctionGrid.vue'
import type { FunctionItem, FunctionSection } from '@/types/erp'

/**
 * 特殊功能：拆卸单
 */
const specialFunction: FunctionItem = {
  id: 'disassemble',
  icon: 'setting-o',
  label: '拆卸单',
  color: '#FF6B35'
}

/**
 * 开单管理业务模块配置
 */
const sections: FunctionSection[] = [
  {
    title: '盘点业务',
    items: [
      { id: 'inventory-input', icon: 'edit', label: '盘点录入', color: '#FF6B35' },
      { id: 'inventory-review', icon: 'checked', label: '盘点复盘', color: '#FF6B35' }
    ]
  },
  {
    title: '财务管理',
    items: [
      { id: 'income-order', icon: 'gold-coin-o', label: '收入单', color: '#FF6B35' },
      { id: 'expense-order', icon: 'minus', label: '支出单', color: '#FF6B35' },
      { id: 'receipt-order', icon: 'credit-pay', label: '收款单', color: '#FF6B35' },
      { id: 'payment-order', icon: 'debit-pay', label: '付款单', color: '#FF6B35' },
      { id: 'transfer-order', icon: 'exchange', label: '转账单', color: '#FF6B35' },
      { id: 'prepayment', icon: 'balance-pay', label: '收预付款', color: '#FF6B35' }
    ]
  },
  {
    title: '零售管理',
    items: [
      { id: 'retail-outbound', icon: 'shop-o', label: '零售出库', color: '#FF6B35' },
      { id: 'retail-return', icon: 'revoke', label: '零售退货', color: '#FF6B35' }
    ]
  },
  {
    title: '采购管理',
    items: [
      { id: 'purchase-request', icon: 'orders-o', label: '请购单', color: '#FF6B35' },
      { id: 'purchase-order', icon: 'shopping-cart-o', label: '采购订单', color: '#FF6B35' },
      { id: 'purchase-inbound', icon: 'logistics', label: '采购入库', color: '#FF6B35' },
      { id: 'purchase-return', icon: 'revoke', label: '采购退货', color: '#FF6B35' }
    ]
  },
  {
    title: '销售管理',
    items: [
      { id: 'sales-order', icon: 'orders-o', label: '销售订单', color: '#FF6B35' },
      { id: 'sales-outbound', icon: 'send-gift-o', label: '销售出库', color: '#FF6B35' },
      { id: 'sales-return', icon: 'revoke', label: '销售退货', color: '#FF6B35' }
    ]
  },
  {
    title: '仓库管理',
    items: [
      { id: 'other-inbound', icon: 'logistics', label: '其它入库', color: '#FF6B35' },
      { id: 'other-outbound', icon: 'send-gift-o', label: '其它出库', color: '#FF6B35' },
      { id: 'transfer-outbound', icon: 'exchange', label: '调拨出库', color: '#FF6B35' },
      { id: 'assembly-order', icon: 'setting-o', label: '组装单', color: '#FF6B35' }
    ]
  }
]

/**
 * 根据功能项数量确定网格列数
 */
const getGridColumns = (itemCount: number): number => {
  if (itemCount <= 2) return 2
  if (itemCount <= 3) return 3
  return 4
}

/**
 * 处理功能项点击
 */
const handleFunctionClick = (item: FunctionItem): void => {
  console.log('Order function clicked:', item)
  showToast(`点击了${item.label}`)

  // TODO: 根据功能ID跳转到对应页面或执行对应操作
  switch (item.id) {
    case 'disassemble':
      // 跳转到拆卸单页面
      break
    case 'sales-order':
      // 跳转到销售订单页面
      break
    case 'purchase-order':
      // 跳转到采购订单页面
      break
    case 'inventory-input':
      // 跳转到盘点录入页面
      break
    default:
      // 其他功能的处理逻辑
      break
  }
}
</script>

<style scoped lang="less">
@import '@/styles/variables.less';
@import '@/styles/common.less';

.special-function {
  margin: var(--erp-spacing-md) var(--erp-spacing-md) var(--erp-spacing-lg);

  .erp-card {
    background: linear-gradient(135deg, #FF6B35 0%, #FF8C42 100%);

    :deep(.erp-function-grid__item) {
      .erp-function-grid__icon {
        color: var(--erp-text-white) !important;
      }

      .erp-function-grid__label {
        color: var(--erp-text-white);
        font-weight: var(--erp-font-weight-semibold);
      }
    }
  }
}

.erp-section {
  &__content {
    background: var(--erp-bg-card);
    border-radius: var(--erp-radius-lg);
    box-shadow: var(--erp-shadow-card);
    margin: 0 var(--erp-spacing-md) var(--erp-spacing-lg);
    overflow: hidden;
  }
}

// 响应式适配
@media (max-width: @erp-mobile-xs) {
  .special-function {
    margin: var(--erp-spacing-sm) var(--erp-spacing-sm) var(--erp-spacing-md);
  }

  .erp-section {
    &__content {
      margin: 0 var(--erp-spacing-sm) var(--erp-spacing-md);
    }
  }
}
</style>
