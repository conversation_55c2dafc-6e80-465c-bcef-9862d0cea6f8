<!--
  销售订单详情页面
  
  功能：
  - 订单基本信息展示
  - 商品明细列表
  - 金额汇总计算
  - 状态流程展示
  - 操作按钮（编辑、审核等）
-->
<template>
  <div class="erp-page">
    <!-- 页面头部 -->
    <div class="erp-header">
      <van-nav-bar
        :title="orderDetail?.orderNo || '订单详情'"
        left-arrow
        @click-left="$router.back()"
      >
        <template #right>
          <van-icon name="share-o" @click="handleShare" />
        </template>
      </van-nav-bar>
    </div>

    <!-- 加载状态 -->
    <van-loading v-if="loading" class="loading-center" vertical>
      加载中...
    </van-loading>

    <!-- 订单详情内容 -->
    <div v-else-if="orderDetail" class="order-detail">
      <!-- 订单状态卡片 -->
      <div class="status-card">
        <div class="status-header">
          <van-tag
            :type="getStatusType(orderDetail.status)"
            size="large"
          >
            {{ getStatusText(orderDetail.status) }}
          </van-tag>
          <div class="order-amount">
            ¥{{ formatCurrency(orderDetail.totalAmount) }}
          </div>
        </div>
        <div class="status-info">
          <div class="info-item">
            <span class="label">订单编号</span>
            <span class="value">{{ orderDetail.orderNo }}</span>
          </div>
          <div class="info-item">
            <span class="label">下单时间</span>
            <span class="value">{{ formatDateTime(orderDetail.orderDate) }}</span>
          </div>
        </div>
      </div>

      <!-- 客户信息 -->
      <div class="section-card">
        <div class="section-title">
          <van-icon name="user-o" />
          <span>客户信息</span>
        </div>
        <div class="customer-info">
          <div class="customer-main">
            <div class="customer-name">{{ orderDetail.customerName }}</div>
            <div class="customer-contact">{{ orderDetail.customerPhone }}</div>
          </div>
          <div class="customer-address">
            <van-icon name="location-o" />
            <span>{{ orderDetail.deliveryAddress }}</span>
          </div>
        </div>
      </div>

      <!-- 商品明细 -->
      <div class="section-card">
        <div class="section-title">
          <van-icon name="goods-collect-o" />
          <span>商品明细</span>
          <span class="item-count">({{ orderDetail.items.length }}件商品)</span>
        </div>
        <div class="product-list">
          <div
            v-for="item in orderDetail.items"
            :key="item.id"
            class="product-item"
          >
            <div class="product-image">
              <van-image
                :src="item.productImage"
                fit="cover"
                :alt="item.productName"
              >
                <template #error>
                  <van-icon name="photo-fail" />
                </template>
              </van-image>
            </div>
            <div class="product-info">
              <div class="product-name">{{ item.productName }}</div>
              <div class="product-spec">{{ item.specification }}</div>
              <div class="product-price">
                <span class="unit-price">¥{{ formatCurrency(item.unitPrice) }}</span>
                <span class="quantity">x{{ item.quantity }}</span>
              </div>
            </div>
            <div class="product-total">
              ¥{{ formatCurrency(item.totalPrice) }}
            </div>
          </div>
        </div>
      </div>

      <!-- 金额汇总 -->
      <div class="section-card">
        <div class="section-title">
          <van-icon name="balance-pay" />
          <span>费用明细</span>
        </div>
        <div class="amount-summary">
          <div class="amount-item">
            <span class="label">商品总额</span>
            <span class="value">¥{{ formatCurrency(orderDetail.subtotal) }}</span>
          </div>
          <div class="amount-item">
            <span class="label">运费</span>
            <span class="value">¥{{ formatCurrency(orderDetail.shippingFee) }}</span>
          </div>
          <div class="amount-item discount" v-if="orderDetail.discount > 0">
            <span class="label">优惠金额</span>
            <span class="value">-¥{{ formatCurrency(orderDetail.discount) }}</span>
          </div>
          <div class="amount-item total">
            <span class="label">应付总额</span>
            <span class="value">¥{{ formatCurrency(orderDetail.totalAmount) }}</span>
          </div>
        </div>
      </div>

      <!-- 订单备注 -->
      <div v-if="orderDetail.remark" class="section-card">
        <div class="section-title">
          <van-icon name="notes-o" />
          <span>订单备注</span>
        </div>
        <div class="remark-content">
          {{ orderDetail.remark }}
        </div>
      </div>

      <!-- 状态流程 -->
      <div class="section-card">
        <div class="section-title">
          <van-icon name="clock-o" />
          <span>状态流程</span>
        </div>
        <div class="status-timeline">
          <van-steps
            :active="getActiveStep(orderDetail.status)"
            direction="vertical"
          >
            <van-step
              v-for="step in statusSteps"
              :key="step.status"
              :title="step.title"
              :description="step.description"
            />
          </van-steps>
        </div>
      </div>
    </div>

    <!-- 错误状态 -->
    <van-empty
      v-else
      description="订单不存在或已删除"
      image="error"
    />

    <!-- 底部操作栏 -->
    <div v-if="orderDetail" class="action-bar">
      <van-button
        v-if="orderDetail.status === 'draft'"
        type="primary"
        size="large"
        @click="handleEdit"
      >
        编辑订单
      </van-button>
      <van-button
        v-if="orderDetail.status === 'pending'"
        type="success"
        size="large"
        @click="handleApprove"
      >
        审核通过
      </van-button>
      <van-button
        v-if="orderDetail.status === 'approved'"
        type="warning"
        size="large"
        @click="handleShip"
      >
        确认发货
      </van-button>
      <van-button
        type="default"
        size="large"
        @click="handlePrint"
      >
        打印订单
      </van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast, showDialog } from 'vant'

/**
 * 订单商品明细接口
 */
interface OrderItem {
  id: string
  productId: string
  productName: string
  productImage?: string
  specification: string
  unitPrice: number
  quantity: number
  totalPrice: number
}

/**
 * 销售订单详情接口
 */
interface SalesOrderDetail {
  id: string
  orderNo: string
  customerName: string
  customerPhone: string
  deliveryAddress: string
  orderDate: string
  status: 'draft' | 'pending' | 'approved' | 'shipped' | 'completed' | 'cancelled'
  items: OrderItem[]
  subtotal: number
  shippingFee: number
  discount: number
  totalAmount: number
  remark?: string
  createTime: string
  updateTime: string
}

/**
 * 状态步骤接口
 */
interface StatusStep {
  status: string
  title: string
  description: string
}

const router = useRouter()
const route = useRoute()

// 响应式数据
const orderDetail = ref<SalesOrderDetail | null>(null)
const loading = ref(true)

// 状态步骤配置
const statusSteps: StatusStep[] = [
  { status: 'draft', title: '创建订单', description: '订单已创建，等待完善信息' },
  { status: 'pending', title: '提交审核', description: '订单已提交，等待审核' },
  { status: 'approved', title: '审核通过', description: '订单已审核通过，准备发货' },
  { status: 'shipped', title: '确认发货', description: '订单已发货，等待收货确认' },
  { status: 'completed', title: '订单完成', description: '订单已完成，交易结束' }
]

/**
 * 获取状态类型
 */
const getStatusType = (status: string) => {
  const typeMap = {
    draft: 'default' as const,
    pending: 'warning' as const,
    approved: 'primary' as const,
    shipped: 'success' as const,
    completed: 'success' as const,
    cancelled: 'danger' as const
  }
  return typeMap[status as keyof typeof typeMap] || 'default' as const
}

/**
 * 获取状态文本
 */
const getStatusText = (status: string): string => {
  const textMap: Record<string, string> = {
    draft: '草稿',
    pending: '待审核',
    approved: '已审核',
    shipped: '已发货',
    completed: '已完成',
    cancelled: '已取消'
  }
  return textMap[status] || '未知'
}

/**
 * 获取当前激活步骤
 */
const getActiveStep = (status: string): number => {
  const stepMap: Record<string, number> = {
    draft: 0,
    pending: 1,
    approved: 2,
    shipped: 3,
    completed: 4,
    cancelled: -1
  }
  return stepMap[status] || 0
}

/**
 * 格式化货币
 */
const formatCurrency = (amount: number): string => {
  return amount.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

/**
 * 格式化日期时间
 */
const formatDateTime = (dateStr: string): string => {
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN')
}

/**
 * 模拟加载订单详情
 */
const loadOrderDetail = async (orderId: string): Promise<void> => {
  loading.value = true
  try {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟订单详情数据
    const mockDetail: SalesOrderDetail = {
      id: orderId,
      orderNo: `SO${orderId.slice(-6).toUpperCase()}`,
      customerName: '张三丰贸易有限公司',
      customerPhone: '138-8888-8888',
      deliveryAddress: '北京市朝阳区建国门外大街1号国贸大厦A座1001室',
      orderDate: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
      status: ['draft', 'pending', 'approved', 'shipped', 'completed'][Math.floor(Math.random() * 5)] as any,
      items: Array.from({ length: Math.floor(Math.random() * 5) + 1 }, (_, index) => ({
        id: `item_${index + 1}`,
        productId: `product_${index + 1}`,
        productName: `商品名称${index + 1}`,
        productImage: undefined,
        specification: `规格${index + 1}`,
        unitPrice: Math.random() * 1000 + 100,
        quantity: Math.floor(Math.random() * 10) + 1,
        totalPrice: 0
      })),
      subtotal: 0,
      shippingFee: 50,
      discount: Math.random() * 100,
      totalAmount: 0,
      remark: Math.random() > 0.5 ? '请尽快发货，客户急需' : undefined,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    }
    
    // 计算商品总价和订单总额
    mockDetail.items.forEach(item => {
      item.totalPrice = item.unitPrice * item.quantity
    })
    mockDetail.subtotal = mockDetail.items.reduce((sum, item) => sum + item.totalPrice, 0)
    mockDetail.totalAmount = mockDetail.subtotal + mockDetail.shippingFee - mockDetail.discount
    
    orderDetail.value = mockDetail
  } catch (error) {
    showToast({ type: 'fail', message: '加载失败' })
  } finally {
    loading.value = false
  }
}

/**
 * 编辑订单
 */
const handleEdit = (): void => {
  router.push(`/orders/sales-order/edit/${orderDetail.value?.id}`)
}

/**
 * 审核订单
 */
const handleApprove = async (): Promise<void> => {
  try {
    await showDialog({
      title: '确认审核',
      message: '确定要审核通过这个订单吗？',
      confirmButtonText: '确认',
      cancelButtonText: '取消'
    })
    
    showToast({ type: 'success', message: '审核成功' })
    // TODO: 更新订单状态
    if (orderDetail.value) {
      orderDetail.value.status = 'approved'
    }
  } catch (error) {
    // 用户取消
  }
}

/**
 * 确认发货
 */
const handleShip = async (): Promise<void> => {
  try {
    await showDialog({
      title: '确认发货',
      message: '确定要确认发货吗？',
      confirmButtonText: '确认',
      cancelButtonText: '取消'
    })
    
    showToast({ type: 'success', message: '发货成功' })
    // TODO: 更新订单状态
    if (orderDetail.value) {
      orderDetail.value.status = 'shipped'
    }
  } catch (error) {
    // 用户取消
  }
}

/**
 * 打印订单
 */
const handlePrint = (): void => {
  showToast('打印功能开发中')
  // TODO: 实现打印功能
}

/**
 * 分享订单
 */
const handleShare = (): void => {
  showToast('分享功能开发中')
  // TODO: 实现分享功能
}

// 组件挂载时加载数据
onMounted(() => {
  const orderId = route.params.id as string
  if (orderId) {
    loadOrderDetail(orderId)
  } else {
    loading.value = false
  }
})
</script>

<style lang="less" scoped>
.loading-center {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.order-detail {
  padding-bottom: 80px; // 为底部操作栏留空间
}

.status-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  margin: 16px;
  border-radius: 12px;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.order-amount {
  font-size: 24px;
  font-weight: bold;
}

.status-info {
  .info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    
    .label {
      opacity: 0.8;
    }
    
    .value {
      font-weight: 500;
    }
  }
}

.section-card {
  background: white;
  margin: 8px 16px;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #333;
  
  .van-icon {
    margin-right: 8px;
    color: #3b82f6;
  }
  
  .item-count {
    margin-left: auto;
    font-size: 14px;
    color: #999;
    font-weight: normal;
  }
}

.customer-info {
  .customer-main {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }
  
  .customer-name {
    font-size: 16px;
    font-weight: 500;
  }
  
  .customer-contact {
    color: #666;
  }
  
  .customer-address {
    display: flex;
    align-items: center;
    color: #666;
    
    .van-icon {
      margin-right: 4px;
    }
  }
}

.product-list {
  .product-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
  }
}

.product-image {
  width: 60px;
  height: 60px;
  margin-right: 12px;
  border-radius: 6px;
  overflow: hidden;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .van-image {
    width: 100%;
    height: 100%;
  }
  
  .van-icon {
    color: #ccc;
    font-size: 24px;
  }
}

.product-info {
  flex: 1;
  
  .product-name {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 4px;
  }
  
  .product-spec {
    font-size: 12px;
    color: #999;
    margin-bottom: 4px;
  }
  
  .product-price {
    display: flex;
    align-items: center;
    
    .unit-price {
      color: #ff6b35;
      font-weight: 500;
    }
    
    .quantity {
      margin-left: 8px;
      color: #666;
    }
  }
}

.product-total {
  font-size: 16px;
  font-weight: bold;
  color: #ff6b35;
}

.amount-summary {
  .amount-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    
    &.discount .value {
      color: #10b981;
    }
    
    &.total {
      border-top: 1px solid #eee;
      padding-top: 12px;
      font-size: 16px;
      font-weight: bold;
      
      .value {
        color: #ff6b35;
      }
    }
  }
}

.remark-content {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  color: #666;
  line-height: 1.5;
}

.status-timeline {
  .van-steps {
    padding-left: 0;
  }
}

.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 12px 16px;
  border-top: 1px solid #eee;
  display: flex;
  gap: 12px;
  
  .van-button {
    flex: 1;
  }
}
</style>
