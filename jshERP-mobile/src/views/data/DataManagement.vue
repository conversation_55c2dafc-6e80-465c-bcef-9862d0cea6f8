<!--
  资料管理页面

  包含商品管理和基本资料两个功能分组
-->
<template>
  <div class="erp-page">
    <!-- 页面头部 -->
    <div class="erp-header erp-header--simple">
      <h1 class="erp-header__title">资料</h1>
    </div>

    <!-- 功能分组 -->
    <div v-for="section in sections" :key="section.title" class="erp-section">
      <ERPSectionTitle :title="section.title" />
      <div class="erp-section__content">
        <ERPFunctionGrid
          :items="section.items"
          :columns="4"
          @item-click="handleFunctionClick"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { showToast } from 'vant'
import ERPSectionTitle from '@/components/erp/ERPSectionTitle.vue'
import ERPFunctionGrid from '@/components/erp/ERPFunctionGrid.vue'
import type { FunctionItem, FunctionSection } from '@/types/erp'

/**
 * 资料管理功能配置
 */
const sections: FunctionSection[] = [
  {
    title: '商品管理',
    items: [
      { id: 'product-info', icon: 'goods-collect-o', label: '商品管理', color: '#FF6B35' },
      { id: 'product-category', icon: 'cluster-o', label: '商品类别', color: '#FF6B35' },
      { id: 'multi-unit', icon: 'balance-o', label: '多单位', color: '#FF6B35' },
      { id: 'multi-attribute', icon: 'label-o', label: '多属性', color: '#FF6B35' }
    ]
  },
  {
    title: '基本资料',
    items: [
      { id: 'supplier', icon: 'shop-o', label: '供应商信息', color: '#FF6B35' },
      { id: 'customer', icon: 'contact', label: '客户信息', color: '#FF6B35' },
      { id: 'member', icon: 'friends-o', label: '会员信息', color: '#FF6B35' },
      { id: 'warehouse', icon: 'home-o', label: '仓库信息', color: '#FF6B35' },
      { id: 'income-expense', icon: 'gold-coin-o', label: '收支项目', color: '#FF6B35' },
      { id: 'account', icon: 'credit-pay', label: '结算账户', color: '#FF6B35' },
      { id: 'handler', icon: 'manager-o', label: '经手人管理', color: '#FF6B35' },
      { id: 'role', icon: 'shield-o', label: '角色管理', color: '#FF6B35' },
      { id: 'log', icon: 'records', label: '日志管理', color: '#FF6B35' }
    ]
  }
]

/**
 * 处理功能项点击
 */
const handleFunctionClick = (item: FunctionItem): void => {
  console.log('Function clicked:', item)
  showToast(`点击了${item.label}`)

  // TODO: 根据功能ID跳转到对应页面或执行对应操作
  switch (item.id) {
    case 'product-info':
      // 跳转到商品管理页面
      break
    case 'customer':
      // 跳转到客户信息页面
      break
    case 'logout':
      // 执行退出登录操作
      break
    default:
      // 其他功能的处理逻辑
      break
  }
}
</script>

<style scoped lang="less">
@import '@/styles/variables.less';
@import '@/styles/common.less';

// 页面特定样式
.erp-section {
  &__content {
    background: var(--erp-bg-card);
    border-radius: var(--erp-radius-lg);
    box-shadow: var(--erp-shadow-card);
    margin: 0 var(--erp-spacing-md) var(--erp-spacing-lg);
    overflow: hidden;
  }
}
</style>
