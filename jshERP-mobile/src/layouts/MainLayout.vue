<!--
  ERP主布局组件
  
  包含底部Tab导航的主要应用布局
-->
<template>
  <div class="main-layout">
    <!-- 内容区域 -->
    <div class="main-layout__content">
      <router-view v-slot="{ Component }">
        <transition name="page" mode="out-in">
          <component :is="Component" />
        </transition>
      </router-view>
    </div>
    
    <!-- 底部Tab导航 -->
    <ERPTabBar />
  </div>
</template>

<script setup lang="ts">
import { watch } from 'vue'
import { useRoute } from 'vue-router'
import ERPTabBar from '@/components/erp/ERPTabBar.vue'

const route = useRoute()

/**
 * 监听路由变化，更新页面标题
 */
watch(
  () => route.meta.title,
  (title) => {
    if (title) {
      document.title = `${title} - jshERP移动端`
    }
  },
  { immediate: true }
)
</script>

<style scoped lang="less">
@import '@/styles/variables.less';

.main-layout {
  min-height: 100vh;
  background: var(--erp-bg-secondary);
  
  &__content {
    min-height: 100vh;
    padding-bottom: 60px; // Tab栏高度
    overflow-x: hidden;
  }
}

// 页面切换动画
.page-enter-active,
.page-leave-active {
  transition: all var(--erp-duration-normal) var(--erp-ease-in-out);
}

.page-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.page-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}
</style>
