<template>
  <div class="default-layout">
    <van-nav-bar
      :title="pageTitle"
      left-arrow
      @click-left="handleBack"
    />
    <main class="default-layout__main">
      <router-view />
    </main>
    <van-tabbar v-model="activeTab" @change="handleTabChange">
      <van-tabbar-item icon="home-o" to="/dashboard">仪表板</van-tabbar-item>
      <van-tabbar-item icon="shop-o" to="/business/sales">销售</van-tabbar-item>
      <van-tabbar-item icon="orders-o" to="/business/inventory">库存</van-tabbar-item>
      <van-tabbar-item icon="user-o" to="/profile">我的</van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()
const activeTab = ref(0)

const pageTitle = computed(() => {
  return route.meta.title as string || 'jshERP移动端'
})

const handleBack = () => {
  router.back()
}

const handleTabChange = (index: number) => {
  console.log('Tab changed to:', index)
}
</script>

<style scoped>
.default-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.default-layout__main {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-md);
  background-color: var(--background-color);
}
</style>