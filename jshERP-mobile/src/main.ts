import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import { setupRouterGuards } from './router/guards'
import { setupVant } from './plugins/vant'
import { useAuthStore, useAppStore } from './stores'

import App from './App.vue'

// 样式
import './styles/main.css'

const app = createApp(App)

// 配置Vant
setupVant(app)

// 配置Pinia
const pinia = createPinia()
app.use(pinia)

// 配置路由
app.use(router)

// 设置路由守卫
setupRouterGuards(router)

// 初始化stores
const authStore = useAuthStore()
const appStore = useAppStore()
authStore.initAuth()
appStore.initApp()

app.mount('#app')