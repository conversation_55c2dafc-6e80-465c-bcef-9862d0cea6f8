import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/auth',
    component: () => import('@/layouts/AuthLayout.vue'),
    children: [
      {
        path: 'login',
        name: 'Login',
        component: () => import('@/views/auth/Login.vue'),
        meta: { 
          requiresAuth: false,
          title: '用户登录'
        }
      }
    ]
  },
  {
    path: '/dashboard',
    component: () => import('@/layouts/DefaultLayout.vue'),
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/Index.vue'),
        meta: { 
          requiresAuth: true,
          title: '仪表板',
          permissions: ['dashboard:view']
        }
      }
    ]
  },
  {
    path: '/business',
    component: () => import('@/layouts/DefaultLayout.vue'),
    children: [
      {
        path: 'sales',
        name: 'Sales',
        component: () => import('@/views/business/Sales.vue'),
        meta: { 
          requiresAuth: true,
          title: '销售管理',
          permissions: ['sales:manage']
        }
      },
      {
        path: 'inventory',
        name: 'Inventory',
        component: () => import('@/views/business/Inventory.vue'),
        meta: { 
          requiresAuth: true,
          title: '库存管理',
          permissions: ['inventory:manage']
        }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

export default router