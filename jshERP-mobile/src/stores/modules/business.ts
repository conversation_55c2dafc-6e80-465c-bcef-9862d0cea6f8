import { defineStore } from 'pinia'
import { ref } from 'vue'

export interface DashboardData {
  todaySales: number
  todayOrders: number
  lowStockCount: number
  pendingTasks: number
}

export const useBusinessStore = defineStore('business', () => {
  const salesData = ref([])
  const inventoryData = ref([])
  const dashboardData = ref<DashboardData | null>(null)
  
  const fetchDashboardData = async () => {
    try {
      // TODO: 实现API调用
      console.log('Fetching dashboard data...')
      
      // 模拟数据
      dashboardData.value = {
        todaySales: 12580.50,
        todayOrders: 28,
        lowStockCount: 5,
        pendingTasks: 12
      }
      
      return dashboardData.value
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error)
      throw error
    }
  }
  
  const fetchSalesData = async () => {
    try {
      // TODO: 实现API调用
      console.log('Fetching sales data...')
      salesData.value = []
      return salesData.value
    } catch (error) {
      console.error('Failed to fetch sales data:', error)
      throw error
    }
  }
  
  const fetchInventoryData = async () => {
    try {
      // TODO: 实现API调用
      console.log('Fetching inventory data...')
      inventoryData.value = []
      return inventoryData.value
    } catch (error) {
      console.error('Failed to fetch inventory data:', error)
      throw error
    }
  }
  
  return {
    salesData,
    inventoryData,
    dashboardData,
    fetchDashboardData,
    fetchSalesData,
    fetchInventoryData
  }
})