import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface UserInfo {
  id: string
  username: string
  realname: string
  avatar: string
  email?: string
  phone?: string
}

export interface LoginCredentials {
  username: string
  password: string
  captcha?: string
  rememberMe?: boolean
}

export const useAuthStore = defineStore('auth', () => {
  const token = ref<string>('')
  const userInfo = ref<UserInfo | null>(null)
  const permissions = ref<string[]>([])
  
  const isLoggedIn = computed(() => !!token.value)
  
  const login = async (credentials: LoginCredentials): Promise<{ success: boolean }> => {
    try {
      // TODO: 实现登录API调用
      console.log('Login with:', credentials)
      
      // 模拟登录成功
      token.value = 'mock-token'
      userInfo.value = {
        id: '1',
        username: credentials.username,
        realname: '测试用户',
        avatar: '/default-avatar.png'
      }
      permissions.value = ['dashboard:view', 'sales:manage']
      
      // 持久化存储
      localStorage.setItem('auth_token', token.value)
      localStorage.setItem('user_info', JSON.stringify(userInfo.value))
      
      return { success: true }
    } catch (error) {
      console.error('Login failed:', error)
      throw error
    }
  }
  
  const logout = async (): Promise<void> => {
    try {
      // TODO: 调用登出API
      console.log('Logout')
    } finally {
      // 清除状态
      token.value = ''
      userInfo.value = null
      permissions.value = []
      
      // 清除存储
      localStorage.removeItem('auth_token')
      localStorage.removeItem('user_info')
    }
  }
  
  const checkPermission = (permission: string): boolean => {
    return permissions.value.includes(permission)
  }
  
  // 初始化时从localStorage恢复状态
  const initAuth = (): void => {
    const savedToken = localStorage.getItem('auth_token')
    const savedUserInfo = localStorage.getItem('user_info')
    
    if (savedToken && savedUserInfo) {
      token.value = savedToken
      userInfo.value = JSON.parse(savedUserInfo)
      permissions.value = ['dashboard:view', 'sales:manage'] // TODO: 从API获取
    }
  }
  
  return {
    token,
    userInfo,
    permissions,
    isLoggedIn,
    login,
    logout,
    checkPermission,
    initAuth
  }
})