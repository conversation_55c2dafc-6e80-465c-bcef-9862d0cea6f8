import type { BaseEntity } from './common'

// 用户相关类型
export interface UserInfo {
  id: string
  username: string
  realname: string
  avatar: string
  email?: string
  phone?: string
  status?: number
  departId?: string
  roleIds?: string[]
}

export interface LoginCredentials {
  username: string
  password: string
  captcha?: string
  rememberMe?: boolean
}

export interface LoginResponse {
  token: string
  refreshToken: string
  userInfo: UserInfo
  permissions: string[]
  expiresIn: number
}

// 商品相关类型
export interface ProductInfo extends BaseEntity {
  name: string
  code: string
  categoryId: string
  categoryName?: string
  price: number
  stock: number
  unit: string
  image?: string
  description?: string
  status: number
}

// 客户相关类型
export interface CustomerInfo extends BaseEntity {
  name: string
  code: string
  phone?: string
  email?: string
  address?: string
  contactPerson?: string
  level?: string
  status: number
}

// 订单相关类型
export interface OrderInfo extends BaseEntity {
  orderNo: string
  customerId: string
  customerName?: string
  totalAmount: number
  discountAmount?: number
  actualAmount: number
  status: number
  orderDate: string
  remark?: string
  items?: OrderItemInfo[]
}

export interface OrderItemInfo extends BaseEntity {
  orderId: string
  productId: string
  productName?: string
  quantity: number
  price: number
  amount: number
}

// 库存相关类型
export interface InventoryInfo extends BaseEntity {
  productId: string
  productName?: string
  warehouseId: string
  warehouseName?: string
  currentStock: number
  availableStock: number
  lockedStock: number
  minStock?: number
  maxStock?: number
}

// 仪表板数据类型
export interface DashboardData {
  todaySales: number
  todayOrders: number
  lowStockCount: number
  pendingTasks: number
  salesTrend?: number[]
  orderTrend?: number[]
}