import { BaseAdapter } from './base'
import { httpClient } from '@/utils/request'
import type { DashboardData } from '@/stores/modules/business'

export interface PageRequest {
  current: number
  size: number
  sort?: string
  order?: 'asc' | 'desc'
}

export interface PageResponse<T> {
  records: T[]
  total: number
  size: number
  current: number
  pages: number
}

export class BusinessAdapter extends BaseAdapter<any, any> {
  protected endpoint = '/business'
  
  transform(rawData: any): any {
    return rawData.result || rawData
  }
  
  async getDashboardData(): Promise<DashboardData> {
    const response = await httpClient.get('/dashboard/data') as any
    return {
      todaySales: response.result?.todaySales || 0,
      todayOrders: response.result?.todayOrders || 0,
      lowStockCount: response.result?.lowStockCount || 0,
      pendingTasks: response.result?.pendingTasks || 0
    }
  }
  
  async getSalesList(params: PageRequest): Promise<PageResponse<any>> {
    const response = await httpClient.get('/sales/list', { params }) as any
    return {
      records: response.result?.records || response.records || [],
      total: response.result?.total || response.total || 0,
      size: response.result?.size || response.size || params.size,
      current: response.result?.current || response.current || params.current,
      pages: response.result?.pages || response.pages || 0
    }
  }
  
  async getInventoryList(params: PageRequest): Promise<PageResponse<any>> {
    const response = await httpClient.get('/inventory/list', { params }) as any
    return {
      records: response.result?.records || response.records || [],
      total: response.result?.total || response.total || 0,
      size: response.result?.size || response.size || params.size,
      current: response.result?.current || response.current || params.current,
      pages: response.result?.pages || response.pages || 0
    }
  }
}

export const businessAdapter = new BusinessAdapter()