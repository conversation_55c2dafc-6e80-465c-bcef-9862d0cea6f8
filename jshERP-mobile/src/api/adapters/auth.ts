import { BaseAdapter } from './base'
import { httpClient } from '@/utils/request'
import type { LoginCredentials, UserInfo } from '@/stores/modules/auth'

export interface LoginResponse {
  token: string
  refreshToken: string
  userInfo: UserInfo
  permissions: string[]
  expiresIn: number
}

export class AuthAdapter extends BaseAdapter<LoginCredentials, LoginResponse> {
  protected endpoint = '/sys/login'
  
  transform(rawData: any): LoginResponse {
    return {
      token: rawData.result?.token || rawData.token,
      refreshToken: rawData.result?.refreshToken || rawData.refreshToken,
      userInfo: {
        id: rawData.result?.userInfo?.id || rawData.userInfo?.id,
        username: rawData.result?.userInfo?.username || rawData.userInfo?.username,
        realname: rawData.result?.userInfo?.realname || rawData.userInfo?.realname,
        avatar: rawData.result?.userInfo?.avatar || rawData.userInfo?.avatar || '/default-avatar.png',
        email: rawData.result?.userInfo?.email || rawData.userInfo?.email,
        phone: rawData.result?.userInfo?.phone || rawData.userInfo?.phone
      },
      permissions: rawData.result?.permissions || rawData.permissions || [],
      expiresIn: rawData.result?.expiresIn || rawData.expiresIn || 7200
    }
  }
  
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    return this.post(credentials)
  }
  
  async logout(): Promise<void> {
    try {
      await httpClient.post('/sys/logout')
    } catch (error) {
      console.error('Logout error:', error)
      // 即使登出失败也要清除本地状态
    }
  }
  
  async refreshToken(refreshToken: string): Promise<LoginResponse> {
    const response = await httpClient.post('/sys/refreshToken', { refreshToken })
    return this.transform(response)
  }
  
  async getCurrentUser(): Promise<UserInfo> {
    const response = await httpClient.get('/sys/user/getUserInfo') as any
    return {
      id: response.result?.id || response.id,
      username: response.result?.username || response.username,
      realname: response.result?.realname || response.realname,
      avatar: response.result?.avatar || response.avatar || '/default-avatar.png',
      email: response.result?.email || response.email,
      phone: response.result?.phone || response.phone
    }
  }
  
  async getPermissions(): Promise<string[]> {
    const response = await httpClient.get('/sys/permission/getUserPermission') as any
    return response.result || response.permissions || []
  }
}

export const authAdapter = new AuthAdapter()