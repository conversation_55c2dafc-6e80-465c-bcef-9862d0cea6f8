# jshERP移动端

基于Vue 3 + Vite + TypeScript + Vant 4构建的现代化移动端ERP应用。

## 🚀 项目特性

- ✅ **现代化技术栈**：Vue 3 + Composition API + TypeScript
- ✅ **高性能构建**：Vite极速开发体验
- ✅ **专业UI组件**：Vant 4移动端组件库
- ✅ **状态管理**：Pinia轻量级状态管理
- ✅ **路由管理**：Vue Router 4支持
- ✅ **代码规范**：ESLint + Prettier + Husky
- ✅ **类型安全**：完整的TypeScript类型系统
- ✅ **移动端优化**：响应式设计，触摸友好

## 📦 项目结构

```
jshERP-mobile/
├── src/
│   ├── api/                 # API接口层
│   │   ├── adapters/       # 数据适配器
│   │   ├── endpoints/      # 接口端点
│   │   └── types/          # API类型定义
│   ├── components/         # 组件库
│   │   ├── base/          # 基础组件
│   │   ├── business/      # 业务组件
│   │   └── layout/        # 布局组件
│   ├── composables/       # 组合式函数
│   ├── layouts/           # 页面布局
│   ├── plugins/           # 插件配置
│   ├── router/            # 路由配置
│   ├── stores/            # Pinia状态管理
│   ├── styles/            # 样式文件
│   ├── utils/             # 工具函数
│   └── views/             # 页面组件
├── public/                # 静态资源
├── docs/                  # 项目文档
└── 配置文件
```

## 🛠️ 开发环境

### 环境要求
- Node.js >= 18.0.0
- npm >= 9.0.0

### 安装依赖
```bash
cd jshERP-mobile
npm install
```

### 启动开发服务器
```bash
npm run dev
```

访问 http://localhost:8081 查看应用

### 构建生产版本
```bash
npm run build
```

### 代码检查和格式化
```bash
# ESLint检查
npm run lint

# Prettier格式化
npm run format

# TypeScript类型检查
npm run type-check
```

## 🎯 核心功能

### 已实现功能
- ✅ 用户认证系统（登录/登出）
- ✅ 仪表板数据展示
- ✅ 销售管理模块
- ✅ 库存管理模块
- ✅ 响应式布局设计
- ✅ 状态管理和数据持久化
- ✅ API适配器和错误处理

### 计划功能
- 🔄 扫码识别功能
- 🔄 拍照记录功能
- 🔄 地理位置功能
- 🔄 推送通知功能
- 🔄 离线缓存功能
- 🔄 PWA支持

## 🔧 配置说明

### 环境变量
```bash
# 开发环境 (.env.development)
VITE_API_BASE_URL=http://localhost:9999
VITE_APP_TITLE=jshERP移动端
VITE_APP_VERSION=1.0.0

# 生产环境 (.env.production)
VITE_API_BASE_URL=https://cms.linghuaart.com/api
VITE_APP_TITLE=jshERP移动端
VITE_APP_VERSION=1.0.0
```

### Vant主题定制
在 `vite.config.ts` 中配置主题变量：
```typescript
css: {
  preprocessorOptions: {
    less: {
      modifyVars: {
        'primary-color': '#3B82F6',
        'success-color': '#10B981',
        // 更多主题变量...
      }
    }
  }
}
```

## 📱 移动端特性

### 响应式设计
- 支持320px - 768px宽度设备
- 自适应不同屏幕密度
- 横竖屏自动适配

### 触摸交互
- 44px最小点击区域
- 手势操作支持
- 触摸反馈效果

### 性能优化
- 路由懒加载
- 组件按需引入
- 图片懒加载
- 代码分割

## 🔐 安全特性

- JWT Token认证
- 请求拦截和响应处理
- 多租户数据隔离
- XSS和CSRF防护

## 📖 开发指南

### 添加新页面
1. 在 `src/views/` 下创建页面组件
2. 在 `src/router/index.ts` 中添加路由配置
3. 设置页面权限和元信息

### 添加新API
1. 在 `src/api/types/` 中定义类型
2. 在 `src/api/adapters/` 中创建适配器
3. 在组件中使用适配器调用API

### 状态管理
使用Pinia进行状态管理：
```typescript
// 定义store
export const useExampleStore = defineStore('example', () => {
  const data = ref([])
  
  const fetchData = async () => {
    // API调用逻辑
  }
  
  return { data, fetchData }
})

// 在组件中使用
const exampleStore = useExampleStore()
```

## 🚀 部署指南

### 开发环境部署
```bash
npm run dev
```

### 生产环境部署
```bash
# 构建
npm run build

# 部署到服务器
# 将 dist/ 目录内容上传到服务器
```

### Nginx配置示例
```nginx
server {
    listen 8081;
    server_name localhost;
    
    location / {
        root /path/to/dist;
        try_files $uri $uri/ /index.html;
    }
    
    location /api/ {
        proxy_pass http://localhost:9999/;
    }
}
```

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- 项目地址：[GitHub Repository]
- 问题反馈：[Issues]
- 文档地址：[Documentation]

---

*最后更新：2024-12-25*