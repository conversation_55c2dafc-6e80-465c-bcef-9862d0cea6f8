# jshERP模块说明文档

## 1. 文档概述

本文档详细说明jshERP系统的各个技术模块和业务模块的设计实现，为二次开发提供详细的技术参考。文档涵盖后端Java模块、前端Vue模块、数据库模块的具体实现和扩展方式。

## 2. 后端模块架构

### 2.1 核心架构模块

#### 2.1.1 应用启动模块 (ErpApplication)
**文件位置**: `com.jsh.erp.ErpApplication`

**功能描述**: Spring Boot应用程序主入口，负责整个系统的启动和初始化

**关键配置**:
```java
@SpringBootApplication
@MapperScan("com.jsh.erp.datasource.mappers")
public class ErpApplication {
    public static void main(String[] args) {
        SpringApplication.run(ErpApplication.class, args);
    }
}
```

**扩展点**:
- 添加新的Bean配置
- 配置应用启动参数
- 添加启动监听器

#### 2.1.2 基础控制器模块 (BaseController)
**文件位置**: `com.jsh.erp.base.BaseController`

**功能描述**: 所有Controller的基类，提供通用的请求处理功能

**核心功能**:
- 分页参数处理
- 统一响应格式
- 异常处理
- 请求参数验证

**关键方法**:
```java
// 获取分页信息
protected PageQueryInfo getPageQueryInfo(HttpServletRequest request)

// 统一响应格式
protected BaseResponseInfo getSuccessResult(Object obj)

// 错误响应格式  
protected BaseResponseInfo getFailResult(String message)
```

**扩展方式**:
- 继承BaseController实现新的控制器
- 重写方法自定义响应格式
- 添加通用的权限检查逻辑

### 2.2 配置模块

#### 2.2.1 多租户配置 (TenantConfig)
**文件位置**: `com.jsh.erp.config.TenantConfig`

**功能描述**: 配置多租户数据隔离机制

**核心配置**:
```java
@Component
public class TenantConfig {
    // 租户ID获取逻辑
    public Long getCurrentTenantId();
    
    // 租户权限验证
    public boolean validateTenantAccess(Long tenantId);
}
```

**扩展方式**:
- 修改租户ID获取策略
- 添加租户级别的缓存配置
- 自定义租户权限验证逻辑

#### 2.2.2 Swagger配置 (Swagger2Config)
**文件位置**: `com.jsh.erp.config.Swagger2Config`

**功能描述**: 配置API文档生成

**配置内容**:
```java
@Configuration
@EnableSwagger2
public class Swagger2Config {
    @Bean
    public Docket createRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.jsh.erp.controller"))
                .paths(PathSelectors.any())
                .build();
    }
}
```

**扩展方式**:
- 添加API分组配置
- 自定义API文档信息
- 配置安全认证

#### 2.2.3 插件配置 (PluginConfiguration)
**文件位置**: `com.jsh.erp.config.PluginConfiguration`

**功能描述**: 配置插件系统

**核心功能**:
- 插件扫描和加载
- 插件生命周期管理
- 插件依赖注入

**扩展方式**:
- 添加新的插件类型
- 自定义插件加载策略
- 配置插件安全策略

### 2.3 业务控制器模块

#### 2.3.1 商品管理控制器
**文件位置**: `com.jsh.erp.controller.MaterialController`

**功能描述**: 处理商品相关的HTTP请求

**主要接口**:
```java
@RestController
@RequestMapping("/material")
public class MaterialController extends BaseController<Material> {
    
    // 商品列表查询
    @GetMapping("/list")
    public BaseResponseInfo getList(HttpServletRequest request);
    
    // 商品详情查询
    @GetMapping("/detail/{id}")
    public BaseResponseInfo getDetail(@PathVariable Long id);
    
    // 新增商品
    @PostMapping("/add")
    public BaseResponseInfo addMaterial(@RequestBody Material material);
    
    // 更新商品
    @PutMapping("/edit")
    public BaseResponseInfo updateMaterial(@RequestBody Material material);
    
    // 删除商品
    @DeleteMapping("/delete")
    public BaseResponseInfo deleteMaterial(@RequestParam("ids") String ids);
    
    // 批量操作
    @PostMapping("/batch")
    public BaseResponseInfo batchOperation(@RequestBody BatchOperationRequest request);
}
```

**扩展方式**:
- 添加新的业务接口
- 自定义查询条件
- 添加业务验证逻辑
- 扩展批量操作功能

#### 2.3.2 单据管理控制器
**文件位置**: `com.jsh.erp.controller.DepotHeadController`

**功能描述**: 处理各类业务单据的HTTP请求

**主要接口**:
```java
@RestController
@RequestMapping("/depotHead")
public class DepotHeadController extends BaseController<DepotHead> {
    
    // 单据列表查询
    @GetMapping("/list")
    public BaseResponseInfo getList(HttpServletRequest request);
    
    // 单据审核
    @PostMapping("/approve")
    public BaseResponseInfo approve(@RequestBody ApprovalRequest request);
    
    // 单据打印
    @GetMapping("/print/{id}")
    public BaseResponseInfo print(@PathVariable Long id);
    
    // 关联单据查询
    @GetMapping("/linked/{id}")
    public BaseResponseInfo getLinkedBills(@PathVariable Long id);
}
```

**扩展方式**:
- 添加新的单据类型
- 自定义审核流程
- 扩展打印模板
- 添加单据状态控制

#### 2.3.3 财务管理控制器
**文件位置**: `com.jsh.erp.controller.AccountHeadController`

**功能描述**: 处理财务相关的HTTP请求

**主要接口**:
```java
@RestController
@RequestMapping("/accountHead")
public class AccountHeadController extends BaseController<AccountHead> {
    
    // 财务单据列表
    @GetMapping("/list")
    public BaseResponseInfo getList(HttpServletRequest request);
    
    // 应收应付统计
    @GetMapping("/statistics")
    public BaseResponseInfo getStatistics(HttpServletRequest request);
    
    // 对账功能
    @PostMapping("/reconcile")
    public BaseResponseInfo reconcile(@RequestBody ReconcileRequest request);
}
```

**扩展方式**:
- 添加新的财务单据类型
- 自定义财务统计规则
- 扩展对账功能
- 添加财务审批流程

### 2.4 业务服务模块

#### 2.4.1 商品服务模块
**文件位置**: `com.jsh.erp.service.MaterialService`

**功能描述**: 商品业务逻辑处理

**核心方法**:
```java
@Service
public class MaterialService {
    
    // 商品查询
    public List<Material> getList(Map<String, Object> parameterMap);
    
    // 商品保存
    public void saveMaterial(Material material);
    
    // 商品删除
    public void deleteMaterial(Long id);
    
    // 库存更新
    public void updateStock(Long materialId, BigDecimal quantity);
    
    // 价格历史
    public List<MaterialPrice> getPriceHistory(Long materialId);
}
```

**扩展方式**:
- 添加新的业务逻辑方法
- 自定义商品编码规则
- 扩展商品属性处理
- 添加商品业务校验

#### 2.4.2 单据服务模块
**文件位置**: `com.jsh.erp.service.DepotHeadService`

**功能描述**: 单据业务逻辑处理

**核心方法**:
```java
@Service
public class DepotHeadService {
    
    // 单据保存
    @Transactional
    public void saveDepotHead(DepotHead depotHead, List<DepotItem> items);
    
    // 单据审核
    @Transactional
    public void approveDepotHead(Long id, String status);
    
    // 库存更新
    @Transactional
    public void updateStock(DepotHead depotHead);
    
    // 成本核算
    public void calculateCost(DepotHead depotHead);
    
    // 财务集成
    @Transactional
    public void generateFinancialRecord(DepotHead depotHead);
}
```

**扩展方式**:
- 添加新的单据处理逻辑
- 自定义审核流程
- 扩展成本核算算法
- 添加第三方系统集成

#### 2.4.3 库存服务模块
**文件位置**: `com.jsh.erp.service.MaterialCurrentStockService`

**功能描述**: 库存管理业务逻辑

**核心方法**:
```java
@Service
public class MaterialCurrentStockService {
    
    // 库存查询
    public List<MaterialCurrentStock> getCurrentStock(Map<String, Object> params);
    
    // 库存更新
    @Transactional
    public void updateStock(Long materialId, Long depotId, BigDecimal quantity);
    
    // 库存预警
    public List<Material> getStockWarning();
    
    // 库存盘点
    @Transactional
    public void stockTaking(List<StockTakingItem> items);
}
```

**扩展方式**:
- 添加库存预警规则
- 自定义库存盘点流程
- 扩展库存统计功能
- 添加库存优化算法

### 2.5 数据访问模块

#### 2.5.1 MyBatis Mapper模块
**文件位置**: `com.jsh.erp.datasource.mappers.*`

**功能描述**: 数据库访问层接口定义

**设计模式**:
- 基础Mapper：MyBatis Generator生成的基础CRUD
- 扩展Mapper：自定义业务查询接口

**示例**:
```java
// 基础Mapper
public interface MaterialMapper {
    int deleteByPrimaryKey(Long id);
    int insert(Material record);
    Material selectByPrimaryKey(Long id);
    int updateByPrimaryKey(Material record);
}

// 扩展Mapper
public interface MaterialMapperEx extends MaterialMapper {
    List<Material> getList(@Param("parameterMap") Map<String, Object> parameterMap);
    List<MaterialVo4Unit> getMaterialAndUnitList(@Param("parameterMap") Map<String, Object> parameterMap);
    void batchDeleteMaterialByIds(@Param("ids") String[] ids);
}
```

**扩展方式**:
- 在Ex Mapper中添加自定义查询方法
- 在对应的XML文件中实现SQL
- 添加复杂的关联查询
- 实现批量操作方法

#### 2.5.2 实体类模块
**文件位置**: `com.jsh.erp.datasource.entities.*`

**功能描述**: 数据库表对应的实体类

**设计规范**:
- 基础实体类：与数据库表一对一对应
- 扩展实体类：包含关联信息的VO类
- 业务实体类：包含业务逻辑的DTO类

**示例**:
```java
// 基础实体类
public class Material {
    private Long id;
    private String name;
    private String model;
    private String standard;
    // getter/setter...
}

// 扩展实体类
public class MaterialEx extends Material {
    private String categoryName;
    private String unitName;
    private BigDecimal currentStock;
    // getter/setter...
}

// VO类
public class MaterialVo4Unit {
    private Long materialId;
    private String materialName;
    private String unitName;
    private String basicUnit;
    // getter/setter...
}
```

**扩展方式**:
- 添加新的字段和属性
- 创建新的VO类
- 添加业务验证注解
- 实现序列化接口

### 2.6 工具模块

#### 2.6.1 工具类模块
**文件位置**: `com.jsh.erp.utils.*`

**功能描述**: 提供系统通用的工具方法

**主要工具类**:

##### Tools.java
```java
public class Tools {
    // 日期处理
    public static String getCurrentDate();
    public static String formatDate(Date date, String pattern);
    
    // 字符串处理
    public static boolean isEmpty(String str);
    public static String generateCode(String prefix);
    
    // 数值处理
    public static BigDecimal add(BigDecimal a, BigDecimal b);
    public static BigDecimal subtract(BigDecimal a, BigDecimal b);
}
```

##### ExcelUtils.java
```java
public class ExcelUtils {
    // Excel导入
    public static List<Map<String, Object>> importExcel(MultipartFile file);
    
    // Excel导出
    public static void exportExcel(List<Map<String, Object>> data, String fileName);
    
    // 模板下载
    public static void downloadTemplate(String templateName);
}
```

##### JsonUtils.java
```java
public class JsonUtils {
    // JSON转换
    public static String objectToJson(Object obj);
    public static <T> T jsonToObject(String json, Class<T> clazz);
    
    // 集合转换
    public static <T> List<T> jsonToList(String json, Class<T> clazz);
}
```

**扩展方式**:
- 添加新的工具方法
- 创建专用的工具类
- 扩展现有工具类功能
- 添加第三方工具库集成

#### 2.6.2 异常处理模块
**文件位置**: `com.jsh.erp.exception.*`

**功能描述**: 统一的异常处理机制

**异常类设计**:
```java
// 业务异常
public class BusinessRunTimeException extends RuntimeException {
    private String code;
    private String message;
}

// 参数检查异常
public class BusinessParamCheckingException extends Exception {
    private String message;
}

// 全局异常处理器
@ControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(BusinessRunTimeException.class)
    public BaseResponseInfo handleBusinessException(BusinessRunTimeException e) {
        return BaseResponseInfo.error(e.getCode(), e.getMessage());
    }
    
    @ExceptionHandler(Exception.class)
    public BaseResponseInfo handleException(Exception e) {
        return BaseResponseInfo.error("500", "系统内部错误");
    }
}
```

**扩展方式**:
- 添加新的异常类型
- 自定义异常处理逻辑
- 添加异常日志记录
- 集成监控系统

## 3. 前端模块架构

### 3.1 核心框架模块

#### 3.1.1 应用入口模块 (main.js)
**文件位置**: `src/main.js`

**功能描述**: Vue应用程序的入口文件，负责应用初始化

**关键配置**:
```javascript
import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import Antd from 'ant-design-vue'

Vue.use(Antd)
Vue.config.productionTip = false

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
```

**扩展方式**:
- 添加全局插件
- 配置全局指令
- 设置全局过滤器
- 添加全局混入

#### 3.1.2 路由配置模块
**文件位置**: `src/router/index.js`

**功能描述**: 配置应用的路由规则

**路由结构**:
```javascript
const routes = [
  {
    path: '/user',
    component: UserLayout,
    children: [
      { path: '/user/login', component: Login }
    ]
  },
  {
    path: '/',
    component: GlobalLayout,
    children: [
      { path: '/dashboard', component: Dashboard },
      { path: '/material', component: MaterialList },
      // 其他业务路由...
    ]
  }
]
```

**扩展方式**:
- 添加新的路由规则
- 配置路由守卫
- 实现动态路由
- 添加路由缓存

#### 3.1.3 状态管理模块
**文件位置**: `src/store/index.js`

**功能描述**: Vuex状态管理配置

**Store结构**:
```javascript
export default new Vuex.Store({
  modules: {
    app,        // 应用状态
    user,       // 用户状态
    permission, // 权限状态
    enhance     // 增强功能状态
  },
  getters
})
```

**扩展方式**:
- 添加新的状态模块
- 扩展现有状态
- 添加状态持久化
- 实现状态同步

### 3.2 布局组件模块

#### 3.2.1 全局布局组件
**文件位置**: `src/components/layouts/GlobalLayout.vue`

**功能描述**: 系统主要页面的布局容器

**组件结构**:
```vue
<template>
  <a-layout class="layout">
    <a-layout-sider>
      <side-menu />
    </a-layout-sider>
    <a-layout>
      <a-layout-header>
        <global-header />
      </a-layout-header>
      <a-layout-content>
        <router-view />
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>
```

**扩展方式**:
- 自定义布局样式
- 添加新的布局区域
- 实现响应式布局
- 添加主题切换

#### 3.2.2 标签页布局组件
**文件位置**: `src/components/layouts/TabLayout.vue`

**功能描述**: 多标签页功能的布局组件

**核心功能**:
- 标签页创建和关闭
- 标签页缓存管理
- 标签页右键菜单
- 标签页拖拽排序

**扩展方式**:
- 自定义标签页样式
- 添加标签页持久化
- 扩展右键菜单功能
- 实现标签页分组

### 3.3 业务组件模块

#### 3.3.1 商品选择组件
**文件位置**: `src/components/jeecgbiz/modal/JSelectMaterialModal.vue`

**功能描述**: 通用的商品选择弹窗组件

**组件接口**:
```vue
<template>
  <a-modal 
    v-model="visible" 
    title="选择商品"
    @ok="handleOk">
    <material-table 
      :columns="columns"
      :data-source="dataSource"
      @select="handleSelect" />
  </a-modal>
</template>

<script>
export default {
  props: {
    multiple: Boolean,  // 是否多选
    filters: Object,    // 过滤条件
    excludeIds: Array   // 排除的商品ID
  },
  methods: {
    handleSelect(selectedRows) {
      this.$emit('select', selectedRows)
    }
  }
}
</script>
```

**扩展方式**:
- 添加新的过滤条件
- 自定义显示列
- 扩展选择逻辑
- 添加商品预览功能

#### 3.3.2 可编辑表格组件
**文件位置**: `src/components/jeecg/JEditableTable.vue`

**功能描述**: 支持行内编辑的表格组件

**组件特性**:
- 行内编辑功能
- 数据验证
- 动态列配置
- 批量操作

**使用示例**:
```vue
<j-editable-table
  :columns="columns"
  :data-source="dataSource"
  :editable="true"
  @change="handleTableChange">
  
  <template slot="operation" slot-scope="text, record, index">
    <a @click="deleteRow(index)">删除</a>
  </template>
</j-editable-table>
```

**扩展方式**:
- 添加新的编辑器类型
- 自定义验证规则
- 扩展操作按钮
- 添加数据导入导出

#### 3.3.3 单据表单组件
**文件位置**: `src/views/bill/modules/*Modal.vue`

**功能描述**: 各种业务单据的录入表单

**组件结构**:
```vue
<template>
  <a-modal v-model="visible" title="采购入库单">
    <a-form :form="form">
      <!-- 单据头信息 -->
      <a-row>
        <a-col :span="8">
          <a-form-item label="单据编号">
            <a-input v-decorator="['billNo']" />
          </a-form-item>
        </a-col>
        <!-- 其他表单项... -->
      </a-row>
      
      <!-- 单据明细 -->
      <j-editable-table
        :columns="itemColumns"
        :data-source="itemList"
        @change="handleItemChange" />
      
      <!-- 单据汇总 -->
      <div class="bill-summary">
        <span>合计金额：{{ totalAmount }}</span>
      </div>
    </a-form>
  </a-modal>
</template>
```

**扩展方式**:
- 添加新的单据类型
- 自定义表单验证
- 扩展单据明细功能
- 添加单据模板功能

### 3.4 工具模块

#### 3.4.1 HTTP请求模块
**文件位置**: `src/utils/request.js`

**功能描述**: 封装HTTP请求方法

**核心方法**:
```javascript
import axios from 'axios'

// 基础请求方法
export function getAction(url, params) {
  return axios.get(url, { params })
}

export function postAction(url, data) {
  return axios.post(url, data)
}

export function putAction(url, data) {
  return axios.put(url, data)
}

export function deleteAction(url, params) {
  return axios.delete(url, { params })
}

// 文件下载
export function downFile(url, params) {
  return axios.get(url, {
    params,
    responseType: 'blob'
  })
}
```

**扩展方式**:
- 添加新的请求方法
- 自定义请求拦截器
- 扩展错误处理
- 添加请求重试机制

#### 3.4.2 工具函数模块
**文件位置**: `src/utils/util.js`

**功能描述**: 提供通用的工具函数

**主要函数**:
```javascript
// 时间格式化
export function formatDate(date, format) {
  // 实现日期格式化
}

// 数字格式化
export function formatNumber(number, precision) {
  // 实现数字格式化
}

// 对象深拷贝
export function deepClone(obj) {
  return JSON.parse(JSON.stringify(obj))
}

// 防抖函数
export function debounce(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}
```

**扩展方式**:
- 添加新的工具函数
- 优化现有函数性能
- 添加函数单元测试
- 实现树形数据处理

### 3.5 Mixins模块

#### 3.5.1 列表页面混入
**文件位置**: `src/mixins/JeecgListMixin.js`

**功能描述**: 为列表页面提供通用的逻辑

**核心功能**:
```javascript
export default {
  data() {
    return {
      // 表格数据
      dataSource: [],
      // 分页信息
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0
      },
      // 加载状态
      loading: false,
      // 选中行
      selectedRowKeys: []
    }
  },
  
  methods: {
    // 加载数据
    loadData(params) {
      this.loading = true
      this.getList(params).then(res => {
        this.dataSource = res.result.records
        this.pagination.total = res.result.total
      }).finally(() => {
        this.loading = false
      })
    },
    
    // 分页变化
    handleTableChange(pagination) {
      this.pagination = pagination
      this.loadData()
    },
    
    // 刷新数据
    refresh() {
      this.loadData()
    }
  }
}
```

**扩展方式**:
- 添加新的通用方法
- 自定义分页逻辑
- 扩展搜索功能
- 添加导出功能

#### 3.5.2 单据混入
**文件位置**: `src/views/bill/mixins/BillListMixin.js`

**功能描述**: 为单据页面提供专用逻辑

**核心功能**:
```javascript
export default {
  mixins: [JeecgListMixin],
  
  data() {
    return {
      // 单据状态选项
      statusOptions: [
        { label: '未审核', value: '0' },
        { label: '已审核', value: '1' }
      ]
    }
  },
  
  methods: {
    // 审核单据
    approve(record) {
      this.$confirm({
        title: '确认审核？',
        onOk: () => {
          return approveDepotHead({ id: record.id }).then(() => {
            this.$message.success('审核成功')
            this.refresh()
          })
        }
      })
    },
    
    // 打印单据
    print(record) {
      const printUrl = `/depotHead/print/${record.id}`
      window.open(printUrl, '_blank')
    }
  }
}
```

**扩展方式**:
- 添加新的单据操作
- 自定义审核流程
- 扩展打印功能
- 添加单据导入导出

## 4. 数据库模块设计

### 4.1 表结构设计规范

#### 4.1.1 命名规范
- **表前缀**: 所有表使用`jsh_`前缀
- **字段命名**: 使用下划线分隔的小写字母
- **主键**: 统一使用`id`作为主键字段
- **外键**: 使用`关联表名_id`格式命名

#### 4.1.2 标准字段
所有业务表都包含以下标准字段：
```sql
-- 多租户字段
tenant_id bigint(20) DEFAULT NULL COMMENT '租户id'

-- 软删除字段
delete_flag varchar(1) DEFAULT '0' COMMENT '删除标记，0未删除，1删除'

-- 审计字段（可选）
create_time datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
update_time datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
create_by bigint(20) DEFAULT NULL COMMENT '创建人'
update_by bigint(20) DEFAULT NULL COMMENT '更新人'
```

### 4.2 核心表设计详解

#### 4.2.1 商品信息表 (jsh_material)
**表功能**: 存储商品基础信息

**关键字段设计**:
```sql
CREATE TABLE `jsh_material` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(50) DEFAULT NULL COMMENT '名称',
  `model` varchar(50) DEFAULT NULL COMMENT '型号',
  `standard` varchar(50) DEFAULT NULL COMMENT '规格',
  `color` varchar(50) DEFAULT NULL COMMENT '颜色',
  `unit_id` bigint(20) DEFAULT NULL COMMENT '计量单位Id',
  `category_id` bigint(20) DEFAULT NULL COMMENT '类别Id',
  `bar_code` varchar(50) DEFAULT NULL COMMENT '条码',
  `commodity_unit` varchar(50) DEFAULT NULL COMMENT '副单位',
  `unit_ratio` decimal(24,6) DEFAULT NULL COMMENT '换算比例',
  `purchase_decimal` decimal(24,6) DEFAULT NULL COMMENT '采购价格',
  `commodity_decimal` decimal(24,6) DEFAULT NULL COMMENT '零售价格',
  `wholesale_decimal` decimal(24,6) DEFAULT NULL COMMENT '销售价格',
  `low_decimal` decimal(24,6) DEFAULT NULL COMMENT '最低售价',
  `other_field1` varchar(50) DEFAULT NULL COMMENT '自定义1',
  `other_field2` varchar(50) DEFAULT NULL COMMENT '自定义2',
  `other_field3` varchar(50) DEFAULT NULL COMMENT '自定义3',
  `enable_serial_number` varchar(1) DEFAULT '0' COMMENT '是否开启序列号',
  `enable_batch_number` varchar(1) DEFAULT '0' COMMENT '是否开启批号',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户id',
  `delete_flag` varchar(1) DEFAULT '0' COMMENT '删除标记',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='商品表';
```

**扩展方式**:
- 添加新的商品属性字段
- 扩展价格体系（增加会员价等）
- 添加商品图片关联表
- 实现商品多语言支持

#### 4.2.2 单据主表 (jsh_depot_head)
**表功能**: 存储各类业务单据的主要信息

**关键字段设计**:
```sql
CREATE TABLE `jsh_depot_head` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `type` varchar(50) DEFAULT NULL COMMENT '类型(出库/入库)',
  `sub_type` varchar(50) DEFAULT NULL COMMENT '子类型',
  `default_number` varchar(50) DEFAULT NULL COMMENT '初始票据号',
  `number` varchar(50) DEFAULT NULL COMMENT '票据号',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `operate_time` datetime DEFAULT NULL COMMENT '出入库时间',
  `organ_id` bigint(20) DEFAULT NULL COMMENT '供应商Id',
  `creator` bigint(20) DEFAULT NULL COMMENT '操作员',
  `account_id` bigint(20) DEFAULT NULL COMMENT '账户Id',
  `change_amount` decimal(24,6) DEFAULT NULL COMMENT '变动金额(收款/付款)',
  `back_amount` decimal(24,6) DEFAULT NULL COMMENT '找零金额',
  `total_price` decimal(24,6) DEFAULT NULL COMMENT '合计金额',
  `pay_type` varchar(50) DEFAULT NULL COMMENT '付款类型(现金/转账)',
  `remark` varchar(1000) DEFAULT NULL COMMENT '备注',
  `file_name` varchar(500) DEFAULT NULL COMMENT '附件名称',
  `status` varchar(1) DEFAULT '0' COMMENT '状态，0未审核、1已审核',
  `purchase_status` varchar(1) DEFAULT '0' COMMENT '采购状态，0未采购、2已采购完、3部分采购',
  `source` varchar(1) DEFAULT '0' COMMENT '单据来源，0-pc，1-手机',
  `link_number` varchar(50) DEFAULT NULL COMMENT '关联号',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户id',
  `delete_flag` varchar(1) DEFAULT '0' COMMENT '删除标记',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='单据主表';
```

**扩展方式**:
- 添加新的单据类型
- 扩展审核流程字段
- 添加单据模板支持
- 实现单据版本控制

#### 4.2.3 财务主表 (jsh_account_head)
**表功能**: 存储财务相关的单据信息

**扩展方式**:
- 添加多货币支持
- 扩展会计科目关联
- 添加预算控制字段
- 实现财务审批流程

### 4.3 索引设计

#### 4.3.1 性能优化索引
```sql
-- 常用查询字段索引
ALTER TABLE jsh_material ADD INDEX idx_tenant_delete (tenant_id, delete_flag);
ALTER TABLE jsh_material ADD INDEX idx_category (category_id);
ALTER TABLE jsh_material ADD INDEX idx_barcode (bar_code);

-- 单据查询索引
ALTER TABLE jsh_depot_head ADD INDEX idx_tenant_delete_type (tenant_id, delete_flag, type);
ALTER TABLE jsh_depot_head ADD INDEX idx_operate_time (operate_time);
ALTER TABLE jsh_depot_head ADD INDEX idx_number (number);

-- 明细关联索引
ALTER TABLE jsh_depot_item ADD INDEX idx_header_id (header_id);
ALTER TABLE jsh_depot_item ADD INDEX idx_material_id (material_id);
```

#### 4.3.2 唯一性约束
```sql
-- 商品条码唯一性
ALTER TABLE jsh_material ADD UNIQUE KEY uk_barcode_tenant (bar_code, tenant_id);

-- 单据编号唯一性  
ALTER TABLE jsh_depot_head ADD UNIQUE KEY uk_number_tenant (number, tenant_id);
```

### 4.4 数据库扩展方案

#### 4.4.1 自定义字段方案
```sql
-- 方案1：预留字段
ALTER TABLE jsh_material ADD COLUMN custom_field1 VARCHAR(100) COMMENT '自定义字段1';
ALTER TABLE jsh_material ADD COLUMN custom_field2 VARCHAR(100) COMMENT '自定义字段2';

-- 方案2：扩展属性表
CREATE TABLE jsh_material_extend_attr (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  material_id bigint(20) NOT NULL,
  attr_name varchar(50) NOT NULL,
  attr_value varchar(500),
  tenant_id bigint(20),
  PRIMARY KEY (id),
  KEY idx_material_id (material_id)
);
```

#### 4.4.2 多语言支持方案
```sql
-- 多语言资源表
CREATE TABLE jsh_i18n_resource (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  resource_key varchar(100) NOT NULL,
  language_code varchar(10) NOT NULL,
  resource_value varchar(500),
  tenant_id bigint(20),
  PRIMARY KEY (id),
  UNIQUE KEY uk_key_lang_tenant (resource_key, language_code, tenant_id)
);
```

## 5. 集成和扩展指南

### 5.1 新增业务模块

#### 5.1.1 后端模块开发步骤
1. **创建实体类**
```java
// 1. 创建基础实体类
@Entity
@Table(name = "jsh_new_module")
public class NewModule {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    private String name;
    private String code;
    private Long tenantId;
    private String deleteFlag;
    
    // getter/setter...
}

// 2. 创建扩展实体类
public class NewModuleEx extends NewModule {
    private String extraField;
    // getter/setter...
}
```

2. **创建Mapper接口**
```java
// 基础Mapper
public interface NewModuleMapper {
    int insert(NewModule record);
    NewModule selectByPrimaryKey(Long id);
    int updateByPrimaryKey(NewModule record);
    int deleteByPrimaryKey(Long id);
}

// 扩展Mapper
public interface NewModuleMapperEx extends NewModuleMapper {
    List<NewModuleEx> getList(@Param("parameterMap") Map<String, Object> parameterMap);
    void batchDelete(@Param("ids") String[] ids);
}
```

3. **实现XML映射**
```xml
<!-- NewModuleMapperEx.xml -->
<mapper namespace="com.jsh.erp.datasource.mappers.NewModuleMapperEx">
    <select id="getList" resultType="com.jsh.erp.datasource.entities.NewModuleEx">
        SELECT * FROM jsh_new_module 
        WHERE tenant_id = #{parameterMap.tenantId}
        AND delete_flag = '0'
        <if test="parameterMap.name != null and parameterMap.name != ''">
            AND name LIKE CONCAT('%', #{parameterMap.name}, '%')
        </if>
    </select>
</mapper>
```

4. **创建Service服务**
```java
@Service
public class NewModuleService {
    
    @Autowired
    private NewModuleMapperEx newModuleMapper;
    
    public List<NewModuleEx> getList(Map<String, Object> parameterMap) {
        return newModuleMapper.getList(parameterMap);
    }
    
    @Transactional
    public void save(NewModule newModule) {
        if (newModule.getId() == null) {
            newModuleMapper.insert(newModule);
        } else {
            newModuleMapper.updateByPrimaryKey(newModule);
        }
    }
}
```

5. **创建Controller控制器**
```java
@RestController
@RequestMapping("/newModule")
public class NewModuleController extends BaseController<NewModule> {
    
    @Autowired
    private NewModuleService newModuleService;
    
    @GetMapping("/list")
    public BaseResponseInfo getList(HttpServletRequest request) {
        Map<String, Object> parameterMap = getParameterMap(request);
        List<NewModuleEx> list = newModuleService.getList(parameterMap);
        return getSuccessResult(list);
    }
    
    @PostMapping("/add")
    public BaseResponseInfo add(@RequestBody NewModule newModule) {
        newModuleService.save(newModule);
        return getSuccessResult("添加成功");
    }
}
```

#### 5.1.2 前端模块开发步骤

1. **创建API接口**
```javascript
// api/newModule.js
import { getAction, postAction, putAction, deleteAction } from '@/utils/request'

// 获取列表
export const getNewModuleList = (params) => getAction('/newModule/list', params)

// 添加
export const addNewModule = (data) => postAction('/newModule/add', data)

// 更新
export const updateNewModule = (data) => putAction('/newModule/edit', data)

// 删除
export const deleteNewModule = (params) => deleteAction('/newModule/delete', params)
```

2. **创建列表页面**
```vue
<!-- views/newModule/NewModuleList.vue -->
<template>
  <div>
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="24">
          <a-col :md="6" :sm="24">
            <a-form-item label="名称">
              <a-input v-model="queryParam.name" placeholder="请输入名称" />
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="24">
            <span class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery">查询</a-button>
              <a-button @click="searchReset">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    
    <!-- 操作按钮 -->
    <div class="table-operator">
      <a-button type="primary" icon="plus" @click="handleAdd">新增</a-button>
      <a-button type="danger" icon="delete" @click="batchDelete">删除</a-button>
    </div>
    
    <!-- 数据表格 -->
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :pagination="pagination"
      :loading="loading"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="handleTableChange">
      
      <template slot="action" slot-scope="text, record">
        <a @click="handleEdit(record)">编辑</a>
        <a-divider type="vertical" />
        <a @click="handleDelete(record)">删除</a>
      </template>
    </a-table>
    
    <!-- 新增/编辑弹窗 -->
    <new-module-modal 
      ref="modalForm" 
      @ok="handleOk" />
  </div>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { getNewModuleList, deleteNewModule } from '@/api/newModule'
import NewModuleModal from './modules/NewModuleModal'

export default {
  name: 'NewModuleList',
  mixins: [JeecgListMixin],
  components: { NewModuleModal },
  
  data() {
    return {
      // 表格列定义
      columns: [
        { title: '名称', dataIndex: 'name' },
        { title: '编码', dataIndex: 'code' },
        { title: '操作', scopedSlots: { customRender: 'action' } }
      ]
    }
  },
  
  methods: {
    // 获取数据
    getList(params) {
      return getNewModuleList(params)
    },
    
    // 删除
    handleDelete(record) {
      this.$confirm({
        title: '确认删除?',
        onOk: () => {
          return deleteNewModule({ ids: record.id }).then(() => {
            this.$message.success('删除成功')
            this.refresh()
          })
        }
      })
    }
  }
}
</script>
```

3. **创建表单弹窗**
```vue
<!-- views/newModule/modules/NewModuleModal.vue -->
<template>
  <a-modal
    v-model="visible"
    :title="title"
    @ok="handleOk"
    @cancel="handleCancel">
    
    <a-form :form="form">
      <a-form-item label="名称" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-input v-decorator="['name', validatorRules.name]" placeholder="请输入名称" />
      </a-form-item>
      
      <a-form-item label="编码" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-input v-decorator="['code', validatorRules.code]" placeholder="请输入编码" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { addNewModule, updateNewModule } from '@/api/newModule'

export default {
  name: 'NewModuleModal',
  
  data() {
    return {
      visible: false,
      form: this.$form.createForm(this),
      title: '',
      isEdit: false,
      model: {},
      
      // 验证规则
      validatorRules: {
        name: { rules: [{ required: true, message: '请输入名称' }] },
        code: { rules: [{ required: true, message: '请输入编码' }] }
      }
    }
  },
  
  methods: {
    // 新增
    add() {
      this.visible = true
      this.title = '新增'
      this.isEdit = false
      this.form.resetFields()
    },
    
    // 编辑
    edit(record) {
      this.visible = true
      this.title = '编辑'
      this.isEdit = true
      this.model = record
      this.$nextTick(() => {
        this.form.setFieldsValue(record)
      })
    },
    
    // 确认
    handleOk() {
      this.form.validateFields((err, values) => {
        if (!err) {
          const formData = { ...values }
          if (this.isEdit) {
            formData.id = this.model.id
          }
          
          const apiMethod = this.isEdit ? updateNewModule : addNewModule
          apiMethod(formData).then(() => {
            this.$message.success('操作成功')
            this.visible = false
            this.$emit('ok')
          })
        }
      })
    }
  }
}
</script>
```

### 5.2 扩展现有模块

#### 5.2.1 添加新字段
1. **数据库扩展**
```sql
-- 添加新字段
ALTER TABLE jsh_material ADD COLUMN new_field VARCHAR(100) COMMENT '新字段';

-- 创建索引
ALTER TABLE jsh_material ADD INDEX idx_new_field (new_field);
```

2. **实体类扩展**
```java
// 在Material类中添加新字段
public class Material {
    // 现有字段...
    
    private String newField;
    
    public String getNewField() {
        return newField;
    }
    
    public void setNewField(String newField) {
        this.newField = newField;
    }
}
```

3. **前端表单扩展**
```vue
<!-- 在MaterialModal.vue中添加新字段 -->
<a-form-item label="新字段" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
  <a-input v-decorator="['newField']" placeholder="请输入新字段" />
</a-form-item>
```

#### 5.2.2 添加新功能
1. **后端功能扩展**
```java
// 在MaterialService中添加新方法
@Service
public class MaterialService {
    
    // 现有方法...
    
    // 新增功能：批量更新价格
    @Transactional
    public void batchUpdatePrice(List<MaterialPriceUpdate> updates) {
        for (MaterialPriceUpdate update : updates) {
            Material material = materialMapper.selectByPrimaryKey(update.getMaterialId());
            if (material != null) {
                material.setPurchaseDecimal(update.getPurchasePrice());
                material.setCommodityDecimal(update.getRetailPrice());
                materialMapper.updateByPrimaryKey(material);
            }
        }
    }
}
```

2. **前端功能扩展**
```vue
<!-- 在MaterialList.vue中添加新按钮 -->
<div class="table-operator">
  <a-button type="primary" icon="plus" @click="handleAdd">新增</a-button>
  <a-button icon="edit" @click="batchUpdatePrice">批量改价</a-button>
</div>
```

### 5.3 第三方系统集成

#### 5.3.1 API集成
```java
// 创建第三方API服务
@Service
public class ThirdPartyApiService {
    
    @Autowired
    private RestTemplate restTemplate;
    
    // 同步商品信息到第三方系统
    public void syncMaterialToThirdParty(Material material) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("name", material.getName());
            requestBody.put("code", material.getBarCode());
            requestBody.put("price", material.getCommodityDecimal());
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            
            ResponseEntity<String> response = restTemplate.postForEntity(
                "http://third-party-api/materials", 
                request, 
                String.class
            );
            
            if (response.getStatusCode() == HttpStatus.OK) {
                // 同步成功处理
                logger.info("商品同步成功: {}", material.getName());
            }
        } catch (Exception e) {
            logger.error("商品同步失败: {}", e.getMessage(), e);
        }
    }
}
```

#### 5.3.2 消息队列集成
```java
// 使用消息队列进行异步处理
@Component
public class MaterialEventPublisher {
    
    @Autowired
    private RabbitTemplate rabbitTemplate;
    
    // 发布商品变更事件
    public void publishMaterialChangeEvent(Material material, String operation) {
        MaterialChangeEvent event = new MaterialChangeEvent();
        event.setMaterialId(material.getId());
        event.setOperation(operation);
        event.setTimestamp(System.currentTimeMillis());
        event.setData(material);
        
        rabbitTemplate.convertAndSend(
            "material.exchange", 
            "material.change", 
            event
        );
    }
}

// 监听商品变更事件
@RabbitListener(queues = "material.change.queue")
@Component
public class MaterialChangeEventListener {
    
    @Autowired
    private ThirdPartyApiService thirdPartyApiService;
    
    public void handleMaterialChange(MaterialChangeEvent event) {
        try {
            if ("CREATE".equals(event.getOperation()) || "UPDATE".equals(event.getOperation())) {
                thirdPartyApiService.syncMaterialToThirdParty(event.getData());
            }
        } catch (Exception e) {
            logger.error("处理商品变更事件失败: {}", e.getMessage(), e);
        }
    }
}
```

## 6. 性能优化和最佳实践

### 6.1 数据库优化

#### 6.1.1 查询优化
```sql
-- 优化前：查询商品库存
SELECT m.*, mcs.current_number 
FROM jsh_material m 
LEFT JOIN jsh_material_current_stock mcs ON m.id = mcs.material_id 
WHERE m.tenant_id = ? AND m.delete_flag = '0'

-- 优化后：添加索引和分页
SELECT m.*, mcs.current_number 
FROM jsh_material m 
LEFT JOIN jsh_material_current_stock mcs ON m.id = mcs.material_id 
WHERE m.tenant_id = ? AND m.delete_flag = '0'
ORDER BY m.id 
LIMIT ?, ?

-- 创建复合索引
CREATE INDEX idx_material_tenant_delete ON jsh_material(tenant_id, delete_flag, id);
```

#### 6.1.2 批量操作优化
```java
// 优化前：逐条插入
for (DepotItem item : items) {
    depotItemMapper.insert(item);
}

// 优化后：批量插入
@Override
public void batchInsertDepotItem(List<DepotItem> items) {
    if (items != null && !items.isEmpty()) {
        // 分批处理，每批1000条
        int batchSize = 1000;
        for (int i = 0; i < items.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, items.size());
            List<DepotItem> batch = items.subList(i, endIndex);
            depotItemMapper.batchInsert(batch);
        }
    }
}
```

### 6.2 缓存策略

#### 6.2.1 Redis缓存应用
```java
@Service
public class MaterialService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    // 缓存商品信息
    public Material getMaterialById(Long id) {
        String cacheKey = "material:" + id;
        Material material = (Material) redisTemplate.opsForValue().get(cacheKey);
        
        if (material == null) {
            material = materialMapper.selectByPrimaryKey(id);
            if (material != null) {
                redisTemplate.opsForValue().set(cacheKey, material, 30, TimeUnit.MINUTES);
            }
        }
        
        return material;
    }
    
    // 清理缓存
    @CacheEvict(value = "material", key = "#material.id")
    public void updateMaterial(Material material) {
        materialMapper.updateByPrimaryKey(material);
    }
}
```

### 6.3 前端性能优化

#### 6.3.1 组件懒加载
```javascript
// 路由懒加载
const routes = [
  {
    path: '/material',
    component: () => import('@/views/material/MaterialList.vue')
  }
]

// 组件按需加载
export default {
  components: {
    MaterialModal: () => import('./modules/MaterialModal.vue')
  }
}
```

#### 6.3.2 表格虚拟滚动
```vue
<!-- 大数据量表格优化 -->
<a-table
  :columns="columns"
  :data-source="dataSource"
  :scroll="{ y: 400 }"
  :pagination="false"
  :virtual="true">
</a-table>
```

本模块说明文档为jshERP系统的二次开发提供了详细的技术指导，涵盖了后端、前端、数据库各个层面的模块设计和扩展方案。通过遵循文档中的设计规范和最佳实践，可以高效地进行系统扩展和定制开发。