# jshERP 备份系统快速使用指南

## 🚀 快速开始

### 1. 执行完整备份
```bash
# 确保脚本有执行权限
chmod +x full_backup.sh

# 执行备份
./full_backup.sh

# 查看备份结果
ls -lh /backup/jshERP/
```

### 2. 验证备份完整性
```bash
# 验证最新备份
./backup_verify.sh /backup/jshERP/jshERP_complete_backup_*.tar.gz

# 查看验证报告
cat /backup/jshERP/*/verification_report.txt
```

### 3. 恢复系统
```bash
# 完整恢复到新环境
./full_restore.sh /backup/jshERP/jshERP_complete_backup_20250613_143022.tar.gz

# 恢复到指定目录
./full_restore.sh /backup/jshERP/jshERP_complete_backup_20250613_143022.tar.gz /opt/jshERP
```

## 📋 备份文件说明

### 备份内容清单
- ✅ **源代码**：完整项目代码 + 配置修改
- ✅ **数据库**：MySQL 完整数据 + 表结构
- ✅ **Docker 镜像**：所有相关容器镜像
- ✅ **Redis 数据**：缓存数据 + 配置信息
- ✅ **文档脚本**：部署文档 + 维护脚本

### 文件大小估算
| 组件 | 大小范围 | 说明 |
|------|----------|------|
| 完整备份包 | 1.1-1.6GB | 压缩后的最终文件 |
| 源代码 | 50-100MB | 项目源码压缩包 |
| 数据库 | 5-25MB | 根据业务数据量 |
| Docker 镜像 | 1-1.5GB | 占用最大空间 |
| Redis 数据 | 1-5MB | 缓存数据 |

## ⚙️ 自动化配置

### 设置定时备份
```bash
# 编辑 crontab
crontab -e

# 添加每日凌晨 2 点备份
0 2 * * * /path/to/full_backup.sh >> /var/log/jshERP_backup.log 2>&1

# 添加每周验证
0 3 * * 1 /path/to/backup_verify.sh /backup/jshERP/jshERP_complete_backup_*.tar.gz
```

### 监控脚本
```bash
# 检查备份状态
#!/bin/bash
LATEST_BACKUP=$(ls -t /backup/jshERP/jshERP_complete_backup_*.tar.gz | head -1)
if [ -z "$LATEST_BACKUP" ]; then
    echo "警告：未找到备份文件"
    exit 1
fi

# 检查备份时间（24小时内）
if [ $(find /backup/jshERP -name "*.tar.gz" -mtime -1 | wc -l) -eq 0 ]; then
    echo "警告：24小时内无新备份"
    exit 1
fi

echo "备份状态正常"
```

## 🔧 故障处理

### 常见问题解决

#### 1. 备份空间不足
```bash
# 检查磁盘空间
df -h /backup

# 清理旧备份（保留最近7天）
find /backup/jshERP -name "*.tar.gz" -mtime +7 -delete

# 压缩旧备份
find /backup/jshERP -name "20*" -type d -mtime +3 -exec tar -czf {}.tar.gz {} \; -exec rm -rf {} \;
```

#### 2. 备份验证失败
```bash
# 查看详细错误
./backup_verify.sh /path/to/backup.tar.gz

# 检查校验和
cd /backup/jshERP/20250613_143022
md5sum -c backup_checksums.md5

# 重新生成校验和
find . -type f -not -name "*.md5" -exec md5sum {} \; > backup_checksums.md5
```

#### 3. 恢复失败
```bash
# 检查 Docker 环境
docker --version
docker-compose --version

# 检查端口占用
netstat -tulpn | grep -E ':(3000|9999|3306|6379|80)\s'

# 清理现有容器
docker-compose -f docker-compose.dev.yml down -v
docker system prune -f
```

### 紧急恢复流程

#### 完全系统故障
```bash
# 1. 准备新环境
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# 2. 下载备份
# 从云存储或其他位置获取最新备份

# 3. 执行恢复
./full_restore.sh backup_file.tar.gz /opt/jshERP

# 4. 验证系统
curl http://localhost:3000
```

#### 仅数据库故障
```bash
# 1. 停止应用
docker-compose stop jsherp-backend-dev

# 2. 恢复数据库
docker exec -i jsherp-mysql-dev mysql -u root -p123456 jsh_erp < backup/database/jsh_erp_full_*.sql

# 3. 重启应用
docker-compose start jsherp-backend-dev
```

## 📊 备份监控

### 健康检查
```bash
# 每日健康检查脚本
#!/bin/bash
echo "=== jshERP 备份健康检查 ==="

# 1. 检查最新备份
LATEST=$(ls -t /backup/jshERP/*.tar.gz 2>/dev/null | head -1)
if [ -n "$LATEST" ]; then
    echo "✅ 最新备份: $(basename "$LATEST")"
    echo "   大小: $(du -h "$LATEST" | cut -f1)"
    echo "   时间: $(stat -f%Sm "$LATEST" 2>/dev/null || stat -c%y "$LATEST")"
else
    echo "❌ 未找到备份文件"
fi

# 2. 检查磁盘空间
USAGE=$(df /backup | awk 'NR==2 {print $5}' | sed 's/%//')
if [ "$USAGE" -lt 80 ]; then
    echo "✅ 磁盘空间充足: ${USAGE}%"
else
    echo "⚠️  磁盘空间不足: ${USAGE}%"
fi

# 3. 检查备份频率
RECENT=$(find /backup/jshERP -name "*.tar.gz" -mtime -1 | wc -l)
if [ "$RECENT" -gt 0 ]; then
    echo "✅ 24小时内有新备份"
else
    echo "⚠️  24小时内无新备份"
fi
```

### 告警配置
```bash
# 邮件告警示例
send_alert() {
    local subject="$1"
    local message="$2"
    echo "$message" | mail -s "jshERP 备份告警: $subject" <EMAIL>
}

# 企业微信告警示例
send_wechat_alert() {
    local message="$1"
    curl -X POST "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_KEY" \
         -H "Content-Type: application/json" \
         -d "{\"msgtype\":\"text\",\"text\":{\"content\":\"$message\"}}"
}
```

## 🌐 云端备份

### 上传到阿里云 OSS
```bash
# 安装 ossutil
wget http://gosspublic.alicdn.com/ossutil/1.7.0/ossutil64
chmod +x ossutil64

# 配置
./ossutil64 config

# 自动上传脚本
#!/bin/bash
BACKUP_FILE="/backup/jshERP/jshERP_complete_backup_$(date +%Y%m%d)*.tar.gz"
if [ -f $BACKUP_FILE ]; then
    ./ossutil64 cp $BACKUP_FILE oss://your-bucket/jshERP/$(date +%Y/%m)/
    echo "云端备份完成"
fi
```

### 上传到 AWS S3
```bash
# 安装 AWS CLI
pip install awscli

# 配置
aws configure

# 自动上传脚本
#!/bin/bash
BACKUP_FILE="/backup/jshERP/jshERP_complete_backup_$(date +%Y%m%d)*.tar.gz"
if [ -f $BACKUP_FILE ]; then
    aws s3 cp $BACKUP_FILE s3://your-bucket/jshERP/$(date +%Y/%m)/
    echo "S3 备份完成"
fi
```

## 📝 最佳实践

### 1. 备份策略
- **频率**：每日自动备份
- **保留**：本地 7 天，云端 30 天
- **验证**：每周验证备份完整性
- **测试**：每月进行恢复测试

### 2. 安全建议
- 备份文件加密存储
- 限制备份文件访问权限
- 定期更新备份脚本
- 记录备份操作日志

### 3. 性能优化
- 在业务低峰期执行备份
- 使用增量备份减少时间
- 并行处理提高效率
- 压缩备份节省空间

## 📞 技术支持

### 联系方式
- **文档问题**：查看 `jshERP备份方案.md`
- **脚本问题**：检查日志文件 `/backup/jshERP/*/backup.log`
- **恢复问题**：参考 `总结文档.md` 故障排查部分

### 常用命令
```bash
# 查看备份日志
tail -f /backup/jshERP/*/backup.log

# 检查系统状态
docker-compose -f docker-compose.dev.yml ps

# 验证数据库连接
docker exec jsherp-mysql-dev mysql -u root -p123456 -e "SELECT 1"

# 检查磁盘空间
df -h /backup
```

---

**版本**: v1.0  
**更新**: 2025年6月13日  
**适用**: jshERP 最新版本
