# jshERP 系统 Docker 化部署与问题解决完整指南

## 目录
- [系统概述](#系统概述)
- [技术架构](#技术架构)
- [环境要求](#环境要求)
- [完整部署指南](#完整部署指南)
- [问题诊断与解决方案](#问题诊断与解决方案)
- [系统访问信息](#系统访问信息)
- [功能验证清单](#功能验证清单)
- [维护与运维指南](#维护与运维指南)
- [常见问题 FAQ](#常见问题-faq)

## 系统概述

### jshERP 简介
jshERP（管伊佳ERP）是一款开源的企业资源规划系统，专为中小企业设计，提供完整的进销存管理、财务管理、报表分析等功能。

### 主要功能模块
- **零售管理**：零售出库、零售退货
- **采购管理**：请购单、采购订单、采购入库、采购退货
- **销售管理**：销售订单、销售出库、销售退货
- **仓库管理**：其它入库、其它出库、调拨出库、组装单、拆卸单
- **财务管理**：收入单、支出单、收款单、付款单、转账单、收预付款
- **报表查询**：商品库存、账户统计、采购统计、销售统计等
- **商品管理**：商品类别、商品信息、多单位、多属性
- **基础资料**：供应商、客户、会员、仓库、收支项目、结算账户
- **系统管理**：角色管理、用户管理、机构管理、日志管理等

### 适用场景
- 中小型制造企业
- 贸易公司
- 零售连锁店
- 仓储物流企业
- 需要进销存管理的各类企业

## 技术架构

### 前端技术栈
- **框架**：Vue.js 2.x + Ant Design Vue
- **构建工具**：Vue CLI + Webpack
- **开发服务器**：Node.js + Express

### 后端技术栈
- **框架**：Spring Boot 2.x
- **ORM**：MyBatis Plus
- **数据库**：MySQL 5.7+
- **缓存**：Redis 6.x
- **Java 版本**：JDK 8

### 容器化架构
- **容器引擎**：Docker + Docker Compose
- **反向代理**：Nginx
- **网络模式**：自定义 Docker 网络
- **数据持久化**：Docker Volumes

## 环境要求

### 硬件要求
- **CPU**：2核心以上
- **内存**：4GB 以上（推荐 8GB）
- **存储**：20GB 可用空间
- **网络**：稳定的互联网连接

### 软件依赖
- **操作系统**：Linux/macOS/Windows
- **Docker**：20.10+ 版本
- **Docker Compose**：2.0+ 版本
- **Python 3**：用于脚本执行（可选）

### 端口需求
- **3000**：前端服务
- **9999**：后端 API 服务
- **3306**：MySQL 数据库
- **6379**：Redis 缓存
- **80**：Nginx 反向代理

## 完整部署指南

### 快速部署脚本

创建 `deploy.sh` 脚本：

```bash
#!/bin/bash
set -e

echo "=== jshERP 系统快速部署脚本 ==="

# 1. 检查 Docker 环境
if ! command -v docker &> /dev/null; then
    echo "错误：Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "错误：Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 2. 进入项目目录
PROJECT_DIR="/Users/<USER>/Desktop/jshERP-0612-Cursor"
cd "$PROJECT_DIR"

echo "当前工作目录：$(pwd)"

# 3. 修改 MyBatis 配置
echo "修改 MyBatis 映射文件配置..."
sed -i.bak 's/classpath\*:\/mapper_xml\/\*Mapper\.xml/classpath*:\/mapper_xml\/\*.xml/g' \
    jshERP-boot/src/main/resources/application-docker.properties

# 4. 启动基础服务
echo "启动基础服务（MySQL, Redis, Nginx）..."
docker-compose -f docker-compose.dev.yml up -d jsherp-mysql-dev jsherp-redis-dev jsherp-nginx-dev

# 5. 等待数据库启动
echo "等待数据库启动..."
sleep 30

# 6. 导入数据库
echo "导入数据库..."
docker exec -i jsherp-mysql-dev mysql -u root -p123456 jsh_erp < \
    jshERP-boot/docs/jsh_erp_2025-06-11_06-43-53_mysql_data_pbMor.sql

# 7. 更新数据库表结构
echo "更新数据库表结构..."
docker exec jsherp-mysql-dev mysql -u root -p123456 -e "
ALTER TABLE jsh_erp.jsh_system_config ADD COLUMN IF NOT EXISTS zero_change_amount_flag varchar(1) DEFAULT '0' COMMENT '零收付款启用标记，0未启用，1启用' AFTER audit_print_flag;
ALTER TABLE jsh_erp.jsh_system_config ADD COLUMN IF NOT EXISTS customer_static_price_flag varchar(1) DEFAULT '0' COMMENT '客户静态单价启用标记，0未启用，1启用' AFTER zero_change_amount_flag;
"

# 8. 启动后端服务
echo "启动后端服务..."
docker-compose -f docker-compose.dev.yml up -d jsherp-backend-dev

# 9. 等待后端启动
echo "等待后端服务启动..."
sleep 45

# 10. 修复前端代理配置
echo "修复前端代理配置..."
docker exec jsherp-frontend-dev sed -i 's/http:\/\/localhost:9999/http:\/\/jsherp-backend-dev:9999/g' vue.config.js || true

# 11. 启动前端服务
echo "启动前端服务..."
docker-compose -f docker-compose.dev.yml up -d jsherp-frontend-dev

# 12. 验证部署
echo "验证部署状态..."
sleep 30

echo "=== 部署完成 ==="
echo "前端访问地址：http://localhost:3000"
echo "后端 API 地址：http://localhost:9999/jshERP-boot"
echo "默认账号：admin / 123456"
echo ""
echo "检查服务状态："
docker-compose -f docker-compose.dev.yml ps
```

### 详细部署步骤

#### 步骤 1：环境准备
```bash
# 检查 Docker 版本
docker --version
docker-compose --version

# 确保端口未被占用
netstat -tulpn | grep -E ':(3000|9999|3306|6379|80)\s'
```

#### 步骤 2：获取项目代码
```bash
# 进入项目目录
cd /Users/<USER>/Desktop/jshERP-0612-Cursor

# 验证项目结构
ls -la
```

**预期结果**：应该看到 `jshERP-boot`、`jshERP-web`、`docker-compose.dev.yml` 等目录和文件。

#### 步骤 3：修改关键配置
```bash
# 修改 MyBatis 映射文件配置
sed -i.bak 's/classpath\*:\/mapper_xml\/\*Mapper\.xml/classpath*:\/mapper_xml\/\*.xml/g' \
    jshERP-boot/src/main/resources/application-docker.properties

# 验证修改结果
grep "mapper-locations" jshERP-boot/src/main/resources/application-docker.properties
```

**预期结果**：应该显示 `mybatis-plus.mapper-locations=classpath*:/mapper_xml/*.xml`

#### 步骤 4：启动基础服务
```bash
# 启动 MySQL、Redis、Nginx
docker-compose -f docker-compose.dev.yml up -d jsherp-mysql-dev jsherp-redis-dev jsherp-nginx-dev

# 检查服务状态
docker-compose -f docker-compose.dev.yml ps
```

**预期结果**：三个服务状态应该为 "Up"。

#### 步骤 5：数据库初始化
```bash
# 等待数据库完全启动
sleep 30

# 测试数据库连接
docker exec jsherp-mysql-dev mysql -u root -p123456 -e "SELECT 1"

# 导入数据库
docker exec -i jsherp-mysql-dev mysql -u root -p123456 jsh_erp < \
    jshERP-boot/docs/jsh_erp_2025-06-11_06-43-53_mysql_data_pbMor.sql
```

**预期结果**：数据库导入成功，无错误信息。

#### 步骤 6：数据库表结构更新
```bash
# 添加缺失的字段
docker exec jsherp-mysql-dev mysql -u root -p123456 -e "
ALTER TABLE jsh_erp.jsh_system_config ADD COLUMN IF NOT EXISTS zero_change_amount_flag varchar(1) DEFAULT '0' COMMENT '零收付款启用标记，0未启用，1启用' AFTER audit_print_flag;
ALTER TABLE jsh_erp.jsh_system_config ADD COLUMN IF NOT EXISTS customer_static_price_flag varchar(1) DEFAULT '0' COMMENT '客户静态单价启用标记，0未启用，1启用' AFTER zero_change_amount_flag;
"

# 验证表结构
docker exec jsherp-mysql-dev mysql -u root -p123456 -e "DESCRIBE jsh_erp.jsh_system_config;" | tail -5
```

**预期结果**：应该看到新添加的两个字段。

#### 步骤 7：启动后端服务
```bash
# 启动后端
docker-compose -f docker-compose.dev.yml up -d jsherp-backend-dev

# 监控启动日志
docker logs -f jsherp-backend-dev
```

**预期结果**：看到 "Started ErpApplication" 表示启动成功。

#### 步骤 8：修复前端配置并启动
```bash
# 等待后端完全启动
sleep 45

# 修复前端代理配置
docker exec jsherp-frontend-dev sed -i 's/http:\/\/localhost:9999/http:\/\/jsherp-backend-dev:9999/g' vue.config.js

# 启动前端
docker-compose -f docker-compose.dev.yml up -d jsherp-frontend-dev

# 检查所有服务状态
docker-compose -f docker-compose.dev.yml ps
```

**预期结果**：所有服务状态为 "Up"。

#### 步骤 9：功能验证
```bash
# 测试验证码接口
curl -s http://localhost:3000/jshERP-boot/user/randomImage | head -c 100

# 测试前端页面
curl -s -o /dev/null -w "%{http_code}" http://localhost:3000
```

**预期结果**：验证码接口返回 JSON 数据，前端页面返回 200 状态码。

## 问题诊断与解决方案

### 问题 1：MyBatis 映射文件加载失败

#### 问题现象
```
org.apache.ibatis.binding.BindingException: Invalid bound statement (not found):
com.jsh.erp.datasource.mappers.LogMapperEx.insertLogWithUserId
```

#### 根本原因
Docker 环境配置文件中 MyBatis 映射文件路径配置错误，只加载 `*Mapper.xml` 文件，不包括 `*MapperEx.xml` 扩展文件。

#### 解决方案
1. **定位配置文件**：
   ```bash
   vim jshERP-boot/src/main/resources/application-docker.properties
   ```

2. **修改配置**：
   ```properties
   # 修改前（错误）
   mybatis-plus.mapper-locations=classpath*:/mapper_xml/*Mapper.xml

   # 修改后（正确）
   mybatis-plus.mapper-locations=classpath*:/mapper_xml/*.xml
   ```

3. **验证修复**：
   ```bash
   # 重启后端服务
   docker-compose -f docker-compose.dev.yml restart jsherp-backend-dev

   # 检查日志确认映射文件加载成功
   docker logs jsherp-backend-dev | grep -i mapper
   ```

#### 预防措施
- 部署前对比生产环境和开发环境的配置文件差异
- 建立配置文件版本管理机制

### 问题 2：数据库表结构与代码不匹配

#### 问题现象
```
com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException:
Unknown column 'zero_change_amount_flag' in 'field list'
```

#### 根本原因
代码中的实体类包含了新版本的字段，但数据库表结构还是旧版本，缺少对应字段。

#### 解决方案
1. **检查缺失字段**：
   ```bash
   # 查看当前表结构
   docker exec jsherp-mysql-dev mysql -u root -p123456 -e "DESCRIBE jsh_erp.jsh_system_config;"
   ```

2. **执行数据库升级**：
   ```sql
   ALTER TABLE jsh_system_config ADD COLUMN zero_change_amount_flag varchar(1) DEFAULT '0'
   COMMENT '零收付款启用标记，0未启用，1启用' AFTER audit_print_flag;

   ALTER TABLE jsh_system_config ADD COLUMN customer_static_price_flag varchar(1) DEFAULT '0'
   COMMENT '客户静态单价启用标记，0未启用，1启用' AFTER zero_change_amount_flag;
   ```

3. **验证修复**：
   ```bash
   # 检查字段是否添加成功
   docker exec jsherp-mysql-dev mysql -u root -p123456 -e "SHOW COLUMNS FROM jsh_erp.jsh_system_config LIKE '%flag%';"
   ```

#### 预防措施
- 建立数据库版本管理机制
- 部署前检查数据库升级脚本
- 使用自动化数据库迁移工具

### 问题 3：验证码显示异常

#### 问题现象
验证码接口返回 500 错误，前端无法显示验证码图片。

#### 根本原因
Docker 容器中缺少图形字体库，Java AWT 无法在无图形环境中生成验证码图片。

#### 解决方案
1. **修改验证码生成逻辑**：
   ```java
   // 在 UserController.randomImage() 方法中添加 SVG 验证码生成
   private String generateSimpleSvgCaptcha(String codeNum) {
       StringBuilder svg = new StringBuilder();
       svg.append("<svg width='105' height='35' xmlns='http://www.w3.org/2000/svg'>");
       svg.append("<rect width='105' height='35' fill='#f8f9fa' stroke='#dee2e6'/>");

       // 添加验证码文字
       for (int i = 0; i < codeNum.length(); i++) {
           char c = codeNum.charAt(i);
           int x = 15 + i * 20;
           int y = 22;
           svg.append("<text x='").append(x).append("' y='").append(y)
              .append("' font-family='Arial,sans-serif' font-size='18' font-weight='bold' fill='#495057'>")
              .append(c).append("</text>");
       }

       svg.append("</svg>");
       String svgBase64 = java.util.Base64.getEncoder().encodeToString(svg.toString().getBytes());
       return "data:image/svg+xml;base64," + svgBase64;
   }
   ```

2. **验证修复**：
   ```bash
   # 测试验证码接口
   curl -s http://localhost:3000/jshERP-boot/user/randomImage
   ```

#### 预防措施
- 容器化应用避免依赖图形库
- 使用纯文本或 SVG 替代方案
- 在容器中安装必要的字体库

### 问题 4：前后端网络通信失败

#### 问题现象
前端页面可以访问，但无法调用后端 API，验证码和登录功能失效。

#### 根本原因
前端容器中的代理配置使用 `localhost`，在 Docker 网络中无法访问后端容器。

#### 解决方案
1. **检查网络配置**：
   ```bash
   # 查看容器网络
   docker network ls
   docker inspect jsherp_network_dev
   ```

2. **修复代理配置**：
   ```bash
   # 修改前端容器中的代理配置
   docker exec jsherp-frontend-dev sed -i 's/http:\/\/localhost:9999/http:\/\/jsherp-backend-dev:9999/g' vue.config.js

   # 重启前端服务
   docker-compose -f docker-compose.dev.yml restart jsherp-frontend-dev
   ```

3. **验证修复**：
   ```bash
   # 测试前端到后端的连接
   curl -s http://localhost:3000/jshERP-boot/user/randomImage
   ```

#### 预防措施
- 容器化应用使用容器名进行内部通信
- 建立统一的网络配置管理
- 使用环境变量管理不同环境的配置

### 通用故障排查方法论

#### 1. 日志分析法
```bash
# 查看容器日志
docker logs jsherp-backend-dev --tail 50
docker logs jsherp-frontend-dev --tail 50

# 实时监控日志
docker logs -f jsherp-backend-dev
```

#### 2. 网络连通性测试
```bash
# 测试容器间网络连通性
docker exec jsherp-frontend-dev ping jsherp-backend-dev
docker exec jsherp-backend-dev ping jsherp-mysql-dev

# 测试端口可达性
docker exec jsherp-frontend-dev telnet jsherp-backend-dev 9999
```

#### 3. 配置文件对比
```bash
# 对比配置文件差异
diff jshERP-boot/src/main/resources/application.properties \
     jshERP-boot/src/main/resources/application-docker.properties
```

#### 4. 数据库连接测试
```bash
# 测试数据库连接
docker exec jsherp-mysql-dev mysql -u root -p123456 -e "SELECT 1"

# 检查数据库表结构
docker exec jsherp-mysql-dev mysql -u root -p123456 -e "SHOW TABLES FROM jsh_erp"
```

#### 5. 服务健康检查
```bash
# 检查所有服务状态
docker-compose -f docker-compose.dev.yml ps

# 检查容器资源使用情况
docker stats
```

## 系统访问信息

### 🌐 访问地址
- **前端系统**：http://localhost:3000
- **后端 API**：http://localhost:9999/jshERP-boot
- **API 文档**：http://localhost:9999/jshERP-boot/doc.html
- **Swagger 文档**：http://localhost:9999/jshERP-boot/swagger-ui.html

### 👤 测试账号
| 角色 | 用户名 | 密码 | 权限说明 |
|------|--------|------|----------|
| 系统管理员 | admin | 123456 | 拥有所有功能权限 |
| 租户管理员 | waterxi | 123456 | 租户级别管理权限 |

### 🗄️ 数据库连接信息
- **主机**：localhost:3306
- **数据库名**：jsh_erp
- **用户名**：root
- **密码**：123456
- **字符集**：utf8

### 📊 Redis 连接信息
- **主机**：localhost:6379
- **密码**：1234abcd
- **数据库**：0（默认）

### 🐳 Docker 服务信息
| 服务名 | 容器名 | 端口映射 | 状态检查 |
|--------|--------|----------|----------|
| 前端服务 | jsherp-frontend-dev | 3000:3000 | http://localhost:3000 |
| 后端服务 | jsherp-backend-dev | 9999:9999 | http://localhost:9999/jshERP-boot |
| MySQL | jsherp-mysql-dev | 3306:3306 | `docker exec jsherp-mysql-dev mysql -u root -p123456 -e "SELECT 1"` |
| Redis | jsherp-redis-dev | 6379:6379 | `docker exec jsherp-redis-dev redis-cli -a 1234abcd ping` |
| Nginx | jsherp-nginx-dev | 80:80 | http://localhost |

## 功能验证清单

### ✅ 基础功能验证

#### 1. 系统登录验证
```bash
# 访问登录页面
curl -s -o /dev/null -w "%{http_code}" http://localhost:3000

# 获取验证码
curl -s http://localhost:3000/jshERP-boot/user/randomImage | head -c 100

# 测试登录（需要真实验证码）
# 在浏览器中手动测试：用户名 admin，密码 123456
```

#### 2. 菜单权限验证
- [ ] 零售管理模块可访问
- [ ] 采购管理模块可访问
- [ ] 销售管理模块可访问
- [ ] 仓库管理模块可访问
- [ ] 财务管理模块可访问
- [ ] 报表查询模块可访问
- [ ] 商品管理模块可访问
- [ ] 基础资料模块可访问
- [ ] 系统管理模块可访问

#### 3. 数据库连接验证
```bash
# 检查数据库连接
docker exec jsherp-mysql-dev mysql -u root -p123456 -e "SELECT COUNT(*) FROM jsh_erp.jsh_user"

# 检查关键表数据
docker exec jsherp-mysql-dev mysql -u root -p123456 -e "
SELECT table_name, table_rows
FROM information_schema.tables
WHERE table_schema = 'jsh_erp'
ORDER BY table_rows DESC
LIMIT 10"
```

#### 4. 缓存服务验证
```bash
# 检查 Redis 连接
docker exec jsherp-redis-dev redis-cli -a 1234abcd ping

# 检查验证码缓存
docker exec jsherp-redis-dev redis-cli -a 1234abcd keys "*captcha*"
```

### ✅ 业务功能验证

#### 1. 商品管理
- [ ] 创建商品类别
- [ ] 添加商品信息
- [ ] 设置多单位
- [ ] 配置多属性

#### 2. 基础资料
- [ ] 添加供应商信息
- [ ] 添加客户信息
- [ ] 设置仓库信息
- [ ] 配置结算账户

#### 3. 采购流程
- [ ] 创建采购订单
- [ ] 采购入库操作
- [ ] 采购退货处理

#### 4. 销售流程
- [ ] 创建销售订单
- [ ] 销售出库操作
- [ ] 销售退货处理

#### 5. 库存管理
- [ ] 查看商品库存
- [ ] 其它入库操作
- [ ] 调拨出库操作

#### 6. 财务管理
- [ ] 创建收款单
- [ ] 创建付款单
- [ ] 查看账户统计

#### 7. 报表功能
- [ ] 查看进销存统计
- [ ] 查看采购统计
- [ ] 查看销售统计
- [ ] 查看库存预警

### ✅ 性能验证

#### 1. 响应时间测试
```bash
# 测试页面加载时间
time curl -s http://localhost:3000 > /dev/null

# 测试 API 响应时间
time curl -s http://localhost:9999/jshERP-boot/user/getUserSession > /dev/null
```

#### 2. 并发测试
```bash
# 简单并发测试（需要安装 ab 工具）
ab -n 100 -c 10 http://localhost:3000/

# 或使用 curl 进行简单测试
for i in {1..10}; do
  curl -s http://localhost:3000 > /dev/null &
done
wait
```

#### 3. 资源使用监控
```bash
# 监控容器资源使用
docker stats --no-stream

# 检查磁盘使用
docker system df
```

## 维护与运维指南

### 🔧 日常维护命令

#### 服务管理
```bash
# 查看所有服务状态
docker-compose -f docker-compose.dev.yml ps

# 启动所有服务
docker-compose -f docker-compose.dev.yml up -d

# 停止所有服务
docker-compose -f docker-compose.dev.yml down

# 重启特定服务
docker-compose -f docker-compose.dev.yml restart jsherp-backend-dev

# 查看服务资源使用情况
docker stats
```

#### 日志管理
```bash
# 查看后端日志
docker logs jsherp-backend-dev --tail 100

# 实时监控日志
docker logs -f jsherp-backend-dev

# 查看前端日志
docker logs jsherp-frontend-dev --tail 50

# 查看数据库日志
docker logs jsherp-mysql-dev --tail 50

# 导出日志到文件
docker logs jsherp-backend-dev > backend.log 2>&1
```

#### 数据库维护
```bash
# 连接数据库
docker exec -it jsherp-mysql-dev mysql -u root -p123456

# 备份数据库
docker exec jsherp-mysql-dev mysqldump -u root -p123456 jsh_erp > backup_$(date +%Y%m%d_%H%M%S).sql

# 恢复数据库
docker exec -i jsherp-mysql-dev mysql -u root -p123456 jsh_erp < backup_file.sql

# 检查数据库大小
docker exec jsherp-mysql-dev mysql -u root -p123456 -e "
SELECT
    table_schema AS 'Database',
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)'
FROM information_schema.tables
WHERE table_schema = 'jsh_erp'
GROUP BY table_schema;"
```

#### 缓存管理
```bash
# 连接 Redis
docker exec -it jsherp-redis-dev redis-cli -a 1234abcd

# 清空缓存
docker exec jsherp-redis-dev redis-cli -a 1234abcd FLUSHALL

# 查看缓存使用情况
docker exec jsherp-redis-dev redis-cli -a 1234abcd INFO memory

# 查看所有键
docker exec jsherp-redis-dev redis-cli -a 1234abcd KEYS "*"
```

### 📊 监控与告警

#### 健康检查脚本
创建 `health_check.sh`：

```bash
#!/bin/bash

echo "=== jshERP 系统健康检查 ==="

# 检查容器状态
echo "1. 检查容器状态..."
docker-compose -f docker-compose.dev.yml ps

# 检查前端服务
echo "2. 检查前端服务..."
FRONTEND_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000)
if [ "$FRONTEND_STATUS" = "200" ]; then
    echo "✅ 前端服务正常"
else
    echo "❌ 前端服务异常，状态码：$FRONTEND_STATUS"
fi

# 检查后端服务
echo "3. 检查后端服务..."
BACKEND_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:9999/jshERP-boot/user/randomImage)
if [ "$BACKEND_STATUS" = "200" ]; then
    echo "✅ 后端服务正常"
else
    echo "❌ 后端服务异常，状态码：$BACKEND_STATUS"
fi

# 检查数据库连接
echo "4. 检查数据库连接..."
DB_CHECK=$(docker exec jsherp-mysql-dev mysql -u root -p123456 -e "SELECT 1" 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "✅ 数据库连接正常"
else
    echo "❌ 数据库连接异常"
fi

# 检查 Redis 连接
echo "5. 检查 Redis 连接..."
REDIS_CHECK=$(docker exec jsherp-redis-dev redis-cli -a 1234abcd ping 2>/dev/null)
if [ "$REDIS_CHECK" = "PONG" ]; then
    echo "✅ Redis 连接正常"
else
    echo "❌ Redis 连接异常"
fi

# 检查磁盘空间
echo "6. 检查磁盘空间..."
DISK_USAGE=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ "$DISK_USAGE" -lt 80 ]; then
    echo "✅ 磁盘空间充足 ($DISK_USAGE%)"
else
    echo "⚠️  磁盘空间不足 ($DISK_USAGE%)"
fi

echo "=== 健康检查完成 ==="
```

#### 性能监控
```bash
# 创建性能监控脚本
cat > monitor.sh << 'EOF'
#!/bin/bash

while true; do
    echo "=== $(date) ==="

    # CPU 和内存使用情况
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"

    # 磁盘使用情况
    echo "磁盘使用："
    df -h /

    # 网络连接数
    echo "网络连接数："
    netstat -an | grep :3000 | wc -l
    netstat -an | grep :9999 | wc -l

    echo "========================"
    sleep 60
done
EOF

chmod +x monitor.sh
```

### 🔄 备份恢复流程

#### 完整备份脚本
创建 `backup.sh`：

```bash
#!/bin/bash

BACKUP_DIR="/backup/jshERP/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "开始备份 jshERP 系统..."

# 1. 备份数据库
echo "备份数据库..."
docker exec jsherp-mysql-dev mysqldump -u root -p123456 --single-transaction jsh_erp > "$BACKUP_DIR/database.sql"

# 2. 备份 Redis 数据
echo "备份 Redis 数据..."
docker exec jsherp-redis-dev redis-cli -a 1234abcd --rdb /data/dump.rdb
docker cp jsherp-redis-dev:/data/dump.rdb "$BACKUP_DIR/redis_dump.rdb"

# 3. 备份配置文件
echo "备份配置文件..."
cp -r jshERP-boot/src/main/resources/ "$BACKUP_DIR/config/"
cp docker-compose.dev.yml "$BACKUP_DIR/"

# 4. 备份上传文件（如果有）
echo "备份上传文件..."
if [ -d "/opt/jshERP/upload" ]; then
    cp -r /opt/jshERP/upload "$BACKUP_DIR/"
fi

# 5. 创建备份信息文件
echo "创建备份信息..."
cat > "$BACKUP_DIR/backup_info.txt" << EOF
备份时间: $(date)
系统版本: jshERP
数据库版本: $(docker exec jsherp-mysql-dev mysql -u root -p123456 -e "SELECT VERSION()" | tail -1)
Redis 版本: $(docker exec jsherp-redis-dev redis-cli -a 1234abcd INFO server | grep redis_version)
备份大小: $(du -sh "$BACKUP_DIR" | cut -f1)
EOF

echo "备份完成，备份目录：$BACKUP_DIR"
```

#### 恢复脚本
创建 `restore.sh`：

```bash
#!/bin/bash

if [ $# -ne 1 ]; then
    echo "使用方法: $0 <备份目录>"
    exit 1
fi

BACKUP_DIR="$1"

if [ ! -d "$BACKUP_DIR" ]; then
    echo "错误：备份目录不存在"
    exit 1
fi

echo "开始恢复 jshERP 系统..."

# 1. 停止服务
echo "停止服务..."
docker-compose -f docker-compose.dev.yml down

# 2. 恢复数据库
echo "恢复数据库..."
docker-compose -f docker-compose.dev.yml up -d jsherp-mysql-dev
sleep 30
docker exec -i jsherp-mysql-dev mysql -u root -p123456 jsh_erp < "$BACKUP_DIR/database.sql"

# 3. 恢复 Redis 数据
echo "恢复 Redis 数据..."
docker-compose -f docker-compose.dev.yml up -d jsherp-redis-dev
sleep 10
docker cp "$BACKUP_DIR/redis_dump.rdb" jsherp-redis-dev:/data/dump.rdb
docker-compose -f docker-compose.dev.yml restart jsherp-redis-dev

# 4. 恢复配置文件
echo "恢复配置文件..."
cp -r "$BACKUP_DIR/config/"* jshERP-boot/src/main/resources/

# 5. 恢复上传文件
echo "恢复上传文件..."
if [ -d "$BACKUP_DIR/upload" ]; then
    mkdir -p /opt/jshERP/
    cp -r "$BACKUP_DIR/upload" /opt/jshERP/
fi

# 6. 启动所有服务
echo "启动所有服务..."
docker-compose -f docker-compose.dev.yml up -d

echo "恢复完成"
```

## 常见问题 FAQ

### Q1: 容器启动失败怎么办？

**A**: 按以下步骤排查：

1. 检查端口占用：
   ```bash
   netstat -tulpn | grep -E ':(3000|9999|3306|6379|80)\s'
   ```

2. 检查 Docker 资源：
   ```bash
   docker system df
   docker system prune -f
   ```

3. 查看容器日志：
   ```bash
   docker logs jsherp-backend-dev
   ```

### Q2: 前端页面无法访问后端 API？

**A**: 检查网络配置：

1. 确认容器网络：
   ```bash
   docker network ls
   docker inspect jsherp_network_dev
   ```

2. 修复代理配置：
   ```bash
   docker exec jsherp-frontend-dev sed -i 's/localhost/jsherp-backend-dev/g' vue.config.js
   docker-compose -f docker-compose.dev.yml restart jsherp-frontend-dev
   ```

### Q3: 数据库连接失败？

**A**: 检查数据库状态：

1. 确认数据库启动：
   ```bash
   docker logs jsherp-mysql-dev
   ```

2. 测试连接：
   ```bash
   docker exec jsherp-mysql-dev mysql -u root -p123456 -e "SELECT 1"
   ```

3. 检查配置文件中的数据库连接信息

### Q4: 验证码不显示？

**A**: 这通常是图形库问题：

1. 检查验证码接口：
   ```bash
   curl -s http://localhost:3000/jshERP-boot/user/randomImage
   ```

2. 如果返回错误，说明需要修改验证码生成逻辑（参考问题解决方案部分）

### Q5: 系统运行缓慢？

**A**: 性能优化建议：

1. 增加容器内存限制：
   ```yaml
   # 在 docker-compose.dev.yml 中添加
   services:
     jsherp-backend-dev:
       mem_limit: 2g
   ```

2. 优化数据库配置：
   ```bash
   # 增加 MySQL 缓存
   docker exec jsherp-mysql-dev mysql -u root -p123456 -e "SET GLOBAL innodb_buffer_pool_size=268435456"
   ```

3. 清理 Docker 资源：
   ```bash
   docker system prune -f
   docker volume prune -f
   ```

### Q6: 如何升级系统？

**A**: 升级步骤：

1. 备份当前系统：
   ```bash
   ./backup.sh
   ```

2. 拉取新代码：
   ```bash
   git pull origin main
   ```

3. 重新构建镜像：
   ```bash
   docker-compose -f docker-compose.dev.yml build
   ```

4. 执行数据库升级脚本（如果有）

5. 重启服务：
   ```bash
   docker-compose -f docker-compose.dev.yml up -d
   ```

### Q7: 如何重置系统？

**A**: 完全重置步骤：

1. 停止并删除所有容器：
   ```bash
   docker-compose -f docker-compose.dev.yml down -v
   ```

2. 删除相关镜像：
   ```bash
   docker rmi $(docker images | grep jsherp | awk '{print $3}')
   ```

3. 重新部署：
   ```bash
   ./deploy.sh
   ```

---

## 📞 技术支持

如果遇到本文档未涵盖的问题，可以通过以下方式获取支持：

- **官方网站**: http://www.huaxiaerp.com/
- **GitHub Issues**: 在项目 GitHub 页面提交问题
- **社区论坛**: 参与社区讨论
- **技术文档**: 查阅官方技术文档

---

**文档版本**: v1.0
**最后更新**: 2025年6月13日
**适用版本**: jshERP 最新版本
