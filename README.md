# jshERP 库存盘点模块使用指南

## 📋 概述

本模块为jshERP系统提供完整的库存盘点功能，包括盘点计划创建、盘点数据录入、差异分析和报表导出等功能。

## 🏗️ 模块结构

```
inventory/
├── InventoryCheckList.vue          # 盘点复盘列表页
├── InventoryInputList.vue          # 盘点录入页面
├── modules/
│   ├── InventoryCheckModal.vue     # 盘点单弹窗
│   └── ImportStockModal.vue        # 导入库存弹窗
├── mixins/
│   └── InventoryMixin.js           # 业务逻辑混入
├── database/
│   └── inventory_menu_config.sql   # 菜单配置SQL脚本
└── README.md                       # 使用文档
```

## 🚀 部署步骤

### 1. 数据库配置

执行菜单配置SQL脚本：

```bash
# 1. 连接到jshERP数据库
mysql -u jsh_user -p jsherp-mysql-dev

# 2. 执行SQL脚本
source /path/to/inventory_menu_config.sql
```

**重要提示**：
- 执行前请先备份数据库
- 确认菜单编号09、0901、0902未被使用
- 根据实际角色ID调整权限分配脚本

### 2. 前端依赖（可选）

如果需要Excel导入功能，请安装XLSX库：

```bash
cd jshERP-web
npm install xlsx
```

如果无法安装，系统会自动降级到CSV格式支持。

### 3. 重启服务

```bash
# 重启jshERP后端服务
docker restart jsherp-boot

# 清除浏览器缓存并重新登录
```

## 📱 功能说明

### 盘点复盘页面 (InventoryCheckList.vue)

**功能特性**：
- 📊 盘点单据列表查询和管理
- 🔍 多条件搜索（单据编号、日期范围）
- ➕ 新增盘点单据
- ✏️ 编辑和删除盘点单据
- 📤 批量导出和打印功能
- 🏷️ 状态标签显示（草稿、盘点中、已完成）

**操作流程**：
1. 点击"新增"按钮创建盘点单
2. 填写盘点基本信息（仓库、类型、操作员等）
3. 选择盘点范围（全部、分类、指定商品）
4. 保存后进入盘点录入页面

### 盘点录入页面 (InventoryInputList.vue)

**功能特性**：
- 📝 盘点明细数据录入
- 🧮 自动计算盈亏数量和金额
- 📥 Excel/CSV文件导入
- 💾 实时保存和提交功能
- 📊 盈亏汇总统计

**操作流程**：
1. 从盘点复盘页面进入或直接访问
2. 点击"导入库存盘点数据"批量导入
3. 或手动添加行录入盘点数据
4. 系统自动计算盈亏差异
5. 保存草稿或提交完成盘点

### 导入功能 (ImportStockModal.vue)

**支持格式**：
- Excel文件（.xlsx, .xls）
- CSV文件（.csv）

**模板格式**：
| 商品名称 | 规格 | 账面数量 | 实际数量 | 备注 |
|---------|------|----------|----------|------|
| 示例商品1 | M | 100 | 95 | 盘点备注 |
| 示例商品2 | L | 50 | 52 | |

**导入选项**：
- ✅ 覆盖已存在的数据
- ✅ 跳过空行
- ✅ 自动计算盈亏

## 🎨 UI设计规范

### 设计系统
- **主色调**：#3B82F6
- **字体**：Inter
- **背景色**：#F7F8FA / #FFFFFF
- **圆角**：6px
- **间距**：16px / 24px

### 状态颜色
- **草稿**：橙色 (#fa8c16)
- **盘点中**：蓝色 (#1890ff)
- **已完成**：绿色 (#52c41a)
- **盈利**：绿色 (#52c41a)
- **亏损**：红色 (#ff4d4f)

## 🔧 技术规范

### 架构模式
- **Vue.js 2.7.16**：组件开发框架
- **Ant Design Vue 1.5.2**：UI组件库
- **JeecgListMixin**：列表页面标准混入
- **多租户架构**：tenant_id数据隔离

### API接口
基于现有jshERP单据体系：
- **基础路径**：`/depotHead/*`
- **盘点类型**：type="其它", subType="盘点"
- **状态管理**：0-草稿，1-盘点中，2-已完成

### 数据模型
```javascript
// 盘点单据头
{
  id: Number,
  number: String,        // 单据编号
  operTime: Date,        // 单据日期
  depotId: Number,       // 仓库ID
  status: String,        // 状态
  type: "其它",
  subType: "盘点",
  tenantId: Number       // 租户ID
}

// 盘点明细
{
  materialId: Number,    // 商品ID
  bookQuantity: Number,  // 账面数量
  actualQuantity: Number,// 实际数量
  differenceQuantity: Number, // 盈亏数量
  unitPrice: Number,     // 单价
  differenceAmount: Number,   // 盈亏金额
  remark: String        // 备注
}
```

## 🧪 测试指南

### 功能测试清单

#### 基础功能
- [ ] 菜单显示正常
- [ ] 页面加载无错误
- [ ] 搜索功能正常
- [ ] 分页功能正常

#### 盘点复盘页面
- [ ] 新增盘点单弹窗正常
- [ ] 编辑盘点单功能正常
- [ ] 删除盘点单功能正常
- [ ] 状态显示正确
- [ ] 跳转到录入页面正常

#### 盘点录入页面
- [ ] 单据头信息显示正确
- [ ] 明细表格编辑正常
- [ ] 盈亏计算准确
- [ ] 保存功能正常
- [ ] 提交功能正常

#### 导入功能
- [ ] 模板下载正常
- [ ] Excel文件导入正常
- [ ] CSV文件导入正常
- [ ] 数据验证正确
- [ ] 错误提示清晰

### 性能测试
- [ ] 页面加载时间 < 2秒
- [ ] 大量数据（1000+条）处理正常
- [ ] 文件导入响应时间合理

### 兼容性测试
- [ ] Chrome浏览器正常
- [ ] Firefox浏览器正常
- [ ] Safari浏览器正常
- [ ] 移动端响应式布局正常

## 🐛 常见问题

### Q1: 菜单不显示
**解决方案**：
1. 检查SQL脚本是否执行成功
2. 确认用户角色是否有相应权限
3. 清除浏览器缓存重新登录
4. 重启jshERP服务

### Q2: Excel导入失败
**解决方案**：
1. 检查是否安装了xlsx库
2. 使用CSV格式作为备选方案
3. 检查文件格式是否正确
4. 确认文件大小不超过10MB

### Q3: 盈亏计算错误
**解决方案**：
1. 检查账面数量是否正确获取
2. 确认单价数据是否准确
3. 验证计算公式逻辑
4. 检查数据类型转换

### Q4: 权限问题
**解决方案**：
1. 检查jsh_user_business表权限配置
2. 确认按钮权限字符串格式正确
3. 验证用户角色分配
4. 重新登录刷新权限

## 📞 技术支持

如遇到问题，请按以下步骤排查：

1. **检查浏览器控制台**：查看是否有JavaScript错误
2. **检查网络请求**：确认API调用是否正常
3. **检查数据库配置**：验证菜单和权限配置
4. **查看服务日志**：检查后端服务是否有错误

## 📝 更新日志

### v1.0.0 (2025-06-25)
- ✅ 完成基础架构搭建
- ✅ 实现盘点复盘页面
- ✅ 实现盘点录入页面
- ✅ 实现Excel/CSV导入功能
- ✅ 完成菜单权限配置
- ✅ 提供完整使用文档
