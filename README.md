# 项目总述
* 很多人说管伊佳ERP（原名：华夏ERP，英文名：jshERP）是目前人气领先的国产ERP系统
* 虽然目前只有进销存+财务+生产的功能，但后面将会推出ERP的全部功能，有兴趣请帮点一下 **Star** 哦


# 技术框架
* 核心框架：SpringBoot 2.0.0
* 持久层框架：Mybatis 1.3.2
* 日志管理：SLF4J 1.7
* 前端框架：Vue 2.7.16
* UI框架: Ant-Design-Vue 1.5.2
* 模板框架: Jeecg-Boot 2.2.0
* 项目管理框架: Maven 3.2.3

# 开发环境
建议开发者使用以下环境，可以避免版本带来的问题
* IDE: IntelliJ IDEA 2019.2+和JetBrains WebStorm 2019.3+
* DB: Mysql 5.7.33
* JDK: JDK 1.8
* Node: Node 20.17.0
* Maven: Maven 3.2.3+
* Redis: 6.2.1
* Nginx: 1.12.2 

# 服务器环境
* 数据库：Mysql5.7.33(需要安装 mysql5.7版本 （注意请不要安装8.0版本，不支持）)
* JAVA平台：JRE1.8
* Redis库：redis6.2.1
* Nginx代理：nginx1.12.2
* 操作系统：Windows、Linux等

# 配套资料
* 需要用户手册请访问这里 https://www.gyjerp.com/doc/archive/user-manual.html
* 需要接口文档请查看这里 https://www.gyjerp.com/doc/archive/apidoc.html
* 喜欢视频教程可以看这里 https://space.bilibili.com/540003552/channel/series 
* 为方便大家搭建运行环境，分享了下载地址 https://pan.baidu.com/s/1jlild9uyGdQ7H2yaMx76zw  提取码:814g
* 不会打包的小伙伴，请下载此打包后的文件 https://share.weiyun.com/NDJNLhry 密码：vd3aig
* 不会部署的小伙伴，请参考部署教程 https://www.gyjerp.com/doc/archive/deploy.html
* 部署后登录系统的默认租户账号：waterxi，默认超管账户：admin，默认密码均为：123456

# 开源说明
* 本系统100%开源，遵守GPL-3.0协议
* 支持全球73种语言，在登录后右上角“界面设置”页面进行切换
