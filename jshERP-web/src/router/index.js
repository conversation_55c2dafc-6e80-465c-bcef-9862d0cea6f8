/**
 * 统一路由管理
 * 整合桌面端和移动端路由系统
 */

import Vue from 'vue'
import Router from 'vue-router'

// 桌面端路由
import { constantRouterMap } from '@/config/router.config'

//update-begin-author:taoyan date:20191011 for:TASK #3214 【优化】访问online功能测试 浏览器控制台抛出异常
try {
  const originalPush = Router.prototype.push
  Router.prototype.push = function push(location) {
    return originalPush.call(this, location).catch(err => err)
  }
} catch (e) {
}
//update-end-author:taoyan date:20191011 for:TASK #3214 【优化】访问online功能测试 浏览器控制台抛出异常

Vue.use(Router)

/**
 * 桌面端路由配置
 */
const allRoutes = [
  // 桌面端路由
  ...constantRouterMap,

  // 根路径重定向
  {
    path: '/',
    redirect: '/dashboard/analysis'
  },

  // 404页面
  {
    path: '/404',
    component: () => import('@/views/exception/404')
  },

  // 通配符路由（必须放在最后）
  {
    path: '*',
    redirect: '/404'
  }
]

/**
 * 创建路由实例
 */
const router = new Router({
  mode: 'history',
  base: process.env.BASE_URL,
  scrollBehavior: (to, from, savedPosition) => {
    if (savedPosition) {
      return savedPosition
    } else {
      return { x: 0, y: 0 }
    }
  },
  routes: allRoutes
})

/**
 * 全局路由守卫
 */
router.beforeEach((to, from, next) => {
  next()
})

/**
 * 路由错误处理
 */
router.onError((error) => {
  console.error('路由错误:', error)

  // 处理代码分割加载失败
  if (error.message.includes('Loading chunk')) {
    window.location.reload()
  }
})

export default router