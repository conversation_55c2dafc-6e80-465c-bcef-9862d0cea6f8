-- =====================================================
-- jshERP 库存盘点模块菜单配置SQL脚本
-- 执行前请先查询现有菜单结构，确认编号不冲突
-- =====================================================

-- 1. 查询现有菜单结构（执行前先运行此查询）
-- SELECT number, name, parent_number, url, component, sort, enabled 
-- FROM jsh_function 
-- WHERE delete_flag = '0' 
-- ORDER BY sort;

-- 2. 查询编号09是否已被使用
-- SELECT * FROM jsh_function WHERE number = '09' AND delete_flag = '0';

-- =====================================================
-- 菜单配置脚本
-- =====================================================

-- 创建一级菜单：盘点业务
INSERT INTO jsh_function (
    number,           -- 菜单编号
    name,             -- 菜单名称
    parent_number,    -- 父菜单编号（0表示一级菜单）
    url,              -- URL路径
    component,        -- 组件路径
    state,            -- 状态
    sort,             -- 排序号
    enabled,          -- 是否启用
    type,             -- 类型
    push_btn,         -- 按钮权限（一级菜单为空）
    icon,             -- 图标
    delete_flag       -- 删除标志
) VALUES (
    '09',                           -- 使用编号09
    '盘点业务',                     -- 菜单名称
    '0',                            -- 一级菜单
    '/inventory',                   -- URL路径
    'layouts/RouteView',            -- 一级菜单组件（标准布局）
    0,                              -- 状态
    '0900',                         -- 排序号
    1,                              -- 启用
    '电脑版',                       -- 类型
    '',                             -- 一级菜单按钮权限为空
    'database',                     -- 图标
    '0'                             -- 未删除
);

-- 创建二级菜单：盘点复盘
INSERT INTO jsh_function (
    number,           
    name,             
    parent_number,    
    url,              
    component,        
    state,            
    sort,             
    enabled,          
    type,             
    push_btn,         
    icon,             
    delete_flag       
) VALUES (
    '0901',                                    -- 菜单编号
    '盘点复盘',                               -- 菜单名称  
    '09',                                      -- 父菜单编号
    '/inventory/inventory-check-list',         -- URL路径
    '/inventory/InventoryCheckList',           -- Vue组件路径
    0,                                         -- 状态
    '0901',                                    -- 排序号
    1,                                         -- 启用
    '电脑版',                                 -- 类型
    '1,2,3,5,6,7',                            -- 按钮权限（新增、编辑、删除、查看、导出、打印）
    'unordered-list',                          -- 图标
    '0'                                        -- 未删除
);

-- 创建二级菜单：盘点录入
INSERT INTO jsh_function (
    number,           
    name,             
    parent_number,    
    url,              
    component,        
    state,            
    sort,             
    enabled,          
    type,             
    push_btn,         
    icon,             
    delete_flag       
) VALUES (
    '0902',                                    -- 菜单编号
    '盘点录入',                               -- 菜单名称  
    '09',                                      -- 父菜单编号
    '/inventory/inventory-input-list',         -- URL路径
    '/inventory/InventoryInputList',           -- Vue组件路径
    0,                                         -- 状态
    '0902',                                    -- 排序号
    1,                                         -- 启用
    '电脑版',                                 -- 类型
    '1,2,3,4',                                -- 按钮权限（新增、编辑、保存、提交）
    'edit',                                    -- 图标
    '0'                                        -- 未删除
);

-- =====================================================
-- 权限分配脚本
-- =====================================================

-- 查询现有角色（执行前先运行此查询了解角色）
-- SELECT id, name, value, description FROM jsh_role WHERE delete_flag = '0';

-- 查询租户角色的当前权限（假设租户角色ID为10）
-- SELECT * FROM jsh_user_business 
-- WHERE type = 'RoleFunctions' AND key_id = '10' AND delete_flag = '0';

-- 为租户角色分配盘点业务权限（请根据实际角色ID调整）
-- 注意：需要先查询当前的value值，然后在末尾追加新的权限
UPDATE jsh_user_business 
SET value = CONCAT(IFNULL(value, ''), '[09][0901][0902]')
WHERE type = 'RoleFunctions' 
  AND key_id = '10'  -- 租户角色ID，请根据实际情况调整
  AND delete_flag = '0';

-- 为管理员角色分配盘点业务权限（假设管理员角色ID为4）
UPDATE jsh_user_business 
SET value = CONCAT(IFNULL(value, ''), '[09][0901][0902]')
WHERE type = 'RoleFunctions' 
  AND key_id = '4'   -- 管理员角色ID，请根据实际情况调整
  AND delete_flag = '0';

-- =====================================================
-- 验证脚本（执行完成后运行以验证配置）
-- =====================================================

-- 验证菜单是否创建成功
SELECT 
    number,
    name,
    parent_number,
    url,
    component,
    sort,
    enabled,
    push_btn
FROM jsh_function 
WHERE number IN ('09', '0901', '0902') 
  AND delete_flag = '0'
ORDER BY sort;

-- 验证权限是否分配成功
SELECT 
    ub.type,
    ub.key_id,
    r.name as role_name,
    ub.value
FROM jsh_user_business ub
LEFT JOIN jsh_role r ON ub.key_id = r.id
WHERE ub.type = 'RoleFunctions' 
  AND ub.value LIKE '%[09]%'
  AND ub.delete_flag = '0';

-- =====================================================
-- 回滚脚本（如果需要删除配置时使用）
-- =====================================================

-- 删除菜单配置
-- UPDATE jsh_function SET delete_flag = '1' WHERE number IN ('09', '0901', '0902');

-- 移除权限配置（需要手动编辑value字段，移除[09][0901][0902]部分）
-- 建议先备份原始value值，然后手动编辑

-- =====================================================
-- 使用说明
-- =====================================================

/*
执行步骤：
1. 先执行查询脚本，了解现有菜单结构
2. 确认编号09、0901、0902未被使用
3. 执行菜单配置脚本
4. 查询角色ID，确认要分配权限的角色
5. 执行权限分配脚本（注意修改角色ID）
6. 执行验证脚本，确认配置成功
7. 重启jshERP服务，刷新浏览器缓存
8. 登录系统验证菜单是否显示

注意事项：
- 执行前请备份数据库
- 角色ID请根据实际情况调整
- 如果编号冲突，请修改为其他可用编号
- 权限分配时注意不要覆盖现有权限
*/
