# jshERP 库存盘点模块维护扩展指南

## 📋 维护概述

本指南提供jshERP库存盘点模块的日常维护、问题排查、性能优化和功能扩展的详细说明。

## 🔧 日常维护

### 系统监控

#### 性能监控
```bash
# 检查Docker容器资源使用
docker stats jsherp-boot jsherp-web jsherp-mysql

# 检查磁盘空间
df -h

# 检查内存使用
free -h

# 检查数据库连接数
docker exec jsherp-mysql mysql -u jsh_user -p123456 -e "SHOW STATUS LIKE 'Threads_connected';"
```

#### 日志监控
```bash
# 查看后端应用日志
docker logs --tail 100 jsherp-boot | grep -E "(ERROR|WARN|inventory)"

# 查看数据库错误日志
docker exec jsherp-mysql tail -f /var/log/mysql/error.log

# 查看前端访问日志
docker logs --tail 50 jsherp-web
```

### 数据备份

#### 定期备份脚本
```bash
#!/bin/bash
# inventory_backup.sh

BACKUP_DIR="/backup/jshERP/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# 备份数据库
docker exec jsherp-mysql mysqldump -u jsh_user -p123456 jsh_erp > $BACKUP_DIR/jsh_erp_$(date +%H%M%S).sql

# 备份前端代码
tar -czf $BACKUP_DIR/inventory_frontend_$(date +%H%M%S).tar.gz jshERP-web/src/views/inventory/

# 清理7天前的备份
find /backup/jshERP/ -type d -mtime +7 -exec rm -rf {} \;

echo "备份完成: $BACKUP_DIR"
```

#### 自动备份配置
```bash
# 添加到crontab
crontab -e

# 每天凌晨2点执行备份
0 2 * * * /path/to/inventory_backup.sh
```

### 数据清理

#### 清理临时数据
```sql
-- 清理已删除的菜单记录（可选）
DELETE FROM jsh_function 
WHERE delete_flag = '1' AND update_time < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 清理过期的用户会话
DELETE FROM jsh_user_session 
WHERE expire_time < NOW();

-- 优化表结构
OPTIMIZE TABLE jsh_function, jsh_user_business, jsh_depot_head, jsh_depot_item;
```

## 🐛 问题排查

### 常见问题诊断

#### 问题1：菜单突然消失
**症状**：用户反馈看不到盘点业务菜单

**排查步骤**：
```sql
-- 1. 检查菜单是否被误删
SELECT number, name, delete_flag, enabled FROM jsh_function 
WHERE number IN ('09', '0901', '0902');

-- 2. 检查用户权限
SELECT ub.key_id, r.name, ub.value FROM jsh_user_business ub
LEFT JOIN jsh_role r ON ub.key_id = r.id
WHERE ub.type = 'RoleFunctions' AND ub.value LIKE '%[09]%';

-- 3. 检查用户角色分配
SELECT u.username, ub_user.key_id as role_id FROM jsh_user u
JOIN jsh_user_business ub_user ON u.id = ub_user.value
WHERE ub_user.type = 'UserRole' AND u.username = '用户名';
```

**解决方案**：
```sql
-- 恢复菜单（如果被误删）
UPDATE jsh_function SET delete_flag = '0' 
WHERE number IN ('09', '0901', '0902');

-- 重新分配权限
UPDATE jsh_user_business 
SET value = CONCAT(IFNULL(value, ''), '[09][0901][0902]')
WHERE type = 'RoleFunctions' AND key_id = '角色ID';
```

#### 问题2：页面加载缓慢
**症状**：盘点页面加载时间超过5秒

**排查步骤**：
```bash
# 1. 检查网络延迟
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:9999/depotHead/list

# 2. 检查数据库查询性能
docker exec jsherp-mysql mysql -u jsh_user -p123456 jsh_erp -e "
SHOW PROCESSLIST;
SHOW STATUS LIKE 'Slow_queries';
"

# 3. 检查内存使用
docker stats jsherp-boot --no-stream
```

**解决方案**：
```sql
-- 添加数据库索引
ALTER TABLE jsh_depot_head ADD INDEX idx_type_subtype (type, sub_type);
ALTER TABLE jsh_depot_item ADD INDEX idx_header_id (header_id);

-- 优化查询
ANALYZE TABLE jsh_depot_head, jsh_depot_item;
```

#### 问题3：导入功能失败
**症状**：Excel文件导入时报错

**排查步骤**：
1. 检查浏览器控制台错误
2. 验证文件格式和大小
3. 检查服务器磁盘空间

**解决方案**：
```javascript
// 在ImportStockModal.vue中添加更详细的错误处理
beforeUpload(file) {
  console.log('文件信息:', {
    name: file.name,
    size: file.size,
    type: file.type
  });
  
  // 添加更多验证逻辑
  if (file.size > 10 * 1024 * 1024) {
    this.$message.error('文件大小不能超过10MB');
    return false;
  }
  
  return true;
}
```

### 性能优化

#### 前端优化
```javascript
// 1. 组件懒加载
const InventoryCheckList = () => import('./InventoryCheckList.vue');
const InventoryInputList = () => import('./InventoryInputList.vue');

// 2. 数据分页优化
data() {
  return {
    ipagination: {
      pageSize: 20, // 减少每页数据量
      showSizeChanger: true,
      pageSizeOptions: ['10', '20', '50', '100']
    }
  }
}

// 3. 防抖搜索
methods: {
  searchQuery: debounce(function() {
    this.loadData();
  }, 300)
}
```

#### 后端优化
```sql
-- 1. 数据库索引优化
CREATE INDEX idx_depot_head_inventory ON jsh_depot_head(type, sub_type, tenant_id, delete_flag);
CREATE INDEX idx_depot_item_header ON jsh_depot_item(header_id, tenant_id, delete_flag);

-- 2. 查询优化
-- 使用LIMIT限制查询结果
SELECT * FROM jsh_depot_head 
WHERE type = '其它' AND sub_type = '盘点' 
  AND tenant_id = ? AND delete_flag = '0'
ORDER BY create_time DESC 
LIMIT 20 OFFSET 0;
```

## 🚀 功能扩展

### 扩展新的盘点类型

#### 1. 添加"快速盘点"类型
```sql
-- 在数据库中无需修改，通过前端配置即可
```

```javascript
// 在InventoryCheckModal.vue中添加新类型
data() {
  return {
    checkTypeOptions: [
      { label: '全盘', value: '全盘' },
      { label: '部分盘点', value: '部分盘点' },
      { label: '循环盘点', value: '循环盘点' },
      { label: '抽盘', value: '抽盘' },
      { label: '快速盘点', value: '快速盘点' } // 新增
    ]
  }
}
```

#### 2. 添加盘点模板功能
```javascript
// 创建新组件：InventoryTemplateModal.vue
<template>
  <a-modal title="盘点模板管理" :visible="visible">
    <a-form>
      <a-form-item label="模板名称">
        <a-input v-model="templateForm.name" />
      </a-form-item>
      <a-form-item label="适用仓库">
        <a-select v-model="templateForm.depotIds" mode="multiple">
          <a-select-option v-for="depot in depotList" :key="depot.id" :value="depot.id">
            {{ depot.name }}
          </a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>
```

### 扩展报表功能

#### 1. 添加盈亏分析报表
```vue
<!-- 创建新页面：InventoryAnalysisReport.vue -->
<template>
  <a-card title="盘点盈亏分析">
    <div class="analysis-charts">
      <!-- 使用ECharts显示图表 -->
      <div id="profitLossChart" style="height: 400px;"></div>
    </div>
    
    <a-table :columns="analysisColumns" :dataSource="analysisData">
      <!-- 分析数据表格 -->
    </a-table>
  </a-card>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'InventoryAnalysisReport',
  mounted() {
    this.initChart();
  },
  methods: {
    initChart() {
      const chart = echarts.init(document.getElementById('profitLossChart'));
      const option = {
        title: { text: '盘点盈亏趋势' },
        xAxis: { type: 'category', data: this.chartData.dates },
        yAxis: { type: 'value' },
        series: [{
          data: this.chartData.values,
          type: 'line'
        }]
      };
      chart.setOption(option);
    }
  }
}
</script>
```

#### 2. 添加菜单配置
```sql
-- 添加报表菜单
INSERT INTO jsh_function (
    number, name, parent_number, url, component, state, sort, 
    enabled, type, push_btn, icon, delete_flag
) VALUES (
    '0903', '盘点分析', '09', '/inventory/inventory-analysis', 
    '/inventory/InventoryAnalysisReport', 0, '0903', 1, '电脑版', 
    '3', 'bar-chart', '0'
);
```

### 扩展API接口

#### 1. 添加统计接口
```java
// 在DepotController中添加新方法
@GetMapping("/inventory/statistics")
@ApiOperation("获取盘点统计数据")
public ResponseEntity<Map<String, Object>> getInventoryStatistics(
    @RequestParam String startDate,
    @RequestParam String endDate,
    @RequestParam(required = false) Long depotId) {
    
    Map<String, Object> result = new HashMap<>();
    
    // 统计盘点次数
    int totalCount = depotHeadService.getInventoryCount(startDate, endDate, depotId);
    
    // 统计盈亏金额
    BigDecimal totalDifference = depotHeadService.getInventoryDifference(startDate, endDate, depotId);
    
    result.put("totalCount", totalCount);
    result.put("totalDifference", totalDifference);
    
    return ResponseEntity.ok(result);
}
```

#### 2. 添加导出接口
```java
@GetMapping("/inventory/export")
@ApiOperation("导出盘点数据")
public void exportInventoryData(
    @RequestParam String startDate,
    @RequestParam String endDate,
    HttpServletResponse response) throws IOException {
    
    // 设置响应头
    response.setContentType("application/vnd.ms-excel");
    response.setHeader("Content-Disposition", "attachment; filename=inventory_data.xlsx");
    
    // 生成Excel文件
    List<InventoryExportVO> data = depotHeadService.getInventoryExportData(startDate, endDate);
    ExcelUtil.writeExcel(response.getOutputStream(), data, InventoryExportVO.class);
}
```

## 🔒 安全维护

### 权限审计

#### 定期权限检查
```sql
-- 检查异常权限分配
SELECT ub.key_id, r.name, ub.value, ub.update_time
FROM jsh_user_business ub
LEFT JOIN jsh_role r ON ub.key_id = r.id
WHERE ub.type = 'RoleFunctions' 
  AND ub.value LIKE '%[09]%'
  AND ub.update_time > DATE_SUB(NOW(), INTERVAL 7 DAY);

-- 检查未授权访问
SELECT u.username, u.login_name, u.last_login_time
FROM jsh_user u
WHERE u.last_login_time > DATE_SUB(NOW(), INTERVAL 1 DAY)
  AND u.id NOT IN (
    SELECT DISTINCT ub_user.value FROM jsh_user_business ub_user
    JOIN jsh_user_business ub_role ON ub_user.key_id = ub_role.key_id
    WHERE ub_user.type = 'UserRole' 
      AND ub_role.type = 'RoleFunctions'
      AND ub_role.value LIKE '%[09]%'
  );
```

### 数据安全

#### 敏感数据保护
```sql
-- 定期检查数据完整性
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN tenant_id IS NULL THEN 1 END) as missing_tenant,
    COUNT(CASE WHEN delete_flag IS NULL THEN 1 END) as missing_delete_flag
FROM jsh_depot_head 
WHERE type = '其它' AND sub_type = '盘点';

-- 检查数据异常
SELECT header_id, COUNT(*) as item_count
FROM jsh_depot_item 
WHERE header_id IN (
    SELECT id FROM jsh_depot_head 
    WHERE type = '其它' AND sub_type = '盘点'
)
GROUP BY header_id
HAVING COUNT(*) > 1000; -- 检查异常大量明细的单据
```

## 📊 监控告警

### 系统监控脚本
```bash
#!/bin/bash
# inventory_monitor.sh

# 检查服务状态
if ! docker ps | grep -q jsherp-boot; then
    echo "警告: jshERP后端服务未运行" | mail -s "系统告警" <EMAIL>
fi

# 检查数据库连接
if ! docker exec jsherp-mysql mysql -u jsh_user -p123456 -e "SELECT 1;" > /dev/null 2>&1; then
    echo "警告: 数据库连接失败" | mail -s "系统告警" <EMAIL>
fi

# 检查磁盘空间
DISK_USAGE=$(df -h | grep '/$' | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "警告: 磁盘使用率超过80%: ${DISK_USAGE}%" | mail -s "系统告警" <EMAIL>
fi

# 检查错误日志
ERROR_COUNT=$(docker logs jsherp-boot --since="1h" | grep -c "ERROR")
if [ $ERROR_COUNT -gt 10 ]; then
    echo "警告: 最近1小时内出现${ERROR_COUNT}个错误" | mail -s "系统告警" <EMAIL>
fi
```

### 性能监控
```bash
#!/bin/bash
# performance_monitor.sh

# 检查响应时间
RESPONSE_TIME=$(curl -w "%{time_total}" -o /dev/null -s http://localhost:9999/depotHead/list)
if (( $(echo "$RESPONSE_TIME > 3.0" | bc -l) )); then
    echo "警告: API响应时间过长: ${RESPONSE_TIME}秒" | mail -s "性能告警" <EMAIL>
fi

# 检查内存使用
MEMORY_USAGE=$(docker stats jsherp-boot --no-stream --format "{{.MemPerc}}" | sed 's/%//')
if (( $(echo "$MEMORY_USAGE > 80.0" | bc -l) )); then
    echo "警告: 内存使用率过高: ${MEMORY_USAGE}%" | mail -s "性能告警" <EMAIL>
fi
```

## 📝 维护日志

### 日志记录模板
```
维护日期: ____年__月__日
维护人员: ________________
维护类型: □ 日常维护 □ 问题修复 □ 功能扩展 □ 性能优化

维护内容:
1. ________________________________
2. ________________________________
3. ________________________________

发现问题:
1. ________________________________
2. ________________________________

解决方案:
1. ________________________________
2. ________________________________

测试结果:
□ 通过 □ 部分通过 □ 失败

备注:
____________________________________
____________________________________

下次维护建议:
____________________________________
____________________________________
```

## 📞 技术支持

### 联系信息
- **文档参考**：查看docs目录下的详细文档
- **问题排查**：使用verification_scripts.sql进行诊断
- **性能监控**：使用提供的监控脚本
- **功能扩展**：参考扩展示例进行开发

### 维护建议
- **定期备份**：建议每日备份数据库和关键代码
- **监控告警**：部署监控脚本，及时发现问题
- **文档更新**：维护操作后及时更新文档
- **版本管理**：使用Git管理代码变更

**维护指南版本**：v1.0
**最后更新时间**：2025年6月25日
