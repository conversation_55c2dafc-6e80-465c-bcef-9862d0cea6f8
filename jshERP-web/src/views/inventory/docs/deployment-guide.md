# jshERP 库存盘点模块部署指导手册

## 🎯 部署目标

将jshERP库存盘点模块成功集成到现有系统中，确保所有功能正常运行。

## ⏰ 预计时间

- **数据库配置**：5-10分钟
- **服务重启**：2-3分钟
- **功能验证**：10-15分钟
- **总计时间**：20-30分钟

## 📋 部署前检查

### 环境确认
```bash
# 检查Docker服务状态
docker ps | grep jsherp

# 检查端口占用
netstat -tlnp | grep -E ':(8080|9999|3306)'

# 检查磁盘空间
df -h
```

**预期结果**：
- ✅ jsherp-boot、jsherp-web、jsherp-mysql容器运行正常
- ✅ 端口8080、9999、3306正常监听
- ✅ 磁盘空间充足（至少1GB可用）

### 数据备份
```bash
# 备份数据库
docker exec jsherp-mysql mysqldump -u jsh_user -p123456 jsh_erp > backup_$(date +%Y%m%d_%H%M%S).sql

# 备份前端代码（可选）
cp -r jshERP-web/src/views jshERP-web/src/views_backup_$(date +%Y%m%d_%H%M%S)
```

## 🗄️ 第一步：数据库配置

### 1.1 访问phpMyAdmin
1. 打开浏览器访问：`http://localhost:8080/phpmyadmin`
2. 登录信息：
   - **服务器**：jsherp-mysql
   - **用户名**：jsh_user
   - **密码**：123456
   - **数据库**：jsh_erp

### 1.2 执行预检查
复制并执行以下SQL：
```sql
-- 检查当前菜单结构
SELECT number, name, parent_number FROM jsh_function 
WHERE delete_flag = '0' ORDER BY sort;

-- 检查编号09是否可用
SELECT CASE 
  WHEN EXISTS(SELECT 1 FROM jsh_function WHERE number = '09' AND delete_flag = '0')
  THEN '编号09已被使用，需要修改脚本'
  ELSE '编号09可用，可以继续'
END as status;

-- 查看当前用户角色
SELECT id, name, description FROM jsh_role WHERE delete_flag = '0';
```

**重要**：如果编号09已被使用，请修改配置脚本中的编号为其他可用编号（如10、11等）。

### 1.3 执行菜单配置
打开文件：`jshERP-web/src/views/inventory/database/inventory_menu_config.sql`

**步骤1：创建一级菜单**
```sql
INSERT INTO jsh_function (
    number, name, parent_number, url, component, state, sort, 
    enabled, type, push_btn, icon, delete_flag
) VALUES (
    '09', '盘点业务', '0', '/inventory', 'layouts/RouteView', 
    0, '0900', 1, '电脑版', '', 'database', '0'
);
```

**步骤2：创建二级菜单**
```sql
-- 盘点复盘
INSERT INTO jsh_function (
    number, name, parent_number, url, component, state, sort, 
    enabled, type, push_btn, icon, delete_flag
) VALUES (
    '0901', '盘点复盘', '09', '/inventory/inventory-check-list', 
    '/inventory/InventoryCheckList', 0, '0901', 1, '电脑版', 
    '1,2,3,5,6,7', 'unordered-list', '0'
);

-- 盘点录入
INSERT INTO jsh_function (
    number, name, parent_number, url, component, state, sort, 
    enabled, type, push_btn, icon, delete_flag
) VALUES (
    '0902', '盘点录入', '09', '/inventory/inventory-input-list', 
    '/inventory/InventoryInputList', 0, '0902', 1, '电脑版', 
    '1,2,3,4', 'edit', '0'
);
```

**步骤3：验证菜单创建**
```sql
SELECT number, name, parent_number, url, enabled 
FROM jsh_function 
WHERE number IN ('09', '0901', '0902') AND delete_flag = '0';
```

**预期结果**：应该看到3条记录，所有enabled字段都为1。

### 1.4 配置权限
**步骤1：查询角色ID**
```sql
SELECT ub.key_id, r.name as role_name, ub.value
FROM jsh_user_business ub
LEFT JOIN jsh_role r ON ub.key_id = r.id
WHERE ub.type = 'RoleFunctions' AND ub.delete_flag = '0';
```

**步骤2：分配权限**
根据查询结果，替换下面SQL中的`YOUR_ROLE_ID`为实际的角色ID：
```sql
-- 为租户角色分配权限（通常是ID为10的角色）
UPDATE jsh_user_business 
SET value = CONCAT(IFNULL(value, ''), '[09][0901][0902]')
WHERE type = 'RoleFunctions' 
  AND key_id = 'YOUR_ROLE_ID'  -- 替换为实际角色ID
  AND delete_flag = '0';

-- 为管理员角色分配权限（通常是ID为4的角色）
UPDATE jsh_user_business 
SET value = CONCAT(IFNULL(value, ''), '[09][0901][0902]')
WHERE type = 'RoleFunctions' 
  AND key_id = '4'  -- 管理员角色ID
  AND delete_flag = '0';
```

**步骤3：验证权限分配**
```sql
SELECT ub.key_id, r.name, ub.value
FROM jsh_user_business ub
LEFT JOIN jsh_role r ON ub.key_id = r.id
WHERE ub.type = 'RoleFunctions' 
  AND ub.value LIKE '%[09]%' 
  AND ub.delete_flag = '0';
```

**预期结果**：至少看到1条记录，value字段包含[09][0901][0902]。

## 🔄 第二步：服务重启

### 2.1 重启后端服务
```bash
# 重启jshERP后端
docker restart jsherp-boot

# 等待服务启动（约30-60秒）
sleep 60

# 检查服务状态
docker ps | grep jsherp-boot

# 检查服务日志
docker logs --tail 50 jsherp-boot
```

**预期结果**：
- ✅ 容器状态为"Up"
- ✅ 日志中无ERROR级别错误
- ✅ 看到"Started Application"相关信息

### 2.2 验证服务可用性
```bash
# 测试后端API
curl -s http://localhost:9999/health || echo "后端服务异常"

# 测试前端服务
curl -s http://localhost:8080 | grep -q "jshERP" && echo "前端服务正常" || echo "前端服务异常"
```

### 2.3 清除浏览器缓存
1. 打开Chrome浏览器
2. 按F12打开开发者工具
3. 右键点击刷新按钮
4. 选择"清空缓存并硬性重新加载"

## 🔐 第三步：登录验证

### 3.1 重新登录
1. 访问：`http://localhost:8080`
2. 如果已登录，请先退出
3. 使用管理员账户重新登录：
   - 用户名：`waterxi` 或 `admin`
   - 密码：您设置的管理员密码

### 3.2 检查菜单显示
登录成功后：
1. 查看左侧菜单栏
2. 寻找"盘点业务"菜单项
3. 点击展开查看子菜单

**预期结果**：
- ✅ 看到"盘点业务"一级菜单
- ✅ 展开后看到"盘点复盘"和"盘点录入"子菜单
- ✅ 菜单图标正常显示

**如果菜单不显示**：
1. 检查用户角色是否正确
2. 重新执行权限分配SQL
3. 清除浏览器缓存重新登录

## 🧪 第四步：功能验证

### 4.1 盘点复盘页面测试
1. 点击"盘点业务" → "盘点复盘"
2. 等待页面加载完成
3. 检查页面元素

**验证清单**：
- [ ] 页面正常加载，无白屏
- [ ] 搜索表单显示正常
- [ ] 操作按钮（新增、删除、刷新等）显示
- [ ] 表格结构正确
- [ ] 无JavaScript错误（F12查看控制台）

### 4.2 盘点录入页面测试
1. 点击"盘点业务" → "盘点录入"
2. 等待页面加载完成
3. 检查页面功能

**验证清单**：
- [ ] 页面正常加载
- [ ] 单据头信息区域显示
- [ ] 明细表格显示
- [ ] 操作按钮正常显示

### 4.3 新增功能测试
1. 在盘点复盘页面点击"新增"按钮
2. 检查弹窗是否正常打开
3. 测试表单功能

**验证清单**：
- [ ] 新增弹窗正常打开
- [ ] 表单字段显示完整
- [ ] 下拉选择框正常工作
- [ ] 保存按钮响应正常

### 4.4 导入功能测试
1. 在盘点录入页面点击"导入库存盘点数据"
2. 检查导入弹窗功能
3. 测试模板下载

**验证清单**：
- [ ] 导入弹窗正常打开
- [ ] 模板下载链接正常
- [ ] 文件上传组件显示
- [ ] 仓库选择下拉正常

## ✅ 第五步：最终验收

### 5.1 功能验收确认
请逐项确认以下功能：

#### 基础功能 ✅
- [ ] 菜单"盘点业务"正常显示
- [ ] 子菜单"盘点复盘"、"盘点录入"可访问
- [ ] 页面正常加载，无JavaScript错误
- [ ] 基础UI元素显示正常

#### 核心功能 ✅
- [ ] 新增盘点单弹窗正常打开
- [ ] 导入功能弹窗正常打开
- [ ] 表单验证正常工作
- [ ] 按钮响应正常

#### 用户体验 ✅
- [ ] 界面美观，符合jshERP风格
- [ ] 操作流畅，响应及时
- [ ] 错误提示清晰
- [ ] 移动端显示正常（可选测试）

### 5.2 性能验收
- [ ] 页面加载时间 < 3秒
- [ ] 菜单切换响应 < 1秒
- [ ] 弹窗打开响应 < 1秒
- [ ] 内存使用正常（无明显增长）

### 5.3 兼容性验收
- [ ] Chrome浏览器正常
- [ ] Firefox浏览器正常（可选）
- [ ] 不同分辨率显示正常

## 🐛 问题排查

### 常见问题及解决方案

#### 问题1：菜单不显示
**症状**：登录后看不到"盘点业务"菜单

**排查步骤**：
```sql
-- 检查菜单是否创建
SELECT * FROM jsh_function WHERE number = '09' AND delete_flag = '0';

-- 检查权限是否分配
SELECT * FROM jsh_user_business WHERE value LIKE '%[09]%' AND type = 'RoleFunctions';
```

**解决方案**：
1. 如果菜单不存在，重新执行菜单创建SQL
2. 如果权限未分配，重新执行权限分配SQL
3. 清除浏览器缓存重新登录

#### 问题2：页面404错误
**症状**：点击菜单后显示404页面

**排查步骤**：
```sql
-- 检查组件路径配置
SELECT number, name, component FROM jsh_function 
WHERE number IN ('0901', '0902') AND delete_flag = '0';
```

**解决方案**：
1. 确认组件路径正确：`/inventory/InventoryCheckList`
2. 检查Vue组件文件是否存在
3. 重启前端服务

#### 问题3：弹窗无法打开
**症状**：点击按钮后弹窗不显示

**排查步骤**：
1. 按F12打开开发者工具
2. 查看Console标签页的错误信息
3. 检查Network标签页的请求状态

**解决方案**：
1. 检查组件导入是否正确
2. 确认组件文件路径无误
3. 重新刷新页面

### 紧急回滚
如果出现严重问题，可以执行回滚：
```sql
-- 删除创建的菜单
UPDATE jsh_function SET delete_flag = '1' 
WHERE number IN ('09', '0901', '0902');

-- 重启服务
docker restart jsherp-boot
```

## 🎉 部署成功确认

当以下所有项目都确认无误时，表示部署成功：

### ✅ 基础确认
- [x] 数据库配置执行成功
- [x] 服务重启正常
- [x] 菜单正常显示
- [x] 页面正常访问

### ✅ 功能确认
- [x] 新增功能正常
- [x] 导入功能正常
- [x] 表单验证正常
- [x] 错误处理正常

### ✅ 性能确认
- [x] 响应时间满足要求
- [x] 内存使用正常
- [x] 无明显性能问题

**🎊 恭喜！jshERP库存盘点模块部署成功！**

## 📞 后续支持

部署完成后，如需技术支持：

1. **查看文档**：参考README.md和相关文档
2. **问题排查**：使用verification_scripts.sql诊断问题
3. **性能优化**：参考维护指南进行优化
4. **功能扩展**：按照扩展指南添加新功能

**部署完成时间**：____年__月__日 __:__

**部署人员**：________________

**验收确认**：________________
