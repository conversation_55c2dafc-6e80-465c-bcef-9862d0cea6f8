/* 日历排班组件样式 */

/* ==================== 日历主体样式 ==================== */

.schedule-calendar {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.calendar-title {
  display: flex;
  align-items: center;
  gap: 16px;
}

.month-title {
  font-size: 20px;
  font-weight: 600;
  color: #262626;
  margin: 0 16px;
}

.calendar-actions {
  display: flex;
  gap: 12px;
}

.calendar-body {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

/* ==================== 日历单元格样式 ==================== */

.schedule-calendar-main {
  border: none;
}

.schedule-calendar-main .ant-picker-calendar-full .ant-picker-panel {
  background: white;
}

.schedule-calendar-main .ant-picker-calendar-full .ant-picker-calendar-date {
  border: 1px solid #f0f0f0;
  height: 120px;
  padding: 8px;
  position: relative;
  transition: all 0.3s ease;
}

.schedule-calendar-main .ant-picker-calendar-full .ant-picker-calendar-date:hover {
  background: #f5f5f5;
  border-color: #d9d9d9;
}

.calendar-date-cell {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.date-number {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.assignments-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
  overflow: hidden;
}

.assignment-tag {
  background: #1890ff;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.assignment-tag:hover {
  opacity: 0.8;
  transform: translateY(-1px);
}

.add-assignment-btn {
  position: absolute;
  bottom: 4px;
  right: 4px;
  width: 20px;
  height: 20px;
  background: #52c41a;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  transition: all 0.2s ease;
  font-size: 12px;
}

.calendar-date-cell:hover .add-assignment-btn {
  opacity: 1;
}

.add-assignment-btn:hover {
  background: #389e0d;
  transform: scale(1.1);
}

/* ==================== 日期状态样式 ==================== */

.calendar-date-cell.today .date-number {
  background: #1890ff;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.calendar-date-cell.weekend .date-number {
  color: #ff4d4f;
}

.calendar-date-cell.has-assignments {
  background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
}

.calendar-date-cell.selected-for-batch {
  background: #e6f7ff;
  border-color: #1890ff !important;
}

.schedule-calendar-main .ant-picker-calendar-full .ant-picker-calendar-date.selected-for-batch {
  background: #e6f7ff;
  border-color: #1890ff !important;
}

/* ==================== 月视图单元格样式 ==================== */

.calendar-month-cell {
  text-align: center;
  padding: 16px;
}

.month-name {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8px;
}

.month-stats {
  font-size: 12px;
  color: #666;
}

/* ==================== 弹窗样式 ==================== */

.selected-dates {
  min-height: 32px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 8px;
  background: #fafafa;
  margin-bottom: 8px;
}

.selected-dates .ant-tag {
  margin: 2px;
}

.tip {
  color: #666;
  font-size: 12px;
  margin: 0;
}

.assignment-detail .ant-descriptions {
  margin-bottom: 16px;
}

.assignment-actions {
  text-align: right;
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

/* ==================== 响应式设计 ==================== */

@media (max-width: 1366px) {
  .schedule-calendar-main .ant-picker-calendar-full .ant-picker-calendar-date {
    height: 100px;
    padding: 6px;
  }
  
  .date-number {
    font-size: 14px;
  }
  
  .assignment-tag {
    font-size: 11px;
    padding: 1px 4px;
  }
}

@media (max-width: 768px) {
  .schedule-calendar {
    padding: 16px;
  }
  
  .calendar-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .calendar-title {
    justify-content: center;
  }
  
  .calendar-actions {
    justify-content: center;
  }
  
  .schedule-calendar-main .ant-picker-calendar-full .ant-picker-calendar-date {
    height: 80px;
    padding: 4px;
  }
  
  .date-number {
    font-size: 12px;
  }
  
  .assignment-tag {
    font-size: 10px;
    padding: 1px 3px;
  }
  
  .add-assignment-btn {
    width: 16px;
    height: 16px;
    font-size: 10px;
  }
}

/* ==================== 动画效果 ==================== */

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.calendar-date-cell {
  animation: fadeIn 0.3s ease;
}

.assignment-tag {
  animation: fadeIn 0.2s ease;
}

/* ==================== 主题适配 ==================== */

.schedule-calendar {
  --primary-color: #3B82F6;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #ff4d4f;
  --text-color: #262626;
  --text-color-secondary: #666;
  --border-color: #d9d9d9;
  --background-color: #fafafa;
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .schedule-calendar {
    --primary-color: #3B82F6;
    --success-color: #52c41a;
    --warning-color: #faad14;
    --error-color: #ff4d4f;
    --text-color: #ffffff;
    --text-color-secondary: #a6a6a6;
    --border-color: #434343;
    --background-color: #1f1f1f;
  }
  
  .schedule-calendar {
    background: #141414;
    color: var(--text-color);
  }
  
  .calendar-header,
  .calendar-body {
    background: #1f1f1f;
    border-color: var(--border-color);
  }
  
  .schedule-calendar-main .ant-picker-calendar-full .ant-picker-calendar-date {
    background: #1f1f1f;
    border-color: var(--border-color);
    color: var(--text-color);
  }
  
  .schedule-calendar-main .ant-picker-calendar-full .ant-picker-calendar-date:hover {
    background: #262626;
  }
}

/* ==================== 打印样式 ==================== */

@media print {
  .schedule-calendar {
    background: white;
    padding: 0;
  }
  
  .calendar-header {
    box-shadow: none;
    border: 1px solid #000;
  }
  
  .calendar-actions {
    display: none;
  }
  
  .add-assignment-btn {
    display: none;
  }
  
  .schedule-calendar-main .ant-picker-calendar-full .ant-picker-calendar-date {
    border-color: #000;
    background: white;
  }
  
  .assignment-tag {
    background: #f0f0f0 !important;
    color: #000 !important;
    border: 1px solid #000;
  }
}
