# jshERP Docker Compose 开发环境配置
# 用于本地开发，支持源码挂载和热重载

version: '3.8'

services:
  # MySQL 数据库服务 (与生产环境相同)
  jsherp-mysql:
    image: mysql:${MYSQL_VERSION:-5.7.33}
    container_name: jsherp-mysql-dev
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-123456}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-jsh_erp}
      MYSQL_USER: ${MYSQL_USER:-jsh_user}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-123456}
      TZ: ${TIMEZONE:-Asia/Shanghai}
    command: [
      '--character-set-server=utf8mb4',
      '--collation-server=utf8mb4_unicode_ci',
      '--default-time-zone=+08:00',
      '--max_connections=1000',
      '--innodb_buffer_pool_size=256M',
      '--innodb_log_file_size=64M',
      '--innodb_flush_log_at_trx_commit=1',
      '--sync_binlog=1'
    ]
    ports:
      - "${HOST_MYSQL_PORT:-3306}:3306"
    volumes:
      - ./volumes/mysql:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d:ro
      - ./docker/mysql/my.cnf:/etc/mysql/conf.d/my.cnf:ro
    networks:
      - jsherp_network_dev
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Redis 缓存服务 (与生产环境相同)
  jsherp-redis:
    image: redis:${REDIS_VERSION:-6.2.1-alpine}
    container_name: jsherp-redis-dev
    restart: unless-stopped
    environment:
      TZ: ${TIMEZONE:-Asia/Shanghai}
    command: redis-server --requirepass ${REDIS_PASSWORD:-1234abcd} --appendonly yes
    ports:
      - "${HOST_REDIS_PORT:-6379}:6379"
    volumes:
      - ./volumes/redis:/data
    networks:
      - jsherp_network_dev
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      timeout: 10s
      retries: 5

  # 后端开发服务 (Maven + SpringBoot DevTools)
  jsherp-backend-dev:
    build:
      context: .
      dockerfile: docker/backend/Dockerfile.dev
      target: development
    image: jsherp-backend-dev:latest
    container_name: jsherp-backend-dev
    restart: unless-stopped
    environment:
      SPRING_PROFILES_ACTIVE: dev
      SPRING_DATASOURCE_URL: ******************************/${MYSQL_DATABASE:-jsh_erp}?useUnicode=true&characterEncoding=utf8&useCursorFetch=true&defaultFetchSize=500&allowMultiQueries=true&rewriteBatchedStatements=true&useSSL=false&serverTimezone=Asia/Shanghai
      SPRING_DATASOURCE_USERNAME: ${MYSQL_USER:-jsh_user}
      SPRING_DATASOURCE_PASSWORD: ${MYSQL_PASSWORD:-123456}
      SPRING_REDIS_HOST: jsherp-redis
      SPRING_REDIS_PORT: 6379
      SPRING_REDIS_PASSWORD: ${REDIS_PASSWORD:-1234abcd}
      FILE_UPLOAD_PATH: ${FILE_UPLOAD_PATH:-/opt/jshERP/upload}
      TZ: ${TIMEZONE:-Asia/Shanghai}
      JVM_OPTS: ${JVM_OPTS:--Xms256m -Xmx512m -XX:+UseG1GC}
      SPRING_DEVTOOLS_RESTART_ENABLED: true
    ports:
      - "${HOST_BACKEND_PORT:-9999}:9999"
      - "35729:35729"  # LiveReload 端口
    volumes:
      - ./jshERP-boot:/app:cached  # 源码挂载，支持热重载
      - ./volumes/uploads:${FILE_UPLOAD_PATH:-/opt/jshERP/upload}
    depends_on:
      jsherp-mysql:
        condition: service_healthy
      jsherp-redis:
        condition: service_healthy
    networks:
      - jsherp_network_dev

  # 前端开发服务 (基于 Node.js 基础镜像)
  jsherp-frontend-dev:
    image: node:16-alpine
    container_name: jsherp-frontend-dev
    restart: unless-stopped
    working_dir: /app
    command: sh -c "npm config set registry https://registry.npmmirror.com/ && yarn install && yarn serve --host 0.0.0.0 --port 3000"
    environment:
      NODE_ENV: development
      VUE_APP_API_BASE_URL: http://jsherp-backend-dev:9999/jshERP-boot
      CHOKIDAR_USEPOLLING: true  # 启用文件监听（Docker for Mac/Windows）
      TZ: ${TIMEZONE:-Asia/Shanghai}
    ports:
      - "8080:3000"
    volumes:
      - ./jshERP-web:/app:cached  # 源码挂载，支持热重载
      - /app/node_modules         # 防止覆盖 node_modules
    networks:
      - jsherp_network_dev
    depends_on:
      - jsherp-backend-dev

  # Nginx 开发代理服务
  jsherp-nginx-dev:
    build:
      context: .
      dockerfile: docker/nginx/Dockerfile
    image: jsherp-nginx-dev:latest
    container_name: jsherp-nginx-dev
    restart: unless-stopped
    environment:
      TZ: ${TIMEZONE:-Asia/Shanghai}
    ports:
      - "${HOST_NGINX_PORT:-8000}:80"
    volumes:
      - ./docker/nginx/dev.conf:/etc/nginx/conf.d/default.conf:ro
      - ./volumes/uploads:/opt/jshERP/upload:ro
    depends_on:
      - jsherp-backend-dev
      - jsherp-frontend-dev
    networks:
      - jsherp_network_dev

# 开发环境网络配置
networks:
  jsherp_network_dev:
    name: jsherp_network_dev
    driver: bridge

# 开发环境卷配置
volumes:
  mysql_data_dev:
    driver: local
  redis_data_dev:
    driver: local
  uploads_data_dev:
    driver: local