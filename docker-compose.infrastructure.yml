# jshERP 基础设施服务配置
# 仅包含数据库、缓存和代理服务，前后端服务在宿主机运行

version: '3.8'

services:
  # MySQL 数据库服务
  jsherp-mysql:
    image: mysql:${MYSQL_VERSION:-5.7.33}
    container_name: jsherp-mysql-local
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-123456}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-jsh_erp}
      MYSQL_USER: ${MYSQL_USER:-jsh_user}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-123456}
      TZ: ${TIMEZONE:-Asia/Shanghai}
    command: [
      '--character-set-server=utf8mb4',
      '--collation-server=utf8mb4_unicode_ci',
      '--default-time-zone=+08:00',
      '--max_connections=1000',
      '--innodb_buffer_pool_size=256M',
      '--innodb_log_file_size=64M',
      '--innodb_flush_log_at_trx_commit=1',
      '--sync_binlog=1'
    ]
    ports:
      - "${HOST_MYSQL_PORT:-3306}:3306"
    volumes:
      - ./volumes/mysql:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d:ro
      - ./docker/mysql/my.cnf:/etc/mysql/conf.d/my.cnf:ro
    networks:
      - jsherp_network_local
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Redis 缓存服务
  jsherp-redis:
    image: redis:${REDIS_VERSION:-6.2.1-alpine}
    container_name: jsherp-redis-local
    restart: unless-stopped
    environment:
      TZ: ${TIMEZONE:-Asia/Shanghai}
    command: redis-server --requirepass ${REDIS_PASSWORD:-1234abcd} --appendonly yes
    ports:
      - "${HOST_REDIS_PORT:-6379}:6379"
    volumes:
      - ./volumes/redis:/data
    networks:
      - jsherp_network_local
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      timeout: 10s
      retries: 5

  # Nginx 代理服务（代理到宿主机服务）
  jsherp-nginx-local:
    build:
      context: .
      dockerfile: docker/nginx/Dockerfile
    image: jsherp-nginx-local:latest
    container_name: jsherp-nginx-local
    restart: unless-stopped
    environment:
      TZ: ${TIMEZONE:-Asia/Shanghai}
    ports:
      - "${HOST_NGINX_PORT:-8000}:80"
    volumes:
      - ./docker/nginx/local.conf:/etc/nginx/conf.d/default.conf:ro
      - ./volumes/uploads:/opt/jshERP/upload:ro
    networks:
      - jsherp_network_local
    extra_hosts:
      - "host.docker.internal:host-gateway"  # 允许容器访问宿主机服务

# 本地开发网络配置
networks:
  jsherp_network_local:
    name: jsherp_network_local
    driver: bridge

# 本地开发卷配置  
volumes:
  mysql_data_local:
    driver: local
  redis_data_local:
    driver: local
  uploads_data_local:
    driver: local 