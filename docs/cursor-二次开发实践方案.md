好的，遵命。

我理解您需要一份正式、完整且详尽的方案文档，用于最终的决策和存档。这份文档将作为后续所有开发工作的纲领性文件。

在您正式批准此方案之前，我将不会编写任何代码。

以下是为您准备的《聆花文化ERP扩展模块开发方案》。

---

# **聆花文化ERP扩展模块开发方案**

**版本：** 1.0 (最终版)
**日期：** 2025-06-18
**编制：** AI 助手

## 1.0 项目概述

本项目旨在为“聆花文化”在现有 `jshERP` 系统的基础上，开发一套定制化的业务功能模块。为确保主系统的稳定性、安全性及未来升级的兼容性，本次开发将严格遵循 **“核心不动，外挂扩展”** 的核心设计原则。所有新增功能将作为一个独立的业务插件（`linghua-business-plugin`）进行开发，通过 API 与主系统进行数据和业务交互，实现对现有系统的“最小化侵入”。

## 2.0 核心设计理念与技术架构

### 2.1 设计理念
- **模块独立：** 新增功能（生产、活动、薪酬等）在物理和逻辑上都将与 `jshERP` 核心业务代码解耦，封装在独立的 `linghua-business-plugin` 模块中。
- **API 驱动：** 插件模块将通过定义清晰的 RESTful API 接口与前端及 `jshERP` 核心系统进行通信。
- **无缝集成：** 在前端界面上，新功能将以新增菜单和嵌入式卡片/标签页的形式与现有系统融合，为用户提供统一、连贯的操作体验。

### 2.2 技术栈
为确保技术统一和维护便利，扩展模块将沿用 `jshERP` 的现有技术体系。
- **后端：** Spring Boot + Mybatis-Plus
- **前端：** Vue.js + Element UI
- **数据库：** MySQL

### 2.3 数据库设计
本次开发将基于已设计并确认的数据库迁移文件 `V3.5.1__add_workshop_and_expansion_modules.sql`。该文件包含了“生产工坊”、“活动排班”、“薪酬核算”及“核心功能扩展”四大模块所需的全部数据表结构，是本次后端开发的数据基础。

### 2.4 模块划分
本次开发内容主要分为以下四个核心业务模块：
1.  **生产工坊模块 (Workshop)**
2.  **活动与排班模块 (Activity & Schedule)**
3.  **薪酬核算中心 (Payroll)**
4.  **核心功能扩展模块 (Core Extension)**

---

## 3.0 模块功能详细规划

### 3.1 生产工坊模块 (Workshop)

- **后端规划:**
    - **实体类:** `WorkshopOrder.java`, `WorkshopBom.java`, `WorkshopLog.java`。
    - **核心业务逻辑:**
        - **工单管理：** 实现从销售订单到生产工单的自动或手动创建，管理工单生命周期（待生产、生产中、已完工）。
        - **物料清单(BOM)管理：** 关联工单与所需原料，自动计算计划用料。
        - **计件报工：** 员工提交报工记录（含完成数量、图片等），系统自动计算对应的人工成本。
    - **API 接口 (示例):**
        - `POST /linghua/workshop/order`: 创建生产工单。
        - `GET /linghua/workshop/order/list`: 获取工单列表（支持分页、筛选）。
        - `PUT /linghua/workshop/order/{id}/status`: 更新工单状态。
        - `POST /linghua/workshop/log`: 员工提交报工记录。
- **前端规划:**
    - **主要视图:**
        - `WorkshopOrderList.vue`: 生产工单管理页面，以列表形式展示所有工单，提供强大的搜索和筛选功能。
        - `WorkshopOrderDetail.vue`: 工单详情页，用于创建、编辑工单，并可在此页面查看物料清单、上传报工记录及图片。

### 3.2 活动与排班模块 (Activity & Schedule)

- **后端规划:**
    - **实体类:** `Activity.java`, `ActivityResource.java`, `ShiftSchedule.java`。
    - **核心业务逻辑:**
        - **活动管理:** 创建、编辑和查询团建活动，包括时间、地点、客户、预算等信息。
        - **资源调度:** 管理活动所需的资源（如讲师、助理），并自动检测时间冲突。
        - **排班管理:** 为指定员工（如咖啡店员）按天和时间段进行排班。
    - **API 接口 (示例):**
        - `POST /linghua/activity`: 创建新活动。
        - `GET /linghua/activity/calendar`: 按月或周获取活动日历视图数据。
        - `POST /linghua/schedule`: 创建排班记录。
        - `GET /linghua/schedule/list`: 获取排班表。
- **前端规划:**
    - **主要视图:**
        - `ActivityCalendar.vue`: 以日历视图展示所有活动，支持点击创建和查看详情。
        - `ShiftScheduleBoard.vue`: 可视化排班看板，支持拖拽、点击等方式快速为员工排班。

### 3.3 薪酬核算中心 (Payroll)

- **后端规划:**
    - **实体类:** `PayrollSheet.java`, `PayrollDetail.java`。
    - **核心业务逻辑:**
        - **数据自动归集：** 定时（如每月1号）自动从生产模块、活动模块、销售提成等处拉取数据，生成薪酬明细。
        - **薪酬单生成与计算：** 汇总所有明细，结合员工基本工资，自动生成月度薪酬单。
        - **审核与支付流程：** 提供薪酬单的审核、确认及状态变更功能。
    - **API 接口 (示例):**
        - `GET /linghua/payroll/sheet?period=YYYY-MM`: 获取指定月份的薪酬单一览。
        - `GET /linghua/payroll/sheet/{id}`: 获取单个员工的薪酬单详情。
        - `PUT /linghua/payroll/sheet/{id}/approve`: 审核通过薪酬单。
- **前端规划:**
    - **主要视图:**
        - `PayrollDashboard.vue`: 薪酬管理主页，可按月份切换，查看所有员工的薪酬汇总列表。
        - `PayrollDetailModal.vue`: 弹窗或独立页面，展示单张薪酬单的详细构成，如计件工资、活动费用、提成等。

### 3.4 核心功能扩展模块 (Core Extension)

- **后端规划:**
    - **实体类:** `MaterialPlus.java`, `CustomerPlus.java`。
    - **核心业务逻辑:**
        - **数据伴生：** 提供与 `jsh_material` 和 `jsh_supplier` 表一对一关联的数据存储能力。
        - **数据读写：** 提供对产品扩展信息（匠人、故事）和客户扩展信息（渠道类型、押金）的增删改查服务。
    - **API 接口 (示例):**
        - `GET /linghua/material/plus/{materialId}`: 获取产品的扩展信息。
        - `POST /linghua/material/plus`: 创建或更新产品的扩展信息。
        - `GET /linghua/customer/plus/{customerId}`: 获取客户的扩展信息。
- **前端规划:**
    - **集成方式（非独立页面）:**
        - **产品管理:** 在 `jshERP` 原有的产品信息弹窗或页面中，新增一个“品牌信息”标签页，用于展示和编辑 `MaterialPlus` 的内容。
        - **客户管理:** 在 `jshERP` 原有的客户信息（供应商管理）页面，新增一个“渠道信息”标签页，用于展示和编辑 `CustomerPlus` 的内容。

---

## 4.0 开发实施计划

本项目将分阶段进行，确保每个阶段都有明确的交付成果。

### **第一阶段：后端服务开发 (预计 10 个工作日)**
1.  搭建 `linghua-business-plugin` 模块的基础环境和依赖。
2.  完成 **生产工坊模块** 的所有后端实体、Mapper、Service 和 Controller 开发。
3.  完成 **活动与排班模块** 的所有后端开发。
4.  完成 **薪酬核算中心** 的所有后端开发。
5.  完成 **核心功能扩展模块** 的所有后端开发。
6.  编写单元测试，确保各 API 接口功能正确。

### **第二阶段：前端集成与开发 (预计 8 个工作日)**
1.  在前端项目中配置新模块的菜单项和页面路由。
2.  开发 **生产工坊模块** 的 `WorkshopOrderList` 和 `WorkshopOrderDetail` 页面，并与后端 API 对接。
3.  开发 **活动与排班模块** 的 `ActivityCalendar` 和 `ShiftScheduleBoard` 页面。
4.  开发 **薪酬核算中心** 的 `PayrollDashboard` 和 `PayrollDetailModal` 页面。
5.  在现有的产品和客户管理页面中，完成 **核心功能扩展模块** 的前端嵌入和对接工作。

### **第三阶段：联调测试与交付 (预计 5 个工作日)**
1.  进行全面的前后端联调，确保所有业务流程通畅无误。
2.  修复测试过程中发现的各类 Bug。
3.  邀请用户进行验收测试 (UAT)。
4.  根据验收反馈进行最终调整。
5.  准备交付文档，完成最终部署。

---

## 5.0 总结

本方案详细阐述了“聆花文化”ERP扩展模块的建设目标、技术架构、功能规划和实施步骤。方案严格遵循“核心不动，外挂扩展”的原则，在满足定制化需求的同时，最大限度地保障了原系统的稳定与安全。

**请审阅此方案。待您批准后，即可启动第一阶段的开发工作。**