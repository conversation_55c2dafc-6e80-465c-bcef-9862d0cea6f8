# 掐丝珐琅馆综合管理模块前端技术调研报告

## 📋 调研概述

本报告基于jshERP现有前端架构，为"掐丝珐琅馆综合管理模块"的前端开发提供技术指导和设计规范。

---

## 🏗️ jshERP前端架构分析

### 核心技术栈
- **Vue.js**: 2.7.16 (最新稳定版)
- **UI框架**: Ant Design Vue 1.5.2
- **状态管理**: Vuex 3.1.0
- **路由管理**: Vue Router 3.0.1
- **样式预处理**: Less 3.9.0
- **图表库**: Viser-vue 2.4.4 (基于AntV/G2)
- **HTTP客户端**: Axios 0.18.0

### 项目结构
```
jshERP-web/src/
├── api/                    # API接口定义
├── components/             # 通用组件
│   ├── jeecg/             # JEECG组件库
│   ├── layouts/           # 布局组件
│   ├── chart/             # 图表组件
│   └── table/             # 表格组件
├── views/                  # 页面视图
├── store/                  # Vuex状态管理
├── utils/                  # 工具函数
├── mixins/                 # 混入
├── assets/less/           # 样式文件
└── router/                # 路由配置
```

---

## 🎨 设计规范分析

### 主题配置
```javascript
// defaultSettings.js
export default {
  primaryColor: '#1890FF',    // 需要改为 #3B82F6
  navTheme: 'light',          // 浅色主题
  layout: 'sidemenu',         // 侧边菜单布局
  contentWidth: 'Fixed',      // 固定宽度内容
  fixedHeader: true,          // 固定头部
  fixSiderbar: true,          // 固定侧边栏
  multipage: true             // 多页签模式
}
```

### 响应式断点
```less
// Ant Design Vue 断点系统
@screen-sm: 576px;    // 小屏幕/平板
@screen-md: 768px;    // 中等屏幕/桌面
@screen-lg: 992px;    // 大屏幕/宽桌面
@screen-xl: 1200px;   // 超大屏幕/全高清
@screen-xxl: 1600px;  // 超超大屏幕/大桌面
```

### 栅格系统使用
```vue
<!-- 标准响应式布局 -->
<a-row :gutter="24">
  <a-col :sm="24" :md="12" :xl="4">
    <!-- 内容 -->
  </a-col>
</a-row>
```

### 间距规范
- **卡片间距**: `gutter="24"`, `marginBottom="12px"`
- **按钮间距**: `margin="0 8px 8px 0"`
- **表格内边距**: `padding="15px"` (上下)
- **Modal高度**: `90%` 视窗高度
- **页面内边距**: `padding="24px"`

---

## 🧩 核心组件模式

### 1. JeecgListMixin标准模式
```javascript
// 标准列表页面开发模式
import { JeecgListMixin } from '@/mixins/JeecgListMixin'

export default {
  mixins: [JeecgListMixin],
  data() {
    return {
      description: '页面描述',
      columns: [],              // 表格列定义
      url: {
        list: '/api/list',      // 查询接口
        delete: '/api/delete',  // 删除接口
        deleteBatch: '/api/deleteBatch'
      },
      queryParam: {},           // 查询参数
      dataSource: [],           // 数据源
      ipagination: {            // 分页配置
        current: 1,
        pageSize: 10,
        total: 0
      }
    }
  }
}
```

### 2. 卡片组件模式
```vue
<!-- ChartCard 数据卡片 -->
<chart-card :loading="loading" title="标题">
  <a-tooltip title="提示信息" slot="action">
    <a-icon type="info-circle-o" />
  </a-tooltip>
  <head-info :content="数据内容"></head-info>
</chart-card>
```

### 3. Modal弹窗模式
```vue
<a-modal
  :title="title"
  :width="800"
  :visible="visible"
  :confirmLoading="confirmLoading"
  @ok="handleOk"
  @cancel="handleCancel">
  <a-form :form="form">
    <!-- 表单内容 -->
  </a-form>
</a-modal>
```

---

## 📱 响应式设计最佳实践

### 设备适配策略
```javascript
// mixinDevice 设备检测
import { mixinDevice } from '@/utils/mixin.js'

export default {
  mixins: [mixinDevice],
  methods: {
    // 检测设备类型
    isDesktop() { return this.device === 'desktop' },
    isMobile() { return this.device === 'mobile' }
  }
}
```

### 响应式布局示例
```vue
<template>
  <!-- 桌面端：4列布局 -->
  <a-col :sm="24" :md="12" :xl="6" v-if="isDesktop()">
    <chart-card />
  </a-col>
  
  <!-- 移动端：单列布局 -->
  <a-col :span="24" v-if="isMobile()">
    <chart-card />
  </a-col>
</template>
```

---

## 🎯 Ant Design Vue 1.5.2 组件使用指南

### 响应式断点系统
```javascript
// 标准断点配置
{
  xs: '480px',    // 超小屏幕
  sm: '576px',    // 小屏幕/平板
  md: '768px',    // 中等屏幕/桌面
  lg: '992px',    // 大屏幕/宽桌面
  xl: '1200px',   // 超大屏幕/全高清
  xxl: '1600px',  // 超超大屏幕/大桌面
  xxxl: '2000px'  // 超超超大屏幕
}
```

### 主题定制配置
```vue
<!-- 使用ConfigProvider定制主题 -->
<a-config-provider
  :theme="{
    token: {
      colorPrimary: '#3B82F6',  // 主色调
      borderRadius: 6,          // 圆角
      colorBgContainer: '#FFFFFF'
    }
  }">
  <a-button type="primary">按钮</a-button>
</a-config-provider>
```

### 栅格布局系统
```vue
<!-- 响应式栅格布局 -->
<a-row :gutter="24">
  <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
    <a-card>卡片内容</a-card>
  </a-col>
</a-row>
```

### 常用组件清单

#### 布局组件
- **`a-layout`**: 页面整体布局
- **`a-layout-header`**: 页面头部
- **`a-layout-sider`**: 侧边栏
- **`a-layout-content`**: 主内容区
- **`a-row` / `a-col`**: 栅格系统

#### 导航组件
- **`a-menu`**: 导航菜单
- **`a-breadcrumb`**: 面包屑导航
- **`a-tabs`**: 标签页
- **`a-pagination`**: 分页器

#### 数据录入组件
- **`a-form`**: 表单容器
- **`a-form-item`**: 表单项
- **`a-input`**: 输入框
- **`a-select`**: 选择器
- **`a-date-picker`**: 日期选择器
- **`a-upload`**: 文件上传

#### 数据展示组件
- **`a-table`**: 表格
- **`a-card`**: 卡片
- **`a-tag`**: 标签
- **`a-badge`**: 徽标
- **`a-descriptions`**: 描述列表
- **`a-calendar`**: 日历

#### 反馈组件
- **`a-modal`**: 对话框
- **`a-message`**: 全局提示
- **`a-notification`**: 通知提醒
- **`a-spin`**: 加载中
- **`a-result`**: 结果页

#### 其他组件
- **`a-button`**: 按钮
- **`a-icon`**: 图标
- **`a-tooltip`**: 文字提示
- **`a-popover`**: 气泡卡片

### 表格组件最佳实践
```vue
<a-table
  :columns="columns"
  :data-source="dataSource"
  :pagination="pagination"
  :loading="loading"
  :row-selection="rowSelection"
  :scroll="{ x: 1200, y: 400 }"
  size="middle"
  bordered
  @change="handleTableChange">

  <!-- 自定义列渲染 -->
  <template #status="{ record }">
    <a-tag :color="record.status === 'active' ? 'green' : 'red'">
      {{ record.status === 'active' ? '启用' : '禁用' }}
    </a-tag>
  </template>

  <!-- 操作列 -->
  <template #action="{ record }">
    <a-space>
      <a-button type="link" @click="handleEdit(record)">编辑</a-button>
      <a-button type="link" danger @click="handleDelete(record)">删除</a-button>
    </a-space>
  </template>
</a-table>
```

### 表单组件最佳实践
```vue
<a-form
  :form="form"
  :label-col="{ span: 6 }"
  :wrapper-col="{ span: 18 }"
  @submit="handleSubmit">

  <a-form-item label="用户名" name="username" :rules="[{ required: true, message: '请输入用户名' }]">
    <a-input v-model:value="form.username" placeholder="请输入用户名" />
  </a-form-item>

  <a-form-item label="状态" name="status">
    <a-select v-model:value="form.status" placeholder="请选择状态">
      <a-select-option value="active">启用</a-select-option>
      <a-select-option value="inactive">禁用</a-select-option>
    </a-select>
  </a-form-item>

  <a-form-item :wrapper-col="{ offset: 6, span: 18 }">
    <a-space>
      <a-button type="primary" html-type="submit">提交</a-button>
      <a-button @click="handleReset">重置</a-button>
    </a-space>
  </a-form-item>
</a-form>
```

### 卡片组件最佳实践
```vue
<a-card
  title="数据统计"
  :bordered="false"
  :body-style="{ padding: '24px' }"
  :head-style="{ borderBottom: '1px solid #f0f0f0' }">

  <!-- 额外操作 -->
  <template #extra>
    <a-button type="link">更多</a-button>
  </template>

  <!-- 卡片内容 -->
  <a-row :gutter="16">
    <a-col :span="8">
      <a-statistic title="总销售额" :value="112893" prefix="¥" />
    </a-col>
    <a-col :span="8">
      <a-statistic title="订单数" :value="1128" />
    </a-col>
    <a-col :span="8">
      <a-statistic title="转化率" :value="11.28" suffix="%" />
    </a-col>
  </a-row>
</a-card>
```

---

## 🔧 开发工具和配置

### ESLint配置
```javascript
// .eslintrc.js 关键规则
rules: {
  "vue/max-attributes-per-line": [2, { "singleline": 5 }],
  "vue/attribute-hyphenation": 0,
  "vue/html-self-closing": 0,
  "no-console": 0,
  "indent": [1, 4]
}
```

### 构建配置
- **构建工具**: Vue CLI 3.3.0
- **代码分割**: 支持路由级别的代码分割
- **压缩**: 生产环境自动压缩
- **兼容性**: 支持现代浏览器 (> 1%, last 2 versions)

---

## 📊 图表组件使用

### Viser-vue图表
```vue
<template>
  <bar 
    title="销售统计" 
    :height="barHeight" 
    :yaxisText="yaxisText" 
    :dataSource="salePriceData"/>
</template>

<script>
import Bar from '@/components/chart/Bar'

export default {
  components: { Bar },
  data() {
    return {
      barHeight: 300,
      yaxisText: '金额',
      salePriceData: []
    }
  }
}
</script>
```

---

## 🚀 性能优化建议

### 1. 路由懒加载
```javascript
component: () => import('@/views/cloisonne/Dashboard')
```

### 2. 组件按需引入
```javascript
import { Button, Table, Card } from 'ant-design-vue'
```

### 3. 图片懒加载
```vue
<img v-lazy="imageSrc" />
```

### 4. 虚拟滚动
```vue
<a-table :scroll="{ y: 400 }" />
```

---

## 📝 开发规范建议

### 1. 组件命名
- 使用PascalCase命名组件
- 文件名与组件名保持一致
- 组件名应该具有描述性

### 2. 样式规范
- 使用Less预处理器
- 遵循BEM命名规范
- 使用scoped样式避免污染

### 3. 代码组织
- 单个组件文件不超过300行
- 复杂逻辑抽取为mixins或utils
- 保持组件的单一职责原则

---

## 🎯 下一步计划

1. **设计页面布局和交互原型**
2. **制定组件开发优先级**
3. **建立代码规范和质量标准**
4. **开始核心组件开发**

---

*报告生成时间: 2025-01-22*
*技术栈版本: Vue.js 2.7.16 + Ant Design Vue 1.5.2*
