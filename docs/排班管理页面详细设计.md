# 掐丝珐琅馆排班管理页面详细设计

## 📋 设计概述

排班管理页面是掐丝珐琅馆综合管理模块的核心功能之一，支持日历视图、列表视图、统计视图三种模式，提供直观的排班管理和批量操作功能。

---

## 🎨 整体布局设计

### 页面结构
```
┌─────────────────────────────────────────────────────────────┐
│ 页面标题: 排班管理              [批量排班] [新增排班] 按钮    │
├─────────────────────────────────────────────────────────────┤
│ [◀上月] [今天] [下月▶]  2025年1月    [日历][列表][统计]     │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│                    主要内容区域                              │
│                 (根据视图类型动态切换)                        │
│                                                             │
├─────────────────────────────────────────────────────────────┤
│ 状态栏: 本月统计 | 操作提示                                  │
└─────────────────────────────────────────────────────────────┘
```

### Vue.js主组件实现
```vue
<template>
  <div class="schedule-management">
    <!-- 页面头部 -->
    <schedule-header 
      title="排班管理"
      @batch-schedule="handleBatchSchedule"
      @add-schedule="handleAddSchedule" />
    
    <!-- 控制面板 -->
    <a-card class="schedule-controls" :bordered="false">
      <a-row justify="space-between" align="middle">
        <!-- 月份导航 -->
        <a-col>
          <a-space size="middle">
            <a-button-group>
              <a-button @click="prevMonth">
                <a-icon type="left" />
                上月
              </a-button>
              <a-button @click="goToday">今天</a-button>
              <a-button @click="nextMonth">
                下月
                <a-icon type="right" />
              </a-button>
            </a-button-group>
            
            <h2 class="current-month">
              {{ currentYear }}年 {{ currentMonthName }}
            </h2>
          </a-space>
        </a-col>
        
        <!-- 视图切换 -->
        <a-col>
          <a-radio-group 
            v-model="viewMode" 
            button-style="solid"
            size="default">
            <a-radio-button value="calendar">
              <a-icon type="calendar" />
              日历视图
            </a-radio-button>
            <a-radio-button value="list">
              <a-icon type="unordered-list" />
              列表视图
            </a-radio-button>
            <a-radio-button value="statistics">
              <a-icon type="bar-chart" />
              统计视图
            </a-radio-button>
          </a-radio-group>
        </a-col>
      </a-row>
    </a-card>
    
    <!-- 视图内容区域 -->
    <div class="schedule-content">
      <calendar-view 
        v-if="viewMode === 'calendar'"
        :schedules="schedules"
        :current-date="currentDate"
        :employees="employees"
        @date-click="handleDateClick"
        @schedule-edit="handleScheduleEdit"
        @schedule-delete="handleScheduleDelete" />
        
      <list-view 
        v-if="viewMode === 'list'"
        :schedules="monthSchedules"
        :employees="employees"
        @edit="handleScheduleEdit"
        @delete="handleScheduleDelete"
        @batch-delete="handleBatchDelete" />
        
      <statistics-view 
        v-if="viewMode === 'statistics'"
        :schedules="schedules"
        :employees="employees"
        :current-date="currentDate" />
    </div>
    
    <!-- 排班编辑弹窗 -->
    <schedule-modal
      ref="scheduleModal"
      :visible="modalVisible"
      :schedule="currentSchedule"
      :employees="employees"
      @save="handleScheduleSave"
      @cancel="handleModalCancel" />
      
    <!-- 批量排班弹窗 -->
    <batch-schedule-modal
      ref="batchModal"
      :visible="batchModalVisible"
      :employees="employees"
      @save="handleBatchSave"
      @cancel="handleBatchCancel" />
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import moment from 'moment'
import ScheduleHeader from './components/ScheduleHeader.vue'
import CalendarView from './components/CalendarView.vue'
import ListView from './components/ListView.vue'
import StatisticsView from './components/StatisticsView.vue'
import ScheduleModal from './components/ScheduleModal.vue'
import BatchScheduleModal from './components/BatchScheduleModal.vue'

export default {
  name: 'ScheduleManagement',
  
  components: {
    ScheduleHeader,
    CalendarView,
    ListView,
    StatisticsView,
    ScheduleModal,
    BatchScheduleModal
  },
  
  data() {
    return {
      viewMode: 'calendar',
      currentDate: moment(),
      modalVisible: false,
      batchModalVisible: false,
      currentSchedule: null
    }
  },
  
  computed: {
    ...mapState('cloisonne', ['schedules', 'employees']),
    
    currentYear() {
      return this.currentDate.year()
    },
    
    currentMonthName() {
      return this.currentDate.format('M月')
    },
    
    monthSchedules() {
      const startOfMonth = this.currentDate.clone().startOf('month')
      const endOfMonth = this.currentDate.clone().endOf('month')
      
      return this.schedules.filter(schedule => {
        const scheduleDate = moment(schedule.date)
        return scheduleDate.isBetween(startOfMonth, endOfMonth, 'day', '[]')
      })
    }
  },
  
  async created() {
    await this.loadScheduleData()
  },
  
  methods: {
    ...mapActions('cloisonne', [
      'fetchSchedules', 
      'createSchedule', 
      'updateSchedule', 
      'deleteSchedule',
      'fetchEmployees'
    ]),
    
    async loadScheduleData() {
      try {
        await Promise.all([
          this.fetchSchedules({
            startDate: this.currentDate.clone().startOf('month').format('YYYY-MM-DD'),
            endDate: this.currentDate.clone().endOf('month').format('YYYY-MM-DD')
          }),
          this.fetchEmployees()
        ])
      } catch (error) {
        this.$message.error('加载排班数据失败')
      }
    },
    
    // 月份导航
    prevMonth() {
      this.currentDate = this.currentDate.clone().subtract(1, 'month')
      this.loadScheduleData()
    },
    
    nextMonth() {
      this.currentDate = this.currentDate.clone().add(1, 'month')
      this.loadScheduleData()
    },
    
    goToday() {
      this.currentDate = moment()
      this.loadScheduleData()
    },
    
    // 排班操作
    handleDateClick(date) {
      this.currentSchedule = {
        date: date.format('YYYY-MM-DD'),
        employeeId: null,
        shift: '早班',
        notes: ''
      }
      this.modalVisible = true
    },
    
    handleAddSchedule() {
      this.currentSchedule = {
        date: moment().format('YYYY-MM-DD'),
        employeeId: null,
        shift: '早班',
        notes: ''
      }
      this.modalVisible = true
    },
    
    handleScheduleEdit(schedule) {
      this.currentSchedule = { ...schedule }
      this.modalVisible = true
    },
    
    async handleScheduleSave(scheduleData) {
      try {
        if (scheduleData.id) {
          await this.updateSchedule(scheduleData)
          this.$message.success('排班更新成功')
        } else {
          await this.createSchedule(scheduleData)
          this.$message.success('排班创建成功')
        }
        this.modalVisible = false
        this.loadScheduleData()
      } catch (error) {
        this.$message.error('保存排班失败')
      }
    },
    
    handleModalCancel() {
      this.modalVisible = false
      this.currentSchedule = null
    },
    
    // 批量排班
    handleBatchSchedule() {
      this.batchModalVisible = true
    },
    
    async handleBatchSave(batchData) {
      try {
        await this.createBatchSchedule(batchData)
        this.$message.success('批量排班成功')
        this.batchModalVisible = false
        this.loadScheduleData()
      } catch (error) {
        this.$message.error('批量排班失败')
      }
    },
    
    handleBatchCancel() {
      this.batchModalVisible = false
    },
    
    // 删除操作
    async handleScheduleDelete(scheduleId) {
      this.$confirm({
        title: '确认删除',
        content: '确定要删除这个排班记录吗？',
        onOk: async () => {
          try {
            await this.deleteSchedule(scheduleId)
            this.$message.success('删除成功')
            this.loadScheduleData()
          } catch (error) {
            this.$message.error('删除失败')
          }
        }
      })
    },
    
    async handleBatchDelete(scheduleIds) {
      this.$confirm({
        title: '确认批量删除',
        content: `确定要删除选中的 ${scheduleIds.length} 条排班记录吗？`,
        onOk: async () => {
          try {
            await Promise.all(scheduleIds.map(id => this.deleteSchedule(id)))
            this.$message.success('批量删除成功')
            this.loadScheduleData()
          } catch (error) {
            this.$message.error('批量删除失败')
          }
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.schedule-management {
  .schedule-controls {
    margin-bottom: 24px;
    
    .current-month {
      margin: 0;
      color: #333;
      font-size: 18px;
      font-weight: 600;
    }
  }
  
  .schedule-content {
    min-height: 600px;
  }
}
</style>
```

---

## 📅 日历视图设计

### CalendarView 日历视图组件
```vue
<template>
  <a-card class="calendar-view" :bordered="false">
    <div class="calendar-container">
      <!-- 星期标题行 -->
      <div class="calendar-header">
        <div 
          v-for="day in weekDays"
          :key="day"
          class="week-day-header">
          {{ day }}
        </div>
      </div>
      
      <!-- 日期网格 -->
      <div class="calendar-grid">
        <div 
          v-for="(day, index) in calendarDays"
          :key="index"
          :class="['calendar-day', {
            'today': isToday(day),
            'other-month': !isCurrentMonth(day),
            'has-schedule': hasSchedule(day),
            'weekend': isWeekend(day)
          }]"
          @click="handleDayClick(day)"
          @drop="handleDrop($event, day)"
          @dragover.prevent
          @dragenter.prevent>
          
          <!-- 日期数字 -->
          <div class="day-number">
            {{ day ? day.date() : '' }}
          </div>
          
          <!-- 排班列表 -->
          <div class="day-schedules" v-if="day">
            <div 
              v-for="schedule in getDaySchedules(day)"
              :key="schedule.id"
              :class="['schedule-item', `shift-${schedule.shift}`]"
              :draggable="true"
              @dragstart="handleDragStart($event, schedule)"
              @click.stop="handleScheduleClick(schedule)">
              
              <a-avatar 
                :size="20" 
                :src="getEmployeeAvatar(schedule.employeeId)"
                class="schedule-avatar" />
              
              <span class="schedule-text">
                {{ getEmployeeName(schedule.employeeId) }}
              </span>
              
              <a-tag 
                :color="getShiftColor(schedule.shift)"
                size="small"
                class="schedule-tag">
                {{ schedule.shift }}
              </a-tag>
              
              <!-- 操作按钮 -->
              <div class="schedule-actions">
                <a-button 
                  type="link" 
                  size="small"
                  icon="edit"
                  @click.stop="handleEdit(schedule)" />
                <a-button 
                  type="link" 
                  size="small"
                  icon="delete"
                  @click.stop="handleDelete(schedule.id)" />
              </div>
            </div>
          </div>
          
          <!-- 添加按钮 -->
          <div 
            v-if="day && isCurrentMonth(day)"
            class="add-schedule-btn"
            @click.stop="handleAddSchedule(day)">
            <a-icon type="plus" />
          </div>
        </div>
      </div>
    </div>
  </a-card>
</template>

<script>
import moment from 'moment'

export default {
  name: 'CalendarView',
  
  props: {
    schedules: { type: Array, default: () => [] },
    currentDate: { type: Object, required: true },
    employees: { type: Array, default: () => [] }
  },
  
  data() {
    return {
      weekDays: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
      draggedSchedule: null
    }
  },
  
  computed: {
    calendarDays() {
      const startOfMonth = this.currentDate.clone().startOf('month')
      const endOfMonth = this.currentDate.clone().endOf('month')
      const startOfCalendar = startOfMonth.clone().startOf('week')
      const endOfCalendar = endOfMonth.clone().endOf('week')
      
      const days = []
      let current = startOfCalendar.clone()
      
      while (current.isSameOrBefore(endOfCalendar)) {
        days.push(current.clone())
        current.add(1, 'day')
      }
      
      return days
    }
  },
  
  methods: {
    isToday(day) {
      return day && day.isSame(moment(), 'day')
    },
    
    isCurrentMonth(day) {
      return day && day.isSame(this.currentDate, 'month')
    },
    
    isWeekend(day) {
      return day && (day.day() === 0 || day.day() === 6)
    },
    
    hasSchedule(day) {
      return day && this.getDaySchedules(day).length > 0
    },
    
    getDaySchedules(day) {
      const dayStr = day.format('YYYY-MM-DD')
      return this.schedules.filter(schedule => schedule.date === dayStr)
    },
    
    getEmployeeName(employeeId) {
      const employee = this.employees.find(emp => emp.id === employeeId)
      return employee ? employee.name : '未知员工'
    },
    
    getEmployeeAvatar(employeeId) {
      const employee = this.employees.find(emp => emp.id === employeeId)
      return employee?.avatar || this.generateAvatar(this.getEmployeeName(employeeId))
    },
    
    generateAvatar(name) {
      return `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=3B82F6&color=fff&size=40`
    },
    
    getShiftColor(shift) {
      const colors = {
        '早班': 'blue',
        '中班': 'purple',
        '晚班': 'green',
        '夜班': 'orange',
        '全天': 'red'
      }
      return colors[shift] || 'default'
    },
    
    // 事件处理
    handleDayClick(day) {
      if (day && this.isCurrentMonth(day)) {
        this.$emit('date-click', day)
      }
    },
    
    handleScheduleClick(schedule) {
      this.$emit('schedule-edit', schedule)
    },
    
    handleEdit(schedule) {
      this.$emit('schedule-edit', schedule)
    },
    
    handleDelete(scheduleId) {
      this.$emit('schedule-delete', scheduleId)
    },
    
    handleAddSchedule(day) {
      this.$emit('date-click', day)
    },
    
    // 拖拽功能
    handleDragStart(event, schedule) {
      this.draggedSchedule = schedule
      event.dataTransfer.effectAllowed = 'move'
      event.dataTransfer.setData('text/html', event.target.outerHTML)
    },
    
    handleDrop(event, targetDay) {
      event.preventDefault()
      
      if (this.draggedSchedule && targetDay && this.isCurrentMonth(targetDay)) {
        const newDate = targetDay.format('YYYY-MM-DD')
        
        if (newDate !== this.draggedSchedule.date) {
          const updatedSchedule = {
            ...this.draggedSchedule,
            date: newDate
          }
          
          this.$emit('schedule-edit', updatedSchedule)
        }
      }
      
      this.draggedSchedule = null
    }
  }
}
</script>

<style lang="less" scoped>
.calendar-view {
  .calendar-container {
    .calendar-header {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      gap: 1px;
      background: #f0f0f0;
      margin-bottom: 1px;
      
      .week-day-header {
        background: #fafafa;
        padding: 16px 8px;
        text-align: center;
        font-weight: 600;
        color: #666;
        font-size: 14px;
      }
    }
    
    .calendar-grid {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      gap: 1px;
      background: #f0f0f0;
      
      .calendar-day {
        background: white;
        min-height: 140px;
        padding: 8px;
        position: relative;
        cursor: pointer;
        transition: all 0.2s;
        
        &:hover {
          background: #f8f9fa;
          
          .add-schedule-btn {
            opacity: 1;
          }
        }
        
        &.today {
          background: #e6f7ff;
          
          .day-number {
            color: #1890ff;
            font-weight: bold;
            background: #1890ff;
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
        
        &.other-month {
          background: #f5f5f5;
          color: #ccc;
        }
        
        &.weekend {
          background: #fafafa;
        }
        
        &.has-schedule {
          border-left: 3px solid #3B82F6;
        }
        
        .day-number {
          font-size: 14px;
          font-weight: 500;
          margin-bottom: 6px;
          height: 24px;
          display: flex;
          align-items: center;
        }
        
        .day-schedules {
          .schedule-item {
            display: flex;
            align-items: center;
            padding: 4px 6px;
            margin-bottom: 4px;
            border-radius: 6px;
            font-size: 12px;
            cursor: move;
            position: relative;
            transition: all 0.2s;
            
            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 2px 8px rgba(0,0,0,0.1);
              
              .schedule-actions {
                opacity: 1;
              }
            }
            
            &.shift-早班 {
              background: #e6f7ff;
              border: 1px solid #91d5ff;
            }
            
            &.shift-中班 {
              background: #f9f0ff;
              border: 1px solid #d3adf7;
            }
            
            &.shift-晚班 {
              background: #f6ffed;
              border: 1px solid #b7eb8f;
            }
            
            &.shift-夜班 {
              background: #fff7e6;
              border: 1px solid #ffd591;
            }
            
            &.shift-全天 {
              background: #fff2f0;
              border: 1px solid #ffb3b3;
            }
            
            .schedule-avatar {
              margin-right: 6px;
              flex-shrink: 0;
            }
            
            .schedule-text {
              flex: 1;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              margin-right: 4px;
            }
            
            .schedule-tag {
              margin: 0;
              font-size: 10px;
              padding: 0 4px;
              height: 16px;
              line-height: 14px;
            }
            
            .schedule-actions {
              position: absolute;
              top: 2px;
              right: 2px;
              opacity: 0;
              transition: opacity 0.2s;
              background: rgba(255, 255, 255, 0.9);
              border-radius: 4px;
              padding: 2px;
              
              .ant-btn {
                padding: 0;
                width: 16px;
                height: 16px;
                font-size: 10px;
                border: none;
                box-shadow: none;
              }
            }
          }
        }
        
        .add-schedule-btn {
          position: absolute;
          bottom: 8px;
          right: 8px;
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background: #3B82F6;
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.2s;
          cursor: pointer;
          font-size: 12px;
          
          &:hover {
            background: #2563EB;
          }
        }
      }
    }
  }
}
</style>
```

---

## 📋 列表视图设计

### ListView 列表视图组件
```vue
<template>
  <a-card class="list-view" :bordered="false">
    <!-- 操作工具栏 -->
    <div class="list-toolbar">
      <a-row justify="space-between" align="middle">
        <a-col>
          <a-space>
            <a-input-search
              v-model="searchKeyword"
              placeholder="搜索员工姓名..."
              style="width: 200px"
              @search="handleSearch" />

            <a-select
              v-model="filterShift"
              placeholder="筛选班次"
              style="width: 120px"
              allow-clear>
              <a-select-option value="">全部班次</a-select-option>
              <a-select-option value="早班">早班</a-select-option>
              <a-select-option value="中班">中班</a-select-option>
              <a-select-option value="晚班">晚班</a-select-option>
              <a-select-option value="夜班">夜班</a-select-option>
              <a-select-option value="全天">全天</a-select-option>
            </a-select>
          </a-space>
        </a-col>

        <a-col>
          <a-space>
            <a-button
              :disabled="selectedRowKeys.length === 0"
              @click="handleBatchDelete">
              <a-icon type="delete" />
              批量删除 ({{ selectedRowKeys.length }})
            </a-button>

            <a-button type="primary" @click="handleExport">
              <a-icon type="download" />
              导出排班表
            </a-button>
          </a-space>
        </a-col>
      </a-row>
    </div>

    <!-- 排班表格 -->
    <a-table
      :columns="columns"
      :data-source="filteredSchedules"
      :pagination="pagination"
      :loading="loading"
      :row-selection="rowSelection"
      :scroll="{ x: 1200 }"
      size="middle"
      bordered
      @change="handleTableChange">

      <!-- 员工信息列 -->
      <template #employee="{ record }">
        <div class="employee-info">
          <a-avatar
            :size="32"
            :src="getEmployeeAvatar(record.employeeId)" />
          <div class="employee-details">
            <div class="employee-name">{{ getEmployeeName(record.employeeId) }}</div>
            <div class="employee-role">{{ getEmployeeRole(record.employeeId) }}</div>
          </div>
        </div>
      </template>

      <!-- 日期列 -->
      <template #date="{ record }">
        <div class="date-info">
          <div class="date-text">{{ formatDate(record.date) }}</div>
          <div class="weekday-text">{{ getWeekday(record.date) }}</div>
        </div>
      </template>

      <!-- 班次列 -->
      <template #shift="{ record }">
        <a-tag :color="getShiftColor(record.shift)">
          {{ record.shift }}
        </a-tag>
        <div class="shift-time">{{ getShiftTime(record.shift) }}</div>
      </template>

      <!-- 状态列 -->
      <template #status="{ record }">
        <a-badge
          :status="getStatusBadge(record.status)"
          :text="record.status || '正常'" />
      </template>

      <!-- 操作列 -->
      <template #action="{ record }">
        <a-space>
          <a-button
            type="link"
            size="small"
            @click="handleEdit(record)">
            编辑
          </a-button>
          <a-button
            type="link"
            size="small"
            danger
            @click="handleDelete(record.id)">
            删除
          </a-button>
        </a-space>
      </template>
    </a-table>
  </a-card>
</template>

<script>
import moment from 'moment'

export default {
  name: 'ListView',

  props: {
    schedules: { type: Array, default: () => [] },
    employees: { type: Array, default: () => [] }
  },

  data() {
    return {
      searchKeyword: '',
      filterShift: '',
      selectedRowKeys: [],
      loading: false,
      pagination: {
        current: 1,
        pageSize: 20,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
      },

      columns: [
        {
          title: '员工',
          dataIndex: 'employeeId',
          key: 'employee',
          width: 200,
          fixed: 'left',
          scopedSlots: { customRender: 'employee' }
        },
        {
          title: '日期',
          dataIndex: 'date',
          key: 'date',
          width: 120,
          sorter: (a, b) => moment(a.date).unix() - moment(b.date).unix(),
          scopedSlots: { customRender: 'date' }
        },
        {
          title: '班次',
          dataIndex: 'shift',
          key: 'shift',
          width: 120,
          filters: [
            { text: '早班', value: '早班' },
            { text: '中班', value: '中班' },
            { text: '晚班', value: '晚班' },
            { text: '夜班', value: '夜班' },
            { text: '全天', value: '全天' }
          ],
          scopedSlots: { customRender: 'shift' }
        },
        {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          width: 100,
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '备注',
          dataIndex: 'notes',
          key: 'notes',
          ellipsis: true
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          key: 'createTime',
          width: 160,
          sorter: (a, b) => moment(a.createTime).unix() - moment(b.createTime).unix(),
          customRender: (text) => moment(text).format('YYYY-MM-DD HH:mm')
        },
        {
          title: '操作',
          key: 'action',
          width: 120,
          fixed: 'right',
          scopedSlots: { customRender: 'action' }
        }
      ]
    }
  },

  computed: {
    filteredSchedules() {
      let filtered = [...this.schedules]

      // 搜索过滤
      if (this.searchKeyword) {
        filtered = filtered.filter(schedule => {
          const employeeName = this.getEmployeeName(schedule.employeeId)
          return employeeName.toLowerCase().includes(this.searchKeyword.toLowerCase())
        })
      }

      // 班次过滤
      if (this.filterShift) {
        filtered = filtered.filter(schedule => schedule.shift === this.filterShift)
      }

      return filtered
    },

    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: (selectedRowKeys) => {
          this.selectedRowKeys = selectedRowKeys
        },
        getCheckboxProps: (record) => ({
          disabled: record.status === '已完成'
        })
      }
    }
  },

  methods: {
    getEmployeeName(employeeId) {
      const employee = this.employees.find(emp => emp.id === employeeId)
      return employee ? employee.name : '未知员工'
    },

    getEmployeeRole(employeeId) {
      const employee = this.employees.find(emp => emp.id === employeeId)
      return employee ? employee.role : ''
    },

    getEmployeeAvatar(employeeId) {
      const employee = this.employees.find(emp => emp.id === employeeId)
      return employee?.avatar || this.generateAvatar(this.getEmployeeName(employeeId))
    },

    generateAvatar(name) {
      return `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=3B82F6&color=fff&size=64`
    },

    formatDate(date) {
      return moment(date).format('MM-DD')
    },

    getWeekday(date) {
      return moment(date).format('dddd')
    },

    getShiftColor(shift) {
      const colors = {
        '早班': 'blue',
        '中班': 'purple',
        '晚班': 'green',
        '夜班': 'orange',
        '全天': 'red'
      }
      return colors[shift] || 'default'
    },

    getShiftTime(shift) {
      const times = {
        '早班': '08:00-16:00',
        '中班': '12:00-20:00',
        '晚班': '16:00-24:00',
        '夜班': '00:00-08:00',
        '全天': '08:00-24:00'
      }
      return times[shift] || ''
    },

    getStatusBadge(status) {
      const badges = {
        '正常': 'success',
        '请假': 'warning',
        '调班': 'processing',
        '缺勤': 'error'
      }
      return badges[status] || 'default'
    },

    handleSearch() {
      // 搜索逻辑已在computed中处理
    },

    handleTableChange(pagination, filters, sorter) {
      this.pagination = pagination
    },

    handleEdit(record) {
      this.$emit('edit', record)
    },

    handleDelete(id) {
      this.$emit('delete', id)
    },

    handleBatchDelete() {
      this.$emit('batch-delete', this.selectedRowKeys)
    },

    handleExport() {
      // 导出功能
      this.$message.info('导出功能开发中...')
    }
  }
}
</script>

<style lang="less" scoped>
.list-view {
  .list-toolbar {
    margin-bottom: 16px;
  }

  .employee-info {
    display: flex;
    align-items: center;

    .employee-details {
      margin-left: 12px;

      .employee-name {
        font-weight: 500;
        margin-bottom: 2px;
      }

      .employee-role {
        font-size: 12px;
        color: #999;
      }
    }
  }

  .date-info {
    .date-text {
      font-weight: 500;
      margin-bottom: 2px;
    }

    .weekday-text {
      font-size: 12px;
      color: #999;
    }
  }

  .shift-time {
    font-size: 12px;
    color: #999;
    margin-top: 2px;
  }
}
</style>
```

---

## 📊 统计视图设计

### StatisticsView 统计视图组件
```vue
<template>
  <div class="statistics-view">
    <a-row :gutter="24">
      <!-- 统计卡片区域 -->
      <a-col :xs="24" :lg="8">
        <a-space direction="vertical" size="large" style="width: 100%">
          <!-- 本月排班统计 -->
          <a-card title="本月排班统计" :bordered="false">
            <div class="stat-item">
              <span class="stat-label">总排班数</span>
              <span class="stat-value">{{ monthStats.totalSchedules }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">参与员工</span>
              <span class="stat-value">{{ monthStats.totalEmployees }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">平均工时</span>
              <span class="stat-value">{{ monthStats.avgWorkHours }}h</span>
            </div>
          </a-card>

          <!-- 班次分布 -->
          <a-card title="班次分布" :bordered="false">
            <div class="shift-distribution">
              <div
                v-for="(count, shift) in shiftDistribution"
                :key="shift"
                class="shift-item">
                <a-tag :color="getShiftColor(shift)">{{ shift }}</a-tag>
                <span class="shift-count">{{ count }}次</span>
                <div class="shift-bar">
                  <div
                    class="shift-progress"
                    :style="{
                      width: `${(count / maxShiftCount) * 100}%`,
                      backgroundColor: getShiftColorHex(shift)
                    }"></div>
                </div>
              </div>
            </div>
          </a-card>
        </a-space>
      </a-col>

      <!-- 图表区域 -->
      <a-col :xs="24" :lg="16">
        <a-space direction="vertical" size="large" style="width: 100%">
          <!-- 员工工时统计图表 -->
          <a-card title="员工工时统计" :bordered="false">
            <div id="workHoursChart" style="height: 300px;"></div>
          </a-card>

          <!-- 每日排班趋势图表 -->
          <a-card title="每日排班趋势" :bordered="false">
            <div id="dailyTrendChart" style="height: 250px;"></div>
          </a-card>
        </a-space>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import moment from 'moment'

export default {
  name: 'StatisticsView',

  props: {
    schedules: { type: Array, default: () => [] },
    employees: { type: Array, default: () => [] },
    currentDate: { type: Object, required: true }
  },

  data() {
    return {
      workHoursChart: null,
      dailyTrendChart: null
    }
  },

  computed: {
    monthStats() {
      const totalSchedules = this.schedules.length
      const employeeIds = [...new Set(this.schedules.map(s => s.employeeId))]
      const totalEmployees = employeeIds.length

      // 计算平均工时
      const totalWorkHours = this.schedules.reduce((sum, schedule) => {
        return sum + this.getShiftHours(schedule.shift)
      }, 0)
      const avgWorkHours = totalSchedules > 0 ? (totalWorkHours / totalEmployees).toFixed(1) : 0

      return {
        totalSchedules,
        totalEmployees,
        avgWorkHours
      }
    },

    shiftDistribution() {
      const distribution = {}
      this.schedules.forEach(schedule => {
        distribution[schedule.shift] = (distribution[schedule.shift] || 0) + 1
      })
      return distribution
    },

    maxShiftCount() {
      return Math.max(...Object.values(this.shiftDistribution), 1)
    },

    employeeWorkHours() {
      const workHours = {}

      this.schedules.forEach(schedule => {
        const employeeId = schedule.employeeId
        const employeeName = this.getEmployeeName(employeeId)
        const hours = this.getShiftHours(schedule.shift)

        if (!workHours[employeeName]) {
          workHours[employeeName] = 0
        }
        workHours[employeeName] += hours
      })

      return workHours
    },

    dailyScheduleCount() {
      const dailyCount = {}
      const startOfMonth = this.currentDate.clone().startOf('month')
      const endOfMonth = this.currentDate.clone().endOf('month')

      // 初始化每日计数
      let current = startOfMonth.clone()
      while (current.isSameOrBefore(endOfMonth)) {
        dailyCount[current.format('YYYY-MM-DD')] = 0
        current.add(1, 'day')
      }

      // 统计每日排班数量
      this.schedules.forEach(schedule => {
        if (dailyCount.hasOwnProperty(schedule.date)) {
          dailyCount[schedule.date]++
        }
      })

      return dailyCount
    }
  },

  mounted() {
    this.$nextTick(() => {
      this.initCharts()
    })
  },

  watch: {
    schedules: {
      handler() {
        this.$nextTick(() => {
          this.updateCharts()
        })
      },
      deep: true
    }
  },

  beforeDestroy() {
    if (this.workHoursChart) {
      this.workHoursChart.dispose()
    }
    if (this.dailyTrendChart) {
      this.dailyTrendChart.dispose()
    }
  },

  methods: {
    getEmployeeName(employeeId) {
      const employee = this.employees.find(emp => emp.id === employeeId)
      return employee ? employee.name : '未知员工'
    },

    getShiftHours(shift) {
      const hours = {
        '早班': 8,
        '中班': 8,
        '晚班': 8,
        '夜班': 8,
        '全天': 16
      }
      return hours[shift] || 8
    },

    getShiftColor(shift) {
      const colors = {
        '早班': 'blue',
        '中班': 'purple',
        '晚班': 'green',
        '夜班': 'orange',
        '全天': 'red'
      }
      return colors[shift] || 'default'
    },

    getShiftColorHex(shift) {
      const colors = {
        '早班': '#1890ff',
        '中班': '#722ed1',
        '晚班': '#52c41a',
        '夜班': '#fa8c16',
        '全天': '#f5222d'
      }
      return colors[shift] || '#d9d9d9'
    },

    initCharts() {
      this.initWorkHoursChart()
      this.initDailyTrendChart()
    },

    initWorkHoursChart() {
      const chartDom = document.getElementById('workHoursChart')
      if (!chartDom) return

      this.workHoursChart = echarts.init(chartDom)
      this.updateWorkHoursChart()
    },

    initDailyTrendChart() {
      const chartDom = document.getElementById('dailyTrendChart')
      if (!chartDom) return

      this.dailyTrendChart = echarts.init(chartDom)
      this.updateDailyTrendChart()
    },

    updateCharts() {
      this.updateWorkHoursChart()
      this.updateDailyTrendChart()
    },

    updateWorkHoursChart() {
      if (!this.workHoursChart) return

      const data = Object.entries(this.employeeWorkHours)
        .map(([name, hours]) => ({ name, value: hours }))
        .sort((a, b) => b.value - a.value)

      const option = {
        title: {
          text: '员工工时排行',
          left: 'center',
          textStyle: {
            fontSize: 14,
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: '{b}: {c}小时'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: data.map(item => item.name),
          axisLabel: {
            rotate: 45
          }
        },
        yAxis: {
          type: 'value',
          name: '工时(小时)'
        },
        series: [{
          type: 'bar',
          data: data.map(item => item.value),
          itemStyle: {
            color: '#3B82F6'
          }
        }]
      }

      this.workHoursChart.setOption(option)
    },

    updateDailyTrendChart() {
      if (!this.dailyTrendChart) return

      const dates = Object.keys(this.dailyScheduleCount).sort()
      const counts = dates.map(date => this.dailyScheduleCount[date])

      const option = {
        title: {
          text: '每日排班数量趋势',
          left: 'center',
          textStyle: {
            fontSize: 14,
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          formatter: function(params) {
            const date = moment(params[0].name).format('MM月DD日')
            return `${date}: ${params[0].value}个排班`
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: dates,
          axisLabel: {
            formatter: function(value) {
              return moment(value).format('DD')
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '排班数量'
        },
        series: [{
          type: 'line',
          data: counts,
          smooth: true,
          itemStyle: {
            color: '#52c41a'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0, color: 'rgba(82, 196, 26, 0.3)'
              }, {
                offset: 1, color: 'rgba(82, 196, 26, 0.1)'
              }]
            }
          }
        }]
      }

      this.dailyTrendChart.setOption(option)
    }
  }
}
</script>

<style lang="less" scoped>
.statistics-view {
  .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .stat-label {
      color: #666;
    }

    .stat-value {
      font-weight: 600;
      color: #333;
      font-size: 16px;
    }
  }

  .shift-distribution {
    .shift-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .ant-tag {
        margin-right: 8px;
        min-width: 50px;
        text-align: center;
      }

      .shift-count {
        margin-right: 12px;
        font-weight: 500;
        min-width: 40px;
      }

      .shift-bar {
        flex: 1;
        height: 8px;
        background: #f0f0f0;
        border-radius: 4px;
        overflow: hidden;

        .shift-progress {
          height: 100%;
          transition: width 0.3s ease;
        }
      }
    }
  }
}
</style>
```

---

*设计文档版本: v1.0*
*最后更新: 2025-01-22*
