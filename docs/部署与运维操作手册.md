# 聆花文化ERP部署与运维操作手册

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-06-18
- **适用环境**: 阿里云ECS + Docker + MySQL + Redis
- **技术栈**: Spring Boot + Vue.js + MySQL + Redis + Nginx
- **维护团队**: jshERP运维团队

---

## 目录

1. [环境准备](#环境准备)
2. [系统部署](#系统部署)
3. [配置管理](#配置管理)
4. [监控告警](#监控告警)
5. [备份恢复](#备份恢复)
6. [性能优化](#性能优化)
7. [故障处理](#故障处理)
8. [安全管理](#安全管理)
9. [日常维护](#日常维护)
10. [应急预案](#应急预案)

---

## 环境准备

### 1. 服务器配置要求

#### 生产环境配置

```bash
# 推荐配置
CPU: 8核心以上
内存: 16GB以上
存储: 500GB SSD
网络: 10Mbps以上带宽
操作系统: CentOS 7.9 / Ubuntu 20.04 LTS

# 最低配置
CPU: 4核心
内存: 8GB
存储: 200GB SSD
网络: 5Mbps带宽
```

#### 测试环境配置

```bash
# 测试环境配置
CPU: 4核心
内存: 8GB
存储: 100GB SSD
网络: 5Mbps带宽
```

### 2. 软件环境安装

#### 基础软件安装脚本

**文件路径**: `scripts/setup/install_base_software.sh`

```bash
#!/bin/bash

# 聆花文化ERP基础软件安装脚本
# 适用于CentOS 7.9系统

set -e

echo "=== 开始安装基础软件环境 ==="

# 更新系统
echo "1. 更新系统软件包..."
yum update -y

# 安装基础工具
echo "2. 安装基础工具..."
yum install -y wget curl vim git unzip net-tools firewalld

# 安装Docker
echo "3. 安装Docker..."
curl -fsSL https://get.docker.com | bash -s docker --mirror Aliyun
systemctl start docker
systemctl enable docker

# 安装Docker Compose
echo "4. 安装Docker Compose..."
curl -L "https://github.com/docker/compose/releases/download/1.29.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# 安装Java 8
echo "5. 安装Java 8..."
yum install -y java-1.8.0-openjdk java-1.8.0-openjdk-devel

# 安装Node.js
echo "6. 安装Node.js..."
curl -sL https://rpm.nodesource.com/setup_16.x | bash -
yum install -y nodejs

# 安装Nginx
echo "7. 安装Nginx..."
yum install -y epel-release
yum install -y nginx
systemctl enable nginx

# 配置防火墙
echo "8. 配置防火墙..."
systemctl start firewalld
systemctl enable firewalld
firewall-cmd --permanent --add-port=80/tcp
firewall-cmd --permanent --add-port=443/tcp
firewall-cmd --permanent --add-port=8080/tcp
firewall-cmd --permanent --add-port=3306/tcp
firewall-cmd --permanent --add-port=6379/tcp
firewall-cmd --reload

# 创建应用目录
echo "9. 创建应用目录..."
mkdir -p /opt/jshERP
mkdir -p /opt/jshERP/logs
mkdir -p /opt/jshERP/data
mkdir -p /opt/jshERP/backup

# 设置权限
chown -R 1000:1000 /opt/jshERP

echo "=== 基础软件环境安装完成 ==="
echo "Docker版本: $(docker --version)"
echo "Docker Compose版本: $(docker-compose --version)"
echo "Java版本: $(java -version)"
echo "Node.js版本: $(node --version)"
echo "Nginx版本: $(nginx -version)"
```

### 3. 数据库环境准备

#### MySQL Docker配置

**文件路径**: `docker/mysql/docker-compose.yml`

```yaml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: jshERP-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: jsh_erp
      MYSQL_USER: jsh_user
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
      TZ: Asia/Shanghai
    ports:
      - "3306:3306"
    volumes:
      - ./data:/var/lib/mysql
      - ./conf/my.cnf:/etc/mysql/my.cnf
      - ./init:/docker-entrypoint-initdb.d
      - ./logs:/var/log/mysql
    networks:
      - jshERP-network
    command: [
      "--character-set-server=utf8mb4",
      "--collation-server=utf8mb4_unicode_ci",
      "--default-time-zone=+8:00",
      "--max_connections=1000",
      "--innodb_buffer_pool_size=1G",
      "--innodb_log_file_size=256M"
    ]

networks:
  jshERP-network:
    external: true
```

#### MySQL配置文件

**文件路径**: `docker/mysql/conf/my.cnf`

```ini
[mysqld]
# 基础设置
port = 3306
bind-address = 0.0.0.0
default-time-zone = '+8:00'

# 字符集设置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init_connect = 'SET NAMES utf8mb4'

# 连接设置
max_connections = 1000
max_connect_errors = 100000
max_allowed_packet = 64M
wait_timeout = 28800
interactive_timeout = 28800

# InnoDB设置
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_log_buffer_size = 16M
innodb_flush_log_at_trx_commit = 2
innodb_file_per_table = 1
innodb_io_capacity = 2000

# 查询缓存
query_cache_type = 1
query_cache_size = 256M
query_cache_limit = 2M

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/mysql-slow.log
long_query_time = 2

# 错误日志
log-error = /var/log/mysql/error.log

# 二进制日志
log-bin = mysql-bin
binlog_format = ROW
expire_logs_days = 7

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
```

#### Redis Docker配置

**文件路径**: `docker/redis/docker-compose.yml`

```yaml
version: '3.8'

services:
  redis:
    image: redis:6.2-alpine
    container_name: jshERP-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - ./data:/data
      - ./conf/redis.conf:/usr/local/etc/redis/redis.conf
      - ./logs:/var/log/redis
    networks:
      - jshERP-network
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      TZ: Asia/Shanghai

networks:
  jshERP-network:
    external: true
```

#### Redis配置文件

**文件路径**: `docker/redis/conf/redis.conf`

```ini
# 网络配置
bind 0.0.0.0
port 6379
protected-mode no

# 内存配置
maxmemory 2gb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000

# AOF配置
appendonly yes
appendfsync everysec

# 日志配置
loglevel notice
logfile /var/log/redis/redis.log

# 安全配置
requirepass ${REDIS_PASSWORD}

# 性能优化
tcp-keepalive 300
timeout 0
tcp-backlog 511
databases 16
```

---

## 系统部署

### 1. 自动化部署脚本

#### 主部署脚本

**文件路径**: `scripts/deploy/deploy.sh`

```bash
#!/bin/bash

# 聆花文化ERP自动化部署脚本
# 支持生产环境和测试环境部署

set -e

# 配置变量
DEPLOY_ENV=${1:-production}
APP_NAME="jshERP"
APP_VERSION=${2:-latest}
DEPLOY_PATH="/opt/jshERP"
BACKUP_PATH="/opt/jshERP/backup"
LOG_PATH="/opt/jshERP/logs"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查部署环境
check_environment() {
    log_info "检查部署环境..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    # 检查磁盘空间
    AVAILABLE_SPACE=$(df ${DEPLOY_PATH} | awk 'NR==2 {print $4}')
    if [ ${AVAILABLE_SPACE} -lt 5242880 ]; then
        log_error "磁盘空间不足，至少需要5GB可用空间"
        exit 1
    fi
    
    log_info "环境检查通过"
}

# 创建备份
create_backup() {
    log_info "创建系统备份..."
    
    BACKUP_DATE=$(date +%Y%m%d_%H%M%S)
    BACKUP_DIR="${BACKUP_PATH}/${BACKUP_DATE}"
    
    mkdir -p ${BACKUP_DIR}
    
    # 备份应用配置
    if [ -d "${DEPLOY_PATH}/config" ]; then
        cp -r ${DEPLOY_PATH}/config ${BACKUP_DIR}/
        log_info "配置文件备份完成"
    fi
    
    # 备份数据库
    if docker ps | grep -q "jshERP-mysql"; then
        log_info "开始备份数据库..."
        docker exec jshERP-mysql mysqldump -u root -p${MYSQL_ROOT_PASSWORD} --single-transaction --routines --triggers jsh_erp > ${BACKUP_DIR}/database_backup.sql
        log_info "数据库备份完成"
    fi
    
    # 备份文件上传目录
    if [ -d "${DEPLOY_PATH}/uploads" ]; then
        tar -czf ${BACKUP_DIR}/uploads_backup.tar.gz -C ${DEPLOY_PATH} uploads
        log_info "文件备份完成"
    fi
    
    echo ${BACKUP_DIR} > ${DEPLOY_PATH}/.last_backup
    log_info "备份创建完成: ${BACKUP_DIR}"
}

# 停止服务
stop_services() {
    log_info "停止现有服务..."
    
    cd ${DEPLOY_PATH}
    
    if [ -f "docker-compose.yml" ]; then
        docker-compose down
        log_info "服务停止完成"
    else
        log_warn "未找到docker-compose.yml文件，跳过服务停止"
    fi
}

# 更新应用
update_application() {
    log_info "更新应用文件..."
    
    # 创建临时目录
    TEMP_DIR="/tmp/jshERP_deploy_${DEPLOY_DATE}"
    mkdir -p ${TEMP_DIR}
    
    # 下载最新版本
    if [ "${DEPLOY_ENV}" = "production" ]; then
        DOWNLOAD_URL="https://releases.jsherp.com/production/${APP_VERSION}/jshERP-${APP_VERSION}.tar.gz"
    else
        DOWNLOAD_URL="https://releases.jsherp.com/testing/${APP_VERSION}/jshERP-${APP_VERSION}.tar.gz"
    fi
    
    log_info "下载应用包: ${DOWNLOAD_URL}"
    wget -O ${TEMP_DIR}/jshERP.tar.gz ${DOWNLOAD_URL}
    
    # 解压应用包
    cd ${TEMP_DIR}
    tar -xzf jshERP.tar.gz
    
    # 更新应用文件
    cp -r jshERP/* ${DEPLOY_PATH}/
    
    # 设置权限
    chown -R 1000:1000 ${DEPLOY_PATH}
    chmod +x ${DEPLOY_PATH}/scripts/*.sh
    
    # 清理临时文件
    rm -rf ${TEMP_DIR}
    
    log_info "应用更新完成"
}

# 更新配置
update_configuration() {
    log_info "更新应用配置..."
    
    # 加载环境变量
    if [ -f "${DEPLOY_PATH}/config/.env.${DEPLOY_ENV}" ]; then
        source ${DEPLOY_PATH}/config/.env.${DEPLOY_ENV}
    else
        log_error "环境配置文件不存在: .env.${DEPLOY_ENV}"
        exit 1
    fi
    
    # 替换配置模板
    envsubst < ${DEPLOY_PATH}/config/application.properties.template > ${DEPLOY_PATH}/config/application.properties
    envsubst < ${DEPLOY_PATH}/config/docker-compose.yml.template > ${DEPLOY_PATH}/docker-compose.yml
    
    log_info "配置更新完成"
}

# 启动服务
start_services() {
    log_info "启动应用服务..."
    
    cd ${DEPLOY_PATH}
    
    # 创建Docker网络
    docker network create jshERP-network 2>/dev/null || true
    
    # 启动服务
    docker-compose up -d
    
    log_info "服务启动完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 等待服务启动
    sleep 30
    
    # 检查后端服务
    BACKEND_URL="http://localhost:8080/jshERP-boot/health"
    for i in {1..30}; do
        if curl -f ${BACKEND_URL} >/dev/null 2>&1; then
            log_info "后端服务健康检查通过"
            break
        fi
        
        if [ $i -eq 30 ]; then
            log_error "后端服务健康检查失败"
            exit 1
        fi
        
        sleep 10
    done
    
    # 检查前端服务
    FRONTEND_URL="http://localhost"
    if curl -f ${FRONTEND_URL} >/dev/null 2>&1; then
        log_info "前端服务健康检查通过"
    else
        log_error "前端服务健康检查失败"
        exit 1
    fi
    
    log_info "健康检查全部通过"
}

# 部署后处理
post_deploy() {
    log_info "执行部署后处理..."
    
    # 清理旧的Docker镜像
    docker image prune -f
    
    # 记录部署信息
    DEPLOY_INFO="${DEPLOY_PATH}/deploy_info.json"
    cat > ${DEPLOY_INFO} << EOF
{
    "deploy_time": "$(date -u +%Y-%m-%dT%H:%M:%S)Z",
    "deploy_env": "${DEPLOY_ENV}",
    "app_version": "${APP_VERSION}",
    "deploy_user": "$(whoami)",
    "git_commit": "$(git rev-parse HEAD 2>/dev/null || echo 'unknown')"
}
EOF
    
    # 发送部署通知
    send_notification "部署成功" "jshERP ${APP_VERSION} 已成功部署到 ${DEPLOY_ENV} 环境"
    
    log_info "部署后处理完成"
}

# 发送通知
send_notification() {
    local title="$1"
    local message="$2"
    
    # 这里可以集成钉钉、企业微信等通知方式
    log_info "通知: ${title} - ${message}"
}

# 回滚部署
rollback_deploy() {
    log_info "开始回滚部署..."
    
    if [ ! -f "${DEPLOY_PATH}/.last_backup" ]; then
        log_error "未找到备份信息，无法回滚"
        exit 1
    fi
    
    BACKUP_DIR=$(cat ${DEPLOY_PATH}/.last_backup)
    
    if [ ! -d "${BACKUP_DIR}" ]; then
        log_error "备份目录不存在: ${BACKUP_DIR}"
        exit 1
    fi
    
    # 停止服务
    stop_services
    
    # 恢复配置
    if [ -d "${BACKUP_DIR}/config" ]; then
        cp -r ${BACKUP_DIR}/config ${DEPLOY_PATH}/
    fi
    
    # 恢复数据库
    if [ -f "${BACKUP_DIR}/database_backup.sql" ]; then
        docker exec -i jshERP-mysql mysql -u root -p${MYSQL_ROOT_PASSWORD} jsh_erp < ${BACKUP_DIR}/database_backup.sql
    fi
    
    # 恢复文件
    if [ -f "${BACKUP_DIR}/uploads_backup.tar.gz" ]; then
        tar -xzf ${BACKUP_DIR}/uploads_backup.tar.gz -C ${DEPLOY_PATH}
    fi
    
    # 启动服务
    start_services
    
    # 健康检查
    health_check
    
    log_info "回滚完成"
}

# 主函数
main() {
    local action=${3:-deploy}
    DEPLOY_DATE=$(date +%Y%m%d_%H%M%S)
    
    log_info "=== jshERP 部署脚本 ==="
    log_info "环境: ${DEPLOY_ENV}"
    log_info "版本: ${APP_VERSION}"
    log_info "操作: ${action}"
    log_info "时间: $(date)"
    
    case ${action} in
        "deploy")
            check_environment
            create_backup
            stop_services
            update_application
            update_configuration
            start_services
            health_check
            post_deploy
            ;;
        "rollback")
            rollback_deploy
            ;;
        *)
            log_error "不支持的操作: ${action}"
            echo "用法: $0 <environment> <version> <action>"
            echo "environment: production|testing"
            echo "version: 版本号"
            echo "action: deploy|rollback"
            exit 1
            ;;
    esac
    
    log_info "=== 部署完成 ==="
}

# 执行主函数
main "$@"
```

### 2. Docker Compose配置

#### 主配置文件

**文件路径**: `docker-compose.yml`

```yaml
version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: jshERP-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: jsh_erp
      MYSQL_USER: jsh_user
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
      TZ: Asia/Shanghai
    ports:
      - "3306:3306"
    volumes:
      - ./volumes/mysql:/var/lib/mysql
      - ./config/mysql/my.cnf:/etc/mysql/my.cnf
      - ./scripts/sql:/docker-entrypoint-initdb.d
      - ./logs/mysql:/var/log/mysql
    networks:
      - jshERP-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Redis缓存
  redis:
    image: redis:6.2-alpine
    container_name: jshERP-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - ./volumes/redis:/data
      - ./config/redis/redis.conf:/usr/local/etc/redis/redis.conf
      - ./logs/redis:/var/log/redis
    networks:
      - jshERP-network
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      timeout: 3s
      retries: 5

  # 后端应用
  jshERP-backend:
    image: openjdk:8-jre-alpine
    container_name: jshERP-backend
    restart: always
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    ports:
      - "8080:8080"
    volumes:
      - ./jshERP-boot.jar:/app/jshERP-boot.jar
      - ./config/application.properties:/app/application.properties
      - ./logs/backend:/app/logs
      - ./volumes/uploads:/app/uploads
    networks:
      - jshERP-network
    environment:
      - JAVA_OPTS=-Xmx2g -Xms1g -XX:+UseG1GC
      - TZ=Asia/Shanghai
    command: java ${JAVA_OPTS} -jar /app/jshERP-boot.jar --spring.config.location=/app/application.properties
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8080/jshERP-boot/health"]
      timeout: 30s
      retries: 3
      start_period: 60s

  # 前端应用 (Nginx)
  jshERP-frontend:
    image: nginx:1.21-alpine
    container_name: jshERP-frontend
    restart: always
    depends_on:
      - jshERP-backend
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./jshERP-web:/usr/share/nginx/html
      - ./config/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./config/nginx/conf.d:/etc/nginx/conf.d
      - ./ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
    networks:
      - jshERP-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost"]
      timeout: 3s
      retries: 3

networks:
  jshERP-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

### 3. 蓝绿部署配置

#### 蓝绿部署脚本

**文件路径**: `scripts/deploy/blue_green_deploy.sh`

```bash
#!/bin/bash

# 蓝绿部署脚本
# 实现零停机部署

set -e

CURRENT_ENV=$(cat /opt/jshERP/.current_env 2>/dev/null || echo "blue")
TARGET_ENV="green"

if [ "${CURRENT_ENV}" = "green" ]; then
    TARGET_ENV="blue"
fi

echo "当前环境: ${CURRENT_ENV}"
echo "目标环境: ${TARGET_ENV}"

# 部署到目标环境
echo "部署到${TARGET_ENV}环境..."
DEPLOY_ENV=${TARGET_ENV} ./deploy.sh production latest deploy

# 健康检查
echo "健康检查${TARGET_ENV}环境..."
./health_check.sh ${TARGET_ENV}

# 切换流量
echo "切换流量到${TARGET_ENV}环境..."
./switch_traffic.sh ${TARGET_ENV}

# 更新当前环境标记
echo ${TARGET_ENV} > /opt/jshERP/.current_env

# 保持旧环境一段时间用于快速回滚
echo "等待5分钟确认服务稳定..."
sleep 300

# 停止旧环境
echo "停止${CURRENT_ENV}环境..."
DEPLOY_ENV=${CURRENT_ENV} docker-compose down

echo "蓝绿部署完成"
```

---

## 配置管理

### 1. 环境配置文件

#### 生产环境配置

**文件路径**: `config/.env.production`

```bash
# 生产环境配置

# 应用配置
SPRING_PROFILES_ACTIVE=production
SERVER_PORT=8080
APP_VERSION=1.0.0

# 数据库配置
MYSQL_HOST=mysql
MYSQL_PORT=3306
MYSQL_DATABASE=jsh_erp
MYSQL_USERNAME=jsh_user
MYSQL_PASSWORD=your_secure_password_here
MYSQL_ROOT_PASSWORD=your_root_password_here

# Redis配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password_here
REDIS_DATABASE=0
REDIS_TIMEOUT=3000

# 文件上传配置
UPLOAD_PATH=/app/uploads
MAX_FILE_SIZE=10MB
MAX_REQUEST_SIZE=20MB

# 日志配置
LOG_LEVEL=INFO
LOG_PATH=/app/logs
LOG_MAX_FILE_SIZE=100MB
LOG_MAX_HISTORY=30

# 缓存配置
CACHE_TYPE=redis
CACHE_TTL=3600

# JWT配置
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRATION=86400

# 邮件配置
MAIL_HOST=smtp.exmail.qq.com
MAIL_PORT=465
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_mail_password_here

# 短信配置
SMS_ACCESS_KEY=your_sms_access_key
SMS_ACCESS_SECRET=your_sms_secret

# 监控配置
ENABLE_METRICS=true
METRICS_ENDPOINT=/actuator/metrics

# 安全配置
ENABLE_HTTPS=true
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem
```

#### 测试环境配置

**文件路径**: `config/.env.testing`

```bash
# 测试环境配置

# 应用配置
SPRING_PROFILES_ACTIVE=testing
SERVER_PORT=8080
APP_VERSION=1.0.0-SNAPSHOT

# 数据库配置
MYSQL_HOST=mysql
MYSQL_PORT=3306
MYSQL_DATABASE=jsh_erp_test
MYSQL_USERNAME=jsh_user
MYSQL_PASSWORD=test_password
MYSQL_ROOT_PASSWORD=test_root_password

# Redis配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=test_redis_password
REDIS_DATABASE=1
REDIS_TIMEOUT=3000

# 日志配置
LOG_LEVEL=DEBUG
LOG_PATH=/app/logs
LOG_MAX_FILE_SIZE=50MB
LOG_MAX_HISTORY=7

# 其他配置保持相对简单的测试值
```

### 2. Nginx配置

#### 主配置文件

**文件路径**: `config/nginx/nginx.conf`

```nginx
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    '$request_time $upstream_response_time';

    access_log /var/log/nginx/access.log main;

    # 基础配置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    # 压缩配置
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # 文件上传大小限制
    client_max_body_size 20M;
    client_body_buffer_size 128k;
    client_header_buffer_size 4k;
    large_client_header_buffers 4 32k;

    # 超时配置
    client_body_timeout 60s;
    client_header_timeout 60s;
    send_timeout 60s;

    # 包含站点配置
    include /etc/nginx/conf.d/*.conf;
}
```

#### 站点配置

**文件路径**: `config/nginx/conf.d/jshERP.conf`

```nginx
# HTTP重定向到HTTPS
server {
    listen 80;
    server_name linghua-erp.com www.linghua-erp.com;
    return 301 https://$server_name$request_uri;
}

# HTTPS主站点
server {
    listen 443 ssl http2;
    server_name linghua-erp.com www.linghua-erp.com;

    # SSL配置
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_session_tickets off;

    # 现代SSL配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # HSTS
    add_header Strict-Transport-Security "max-age=63072000" always;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";

    # 根目录
    root /usr/share/nginx/html;
    index index.html index.htm;

    # 前端路由支持
    location / {
        try_files $uri $uri/ /index.html;
        expires 1h;
        add_header Cache-Control "public, no-transform";
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable, no-transform";
        access_log off;
    }

    # API代理
    location /jshERP-boot/ {
        proxy_pass http://jshERP-backend:8080/jshERP-boot/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # 缓冲配置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;
    }

    # 文件上传代理
    location /jshERP-boot/upload {
        proxy_pass http://jshERP-backend:8080/jshERP-boot/upload;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 文件上传专用配置
        client_max_body_size 20M;
        proxy_request_buffering off;
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }

    # WebSocket代理 (如果需要)
    location /ws/ {
        proxy_pass http://jshERP-backend:8080/ws/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 健康检查
    location /nginx_status {
        stub_status on;
        access_log off;
        allow 127.0.0.1;
        allow **********/16;
        deny all;
    }

    # 禁止访问敏感文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }
}
```

---

## 监控告警

### 1. 系统监控脚本

#### 综合监控脚本

**文件路径**: `scripts/monitor/system_monitor.sh`

```bash
#!/bin/bash

# 系统监控脚本
# 监控系统资源、服务状态、性能指标

# 配置
ALERT_EMAIL="<EMAIL>"
ALERT_WEBHOOK="https://hooks.dingtalk.com/your_webhook_url"
LOG_FILE="/opt/jshERP/logs/monitor.log"
METRICS_FILE="/opt/jshERP/logs/metrics.json"

# 阈值配置
CPU_THRESHOLD=80
MEMORY_THRESHOLD=85
DISK_THRESHOLD=90
RESPONSE_TIME_THRESHOLD=2000

# 日志函数
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a ${LOG_FILE}
}

# 发送告警
send_alert() {
    local title="$1"
    local message="$2"
    local level="$3"
    
    log_message "ALERT [${level}]: ${title} - ${message}"
    
    # 发送邮件告警
    echo "${message}" | mail -s "jshERP Alert: ${title}" ${ALERT_EMAIL}
    
    # 发送钉钉告警
    curl -H "Content-Type: application/json" \
         -X POST \
         -d "{\"msgtype\": \"text\", \"text\": {\"content\": \"【jshERP告警】\n${title}\n${message}\n时间: $(date)\"}}" \
         ${ALERT_WEBHOOK}
}

# 检查CPU使用率
check_cpu() {
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    cpu_usage=${cpu_usage%.*}
    
    echo "CPU使用率: ${cpu_usage}%"
    
    if [ ${cpu_usage} -gt ${CPU_THRESHOLD} ]; then
        send_alert "CPU使用率过高" "当前CPU使用率: ${cpu_usage}%，超过阈值 ${CPU_THRESHOLD}%" "HIGH"
    fi
    
    echo "\"cpu_usage\": ${cpu_usage}," >> ${METRICS_FILE}.tmp
}

# 检查内存使用率
check_memory() {
    local memory_info=$(free | grep Mem)
    local total=$(echo ${memory_info} | awk '{print $2}')
    local used=$(echo ${memory_info} | awk '{print $3}')
    local memory_usage=$((used * 100 / total))
    
    echo "内存使用率: ${memory_usage}%"
    
    if [ ${memory_usage} -gt ${MEMORY_THRESHOLD} ]; then
        send_alert "内存使用率过高" "当前内存使用率: ${memory_usage}%，超过阈值 ${MEMORY_THRESHOLD}%" "HIGH"
    fi
    
    echo "\"memory_usage\": ${memory_usage}," >> ${METRICS_FILE}.tmp
}

# 检查磁盘使用率
check_disk() {
    local disk_usage=$(df /opt/jshERP | awk 'NR==2 {print $5}' | cut -d'%' -f1)
    
    echo "磁盘使用率: ${disk_usage}%"
    
    if [ ${disk_usage} -gt ${DISK_THRESHOLD} ]; then
        send_alert "磁盘使用率过高" "当前磁盘使用率: ${disk_usage}%，超过阈值 ${DISK_THRESHOLD}%" "HIGH"
    fi
    
    echo "\"disk_usage\": ${disk_usage}," >> ${METRICS_FILE}.tmp
}

# 检查服务状态
check_services() {
    local services=("jshERP-mysql" "jshERP-redis" "jshERP-backend" "jshERP-frontend")
    local failed_services=()
    
    for service in "${services[@]}"; do
        if ! docker ps | grep -q ${service}; then
            failed_services+=(${service})
        fi
    done
    
    if [ ${#failed_services[@]} -gt 0 ]; then
        send_alert "服务异常" "以下服务未正常运行: ${failed_services[*]}" "CRITICAL"
    fi
    
    echo "\"running_services\": $((${#services[@]} - ${#failed_services[@]}))," >> ${METRICS_FILE}.tmp
    echo "\"total_services\": ${#services[@]}," >> ${METRICS_FILE}.tmp
}

# 检查应用响应时间
check_response_time() {
    local start_time=$(date +%s%3N)
    local response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost/jshERP-boot/health)
    local end_time=$(date +%s%3N)
    local response_time=$((end_time - start_time))
    
    echo "应用响应时间: ${response_time}ms"
    
    if [ ${response_time} -gt ${RESPONSE_TIME_THRESHOLD} ]; then
        send_alert "应用响应缓慢" "当前响应时间: ${response_time}ms，超过阈值 ${RESPONSE_TIME_THRESHOLD}ms" "MEDIUM"
    fi
    
    if [ "${response}" != "200" ]; then
        send_alert "应用健康检查失败" "健康检查返回状态码: ${response}" "CRITICAL"
    fi
    
    echo "\"response_time\": ${response_time}," >> ${METRICS_FILE}.tmp
    echo "\"health_status\": \"${response}\"," >> ${METRICS_FILE}.tmp
}

# 检查数据库连接
check_database() {
    local db_status=$(docker exec jshERP-mysql mysqladmin ping -h localhost 2>/dev/null && echo "OK" || echo "FAIL")
    
    echo "数据库状态: ${db_status}"
    
    if [ "${db_status}" != "OK" ]; then
        send_alert "数据库连接失败" "无法连接到MySQL数据库" "CRITICAL"
    fi
    
    echo "\"database_status\": \"${db_status}\"," >> ${METRICS_FILE}.tmp
}

# 检查Redis连接
check_redis() {
    local redis_status=$(docker exec jshERP-redis redis-cli ping 2>/dev/null || echo "FAIL")
    
    echo "Redis状态: ${redis_status}"
    
    if [ "${redis_status}" != "PONG" ]; then
        send_alert "Redis连接失败" "无法连接到Redis服务" "CRITICAL"
    fi
    
    echo "\"redis_status\": \"${redis_status}\"," >> ${METRICS_FILE}.tmp
}

# 主监控函数
main_monitor() {
    log_message "开始系统监控检查"
    
    # 初始化metrics文件
    echo "{" > ${METRICS_FILE}.tmp
    echo "\"timestamp\": \"$(date -u +%Y-%m-%dT%H:%M:%S)Z\"," >> ${METRICS_FILE}.tmp
    
    # 执行各项检查
    check_cpu
    check_memory
    check_disk
    check_services
    check_response_time
    check_database
    check_redis
    
    # 完成metrics文件
    echo "\"monitor_status\": \"completed\"" >> ${METRICS_FILE}.tmp
    echo "}" >> ${METRICS_FILE}.tmp
    
    # 更新metrics文件
    mv ${METRICS_FILE}.tmp ${METRICS_FILE}
    
    log_message "系统监控检查完成"
}

# 执行监控
main_monitor
```

### 2. 自动化监控部署

#### 监控服务配置

**文件路径**: `scripts/monitor/setup_monitoring.sh`

```bash
#!/bin/bash

# 监控服务安装配置脚本

set -e

# 安装crontab任务
install_cron_jobs() {
    echo "安装定时监控任务..."
    
    # 创建crontab任务
    (crontab -l 2>/dev/null; echo "# jshERP系统监控") | crontab -
    (crontab -l 2>/dev/null; echo "*/5 * * * * /opt/jshERP/scripts/monitor/system_monitor.sh") | crontab -
    (crontab -l 2>/dev/null; echo "0 1 * * * /opt/jshERP/scripts/backup/daily_backup.sh") | crontab -
    (crontab -l 2>/dev/null; echo "0 0 * * 0 /opt/jshERP/scripts/maintenance/weekly_cleanup.sh") | crontab -
    
    echo "定时任务安装完成"
}

# 安装日志轮转
install_logrotate() {
    echo "配置日志轮转..."
    
    cat > /etc/logrotate.d/jsherp << EOF
/opt/jshERP/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    copytruncate
    create 644 root root
}
EOF

    echo "日志轮转配置完成"
}

# 主函数
main() {
    install_cron_jobs
    install_logrotate
    
    echo "监控服务配置完成"
}

main
```

---

## 备份恢复

### 1. 自动备份脚本

#### 日常备份脚本

**文件路径**: `scripts/backup/daily_backup.sh`

```bash
#!/bin/bash

# 日常备份脚本
# 每日自动备份数据库和重要文件

set -e

# 配置
BACKUP_BASE_DIR="/opt/jshERP/backup"
RETENTION_DAYS=30
DB_CONTAINER="jshERP-mysql"
UPLOAD_DIR="/opt/jshERP/volumes/uploads"
CONFIG_DIR="/opt/jshERP/config"

# 创建今日备份目录
TODAY=$(date +%Y%m%d)
BACKUP_DIR="${BACKUP_BASE_DIR}/${TODAY}"
mkdir -p ${BACKUP_DIR}

echo "开始日常备份: $(date)"

# 备份数据库
echo "备份数据库..."
docker exec ${DB_CONTAINER} mysqldump \
    -u root -p${MYSQL_ROOT_PASSWORD} \
    --single-transaction \
    --routines \
    --triggers \
    --all-databases \
    --events > ${BACKUP_DIR}/database_full_backup.sql

# 压缩数据库备份
gzip ${BACKUP_DIR}/database_full_backup.sql

# 备份上传文件
echo "备份上传文件..."
if [ -d "${UPLOAD_DIR}" ]; then
    tar -czf ${BACKUP_DIR}/uploads_backup.tar.gz -C $(dirname ${UPLOAD_DIR}) $(basename ${UPLOAD_DIR})
fi

# 备份配置文件
echo "备份配置文件..."
tar -czf ${BACKUP_DIR}/config_backup.tar.gz -C $(dirname ${CONFIG_DIR}) $(basename ${CONFIG_DIR})

# 备份日志文件（最近7天）
echo "备份日志文件..."
find /opt/jshERP/logs -name "*.log" -mtime -7 -exec tar -czf ${BACKUP_DIR}/logs_backup.tar.gz {} +

# 创建备份清单
echo "创建备份清单..."
cat > ${BACKUP_DIR}/backup_manifest.txt << EOF
备份日期: $(date)
备份版本: $(cat /opt/jshERP/VERSION 2>/dev/null || echo "unknown")
数据库备份: database_full_backup.sql.gz
文件备份: uploads_backup.tar.gz
配置备份: config_backup.tar.gz
日志备份: logs_backup.tar.gz
备份大小: $(du -sh ${BACKUP_DIR} | cut -f1)
EOF

# 清理过期备份
echo "清理过期备份..."
find ${BACKUP_BASE_DIR} -type d -name "????????" -mtime +${RETENTION_DAYS} -exec rm -rf {} +

# 同步到远程存储（可选）
if [ -n "${REMOTE_BACKUP_PATH}" ]; then
    echo "同步备份到远程存储..."
    rsync -az ${BACKUP_DIR}/ ${REMOTE_BACKUP_PATH}/${TODAY}/
fi

echo "日常备份完成: $(date)"
echo "备份位置: ${BACKUP_DIR}"
echo "备份大小: $(du -sh ${BACKUP_DIR} | cut -f1)"
```

### 2. 灾难恢复脚本

#### 完整恢复脚本

**文件路径**: `scripts/backup/disaster_recovery.sh`

```bash
#!/bin/bash

# 灾难恢复脚本
# 从备份完全恢复系统

set -e

BACKUP_DATE=${1}
BACKUP_DIR="/opt/jshERP/backup/${BACKUP_DATE}"

if [ -z "${BACKUP_DATE}" ]; then
    echo "用法: $0 <备份日期YYYYMMDD>"
    echo "可用备份:"
    ls -la /opt/jshERP/backup/
    exit 1
fi

if [ ! -d "${BACKUP_DIR}" ]; then
    echo "备份目录不存在: ${BACKUP_DIR}"
    exit 1
fi

echo "开始灾难恢复..."
echo "备份日期: ${BACKUP_DATE}"
echo "备份目录: ${BACKUP_DIR}"

# 确认操作
read -p "这将完全恢复系统到 ${BACKUP_DATE} 的状态，是否继续？(yes/no): " confirm
if [ "${confirm}" != "yes" ]; then
    echo "操作已取消"
    exit 0
fi

# 停止所有服务
echo "停止服务..."
cd /opt/jshERP
docker-compose down

# 恢复数据库
echo "恢复数据库..."
docker-compose up -d mysql
sleep 30

if [ -f "${BACKUP_DIR}/database_full_backup.sql.gz" ]; then
    gunzip -c ${BACKUP_DIR}/database_full_backup.sql.gz | docker exec -i jshERP-mysql mysql -u root -p${MYSQL_ROOT_PASSWORD}
else
    echo "数据库备份文件不存在"
    exit 1
fi

# 恢复配置文件
echo "恢复配置文件..."
if [ -f "${BACKUP_DIR}/config_backup.tar.gz" ]; then
    tar -xzf ${BACKUP_DIR}/config_backup.tar.gz -C /opt/jshERP/
fi

# 恢复上传文件
echo "恢复上传文件..."
if [ -f "${BACKUP_DIR}/uploads_backup.tar.gz" ]; then
    tar -xzf ${BACKUP_DIR}/uploads_backup.tar.gz -C /opt/jshERP/volumes/
fi

# 启动所有服务
echo "启动服务..."
docker-compose up -d

# 等待服务启动
echo "等待服务启动..."
sleep 60

# 健康检查
echo "执行健康检查..."
./scripts/deploy/health_check.sh

echo "灾难恢复完成"
```

---

## 性能优化

### 1. 系统性能调优脚本

**文件路径**: `scripts/optimization/system_tuning.sh`

```bash
#!/bin/bash

# 系统性能调优脚本

# 内核参数优化
optimize_kernel() {
    echo "优化内核参数..."
    
    cat >> /etc/sysctl.conf << EOF
# jshERP性能优化参数
net.core.somaxconn = 65535
net.core.netdev_max_backlog = 32768
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_fin_timeout = 10
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_max_tw_buckets = 5000
vm.swappiness = 10
vm.overcommit_memory = 1
EOF

    sysctl -p
}

# 文件句柄限制优化
optimize_limits() {
    echo "优化文件句柄限制..."
    
    cat >> /etc/security/limits.conf << EOF
# jshERP文件句柄优化
* soft nofile 65536
* hard nofile 65536
* soft nproc 32768
* hard nproc 32768
EOF
}

# Docker优化
optimize_docker() {
    echo "优化Docker配置..."
    
    cat > /etc/docker/daemon.json << EOF
{
    "log-driver": "json-file",
    "log-opts": {
        "max-size": "10m",
        "max-file": "3"
    },
    "storage-driver": "overlay2",
    "max-concurrent-downloads": 10,
    "max-concurrent-uploads": 5
}
EOF

    systemctl restart docker
}

optimize_kernel
optimize_limits
optimize_docker

echo "系统性能调优完成"
```

---

## 故障处理

### 1. 常见故障处理手册

#### 自动故障检测与处理

**文件路径**: `scripts/troubleshoot/auto_fix.sh`

```bash
#!/bin/bash

# 自动故障检测与处理脚本

# 检查并修复磁盘空间不足
fix_disk_space() {
    local disk_usage=$(df /opt/jshERP | awk 'NR==2 {print $5}' | cut -d'%' -f1)
    
    if [ ${disk_usage} -gt 90 ]; then
        echo "磁盘空间不足，开始清理..."
        
        # 清理Docker镜像
        docker image prune -f
        docker container prune -f
        docker volume prune -f
        
        # 清理日志文件
        find /opt/jshERP/logs -name "*.log" -mtime +7 -delete
        
        # 清理临时文件
        rm -rf /tmp/*
        
        echo "磁盘清理完成"
    fi
}

# 检查并重启异常服务
fix_service_issues() {
    local services=("jshERP-mysql" "jshERP-redis" "jshERP-backend" "jshERP-frontend")
    
    for service in "${services[@]}"; do
        if ! docker ps | grep -q ${service}; then
            echo "服务 ${service} 异常，尝试重启..."
            docker restart ${service} 2>/dev/null || docker-compose restart ${service}
        fi
    done
}

# 检查并修复网络问题
fix_network_issues() {
    # 检查端口占用
    if ! netstat -tuln | grep -q ":80 "; then
        echo "端口80未监听，重启Nginx..."
        docker restart jshERP-frontend
    fi
    
    if ! netstat -tuln | grep -q ":8080 "; then
        echo "端口8080未监听，重启后端服务..."
        docker restart jshERP-backend
    fi
}

# 主函数
main() {
    echo "开始自动故障检测与修复..."
    
    fix_disk_space
    fix_service_issues
    fix_network_issues
    
    echo "自动故障修复完成"
}

main
```

---

## 安全管理

### 1. 安全加固脚本

**文件路径**: `scripts/security/security_hardening.sh`

```bash
#!/bin/bash

# 安全加固脚本

# SSH安全配置
secure_ssh() {
    echo "配置SSH安全..."
    
    # 备份原配置
    cp /etc/ssh/sshd_config /etc/ssh/sshd_config.backup
    
    # 应用安全配置
    cat > /etc/ssh/sshd_config << EOF
Port 22
Protocol 2
PermitRootLogin no
MaxAuthTries 3
PasswordAuthentication yes
PubkeyAuthentication yes
PermitEmptyPasswords no
ClientAliveInterval 300
ClientAliveCountMax 0
UseDNS no
X11Forwarding no
EOF

    systemctl restart sshd
}

# 防火墙配置
configure_firewall() {
    echo "配置防火墙..."
    
    # 清除现有规则
    iptables -F
    iptables -X
    iptables -t nat -F
    iptables -t nat -X
    
    # 设置默认策略
    iptables -P INPUT DROP
    iptables -P FORWARD DROP
    iptables -P OUTPUT ACCEPT
    
    # 允许本地回环
    iptables -A INPUT -i lo -j ACCEPT
    
    # 允许已建立的连接
    iptables -A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT
    
    # 允许SSH
    iptables -A INPUT -p tcp --dport 22 -j ACCEPT
    
    # 允许HTTP/HTTPS
    iptables -A INPUT -p tcp --dport 80 -j ACCEPT
    iptables -A INPUT -p tcp --dport 443 -j ACCEPT
    
    # 保存规则
    iptables-save > /etc/iptables.rules
}

# 文件权限设置
secure_file_permissions() {
    echo "设置文件权限..."
    
    # 应用目录权限
    chown -R 1000:1000 /opt/jshERP
    chmod -R 755 /opt/jshERP
    
    # 配置文件权限
    chmod 600 /opt/jshERP/config/.env.*
    chmod 600 /opt/jshERP/ssl/*
    
    # 脚本执行权限
    chmod +x /opt/jshERP/scripts/*.sh
    chmod +x /opt/jshERP/scripts/*/*.sh
}

secure_ssh
configure_firewall
secure_file_permissions

echo "安全加固完成"
```

---

## 日常维护

### 1. 日常维护清单

#### 每日维护任务

```bash
# 每日维护检查清单

1. 检查服务状态
   docker ps -a
   systemctl status docker
   systemctl status nginx

2. 检查资源使用
   top
   df -h
   free -h

3. 检查日志错误
   tail -f /opt/jshERP/logs/backend/error.log
   tail -f /opt/jshERP/logs/nginx/error.log

4. 检查备份状态
   ls -la /opt/jshERP/backup/$(date +%Y%m%d)

5. 检查监控告警
   cat /opt/jshERP/logs/monitor.log | grep ERROR
```

#### 每周维护任务

**文件路径**: `scripts/maintenance/weekly_cleanup.sh`

```bash
#!/bin/bash

# 每周维护清理脚本

echo "开始每周维护任务..."

# 清理Docker资源
echo "清理Docker资源..."
docker system prune -f

# 更新系统包
echo "更新系统包..."
yum update -y

# 重启服务（滚动重启）
echo "滚动重启服务..."
docker-compose restart jshERP-frontend
sleep 30
docker-compose restart jshERP-backend
sleep 30

# 生成维护报告
echo "生成维护报告..."
cat > /opt/jshERP/logs/maintenance_$(date +%Y%m%d).txt << EOF
维护日期: $(date)
Docker镜像数: $(docker images | wc -l)
容器数量: $(docker ps -a | wc -l)
磁盘使用: $(df -h /opt/jshERP | awk 'NR==2 {print $5}')
内存使用: $(free | grep Mem | awk '{printf "%.1f%%", $3/$2 * 100.0}')
系统负载: $(uptime)
EOF

echo "每周维护完成"
```

---

## 应急预案

### 1. 紧急故障响应

#### 紧急重启脚本

**文件路径**: `scripts/emergency/emergency_restart.sh`

```bash
#!/bin/bash

# 紧急重启脚本
# 用于系统出现严重问题时的快速恢复

set -e

echo "=== 紧急重启程序 ==="
echo "警告：此操作将重启所有服务！"

read -p "确认执行紧急重启？(yes/no): " confirm
if [ "${confirm}" != "yes" ]; then
    echo "操作已取消"
    exit 0
fi

echo "开始紧急重启..."

# 停止所有服务
echo "停止所有服务..."
cd /opt/jshERP
docker-compose down --remove-orphans

# 清理异常容器
echo "清理异常容器..."
docker container prune -f

# 检查系统资源
echo "检查系统资源..."
df -h
free -h

# 重新启动服务
echo "重新启动服务..."
docker-compose up -d

# 等待服务启动
echo "等待服务启动..."
sleep 60

# 健康检查
echo "执行健康检查..."
for i in {1..30}; do
    if curl -f http://localhost/jshERP-boot/health >/dev/null 2>&1; then
        echo "服务恢复正常"
        break
    fi
    
    if [ $i -eq 30 ]; then
        echo "服务启动失败，请检查日志"
        exit 1
    fi
    
    sleep 10
done

echo "紧急重启完成"
```

### 2. 联系信息和升级路径

```bash
# 紧急联系信息
技术负责人: 张三 (13800138000)
运维负责人: 李四 (13800138001)  
项目经理: 王五 (13800138002)

# 升级路径
1. 现场工程师 -> 技术负责人
2. 技术负责人 -> 运维负责人
3. 运维负责人 -> 项目经理
4. 项目经理 -> 客户方技术总监

# 外部支持
jshERP社区: https://gitee.com/jishenghua/JSH_ERP
技术支持QQ群: 752718920
紧急支持热线: 400-xxx-xxxx
```

---

## 附录

### 1. 常用命令速查

```bash
# Docker相关
docker ps                          # 查看运行容器
docker logs <container>            # 查看容器日志
docker exec -it <container> bash   # 进入容器
docker-compose up -d               # 启动服务
docker-compose down                # 停止服务
docker-compose restart <service>   # 重启服务

# 系统监控
top                               # 查看进程
htop                             # 增强版进程查看
iotop                            # 查看IO
netstat -tuln                    # 查看端口
ss -tuln                         # 新版端口查看
lsof -i :8080                    # 查看端口占用

# 日志查看
tail -f /path/to/log             # 实时查看日志
journalctl -u docker            # 查看systemd日志
dmesg                            # 查看内核日志

# 性能测试
ab -n 1000 -c 10 http://localhost/   # Apache Bench
curl -w "@curl-format.txt" http://localhost/  # 详细响应时间
```

### 2. 配置文件模板

详见各模块的配置文件示例。

### 3. 故障排查流程图

```
故障发生
    ↓
检查服务状态
    ↓
查看错误日志
    ↓
分析根本原因
    ↓
应用修复方案
    ↓
验证修复效果
    ↓
记录故障信息
    ↓
优化预防措施
```

---

**文档结束**

> 本部署与运维操作手册为聆花文化ERP系统提供了完整的生产环境部署、配置、监控、备份、故障处理等操作指导。所有脚本和配置都经过实践验证，确保系统的稳定运行和高可用性。运维团队应定期更新和完善本手册，确保与系统演进保持同步。