# jshERP系统设计文档

## 1. 系统概述

### 1.1 项目简介
jshERP（管伊佳ERP）是一个基于Spring Boot + Vue的企业资源规划(ERP)系统，采用多租户SaaS架构设计，主要面向中小企业的进销存管理和财务管理需求。

### 1.2 项目信息
- **项目版本**: 3.5.0
- **开发模式**: 前后端分离
- **许可证**: GPL-3.0开源协议
- **多语言支持**: 支持全球73种语言
- **部署方式**: 支持单机部署和云平台部署

### 1.3 业务范围
- 商品管理（商品档案、分类、属性、库存）
- 采购管理（采购申请、订单、入库、退货）
- 销售管理（销售订单、出库、退货、零售）
- 库存管理（调拨、盘点、组装、拆卸）
- 财务管理（收付款、应收应付、账户管理）
- 报表分析（进销存报表、财务报表、统计分析）
- 系统管理（用户权限、基础档案、系统配置）

## 2. 系统架构设计

### 2.1 总体架构

```
┌─────────────────────────────────────────────────────────────┐
│                        Web浏览器                            │
│                     (Vue.js 2.7.16)                      │
└─────────────────────────┬───────────────────────────────────┘
                          │ HTTP/HTTPS
┌─────────────────────────▼───────────────────────────────────┐
│                       Nginx                                │
│                   (反向代理/负载均衡)                        │
└─────────────────────────┬───────────────────────────────────┘
                          │
┌─────────────────────────▼───────────────────────────────────┐
│                   Spring Boot                              │
│                   (jshERP-boot)                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ Controller  │ │   Service   │ │   Mapper    │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────┬───────────────────────────────────┘
                          │
        ┌─────────────────┼─────────────────┐
        │                 │                 │
┌───────▼────┐    ┌───────▼────┐    ┌───────▼────┐
│   MySQL    │    │   Redis    │    │   文件存储  │
│  (5.7.33)  │    │  (6.2.1)   │    │(本地/OSS) │
└────────────┘    └────────────┘    └────────────┘
```

### 2.2 技术架构

#### 2.2.1 后端技术栈
- **核心框架**: Spring Boot 2.0.0
- **持久层**: MyBatis Plus 3.0.7.1
- **数据库**: MySQL 5.7.33
- **缓存**: Redis 6.2.1
- **API文档**: Swagger2 + Swagger UI
- **权限控制**: 基于Token的RBAC权限系统
- **多租户**: 基于tenant_id的数据隔离
- **插件系统**: SpringBoot Plugin Framework
- **文件处理**: Excel导入导出、PDF生成
- **云存储**: 支持阿里云OSS

#### 2.2.2 前端技术栈
- **核心框架**: Vue.js 2.7.16
- **UI组件库**: Ant Design Vue 1.5.2
- **路由管理**: Vue Router 3.0.1
- **状态管理**: Vuex 3.1.0
- **HTTP客户端**: Axios 0.18.0
- **构建工具**: Vue CLI 3.3.0 + Webpack
- **样式处理**: Less + CSS预处理器
- **图表组件**: Viser Vue 2.4.4
- **国际化**: Vue-i18n 8.7.0

### 2.3 部署架构

#### 2.3.1 开发环境
```
开发者本机
├── 前端开发服务器 (localhost:8080)
├── 后端开发服务器 (localhost:9999)
├── MySQL数据库 (localhost:3306)
└── Redis缓存 (localhost:6379)
```

#### 2.3.2 生产环境
```
生产服务器
├── Nginx (端口80/443)
│   ├── 静态资源服务 (Vue构建文件)
│   └── API反向代理 → Spring Boot (端口9999)
├── MySQL服务 (端口3306)
├── Redis服务 (端口6379)
└── 文件存储 (本地目录/阿里云OSS)
```

## 3. 数据库设计

### 3.1 数据库架构
- **数据库类型**: MySQL 5.7.33
- **字符集**: UTF-8
- **引擎**: InnoDB (支持事务)
- **连接池**: HikariCP (Spring Boot默认)

### 3.2 多租户设计
```sql
-- 租户隔离字段（所有业务表标准字段）
tenant_id bigint(20) DEFAULT NULL COMMENT '租户id'
delete_flag varchar(1) DEFAULT '0' COMMENT '删除标记，0未删除，1删除'
```

### 3.3 核心数据表

#### 3.3.1 基础数据表
- `jsh_material` - 商品信息表
- `jsh_material_category` - 商品类别表
- `jsh_supplier` - 供应商/客户表
- `jsh_depot` - 仓库表
- `jsh_account` - 账户表
- `jsh_unit` - 计量单位表
- `jsh_person` - 经手人表

#### 3.3.2 业务单据表
- `jsh_depot_head` - 单据主表（采购、销售、库存单据）
- `jsh_depot_item` - 单据明细表
- `jsh_account_head` - 财务主表（收支、收付款单据）
- `jsh_account_item` - 财务明细表

#### 3.3.3 库存管理表
- `jsh_material_current_stock` - 当前库存表
- `jsh_material_initial_stock` - 初始库存表
- `jsh_serial_number` - 序列号管理表

#### 3.3.4 系统管理表
- `jsh_user` - 用户表
- `jsh_role` - 角色表
- `jsh_function` - 功能权限表
- `jsh_organization` - 组织架构表
- `jsh_tenant` - 租户表
- `jsh_user_business` - 用户业务权限关系表

## 4. 系统功能架构

### 4.1 功能模块图

```
jshERP系统
├── 商品管理
│   ├── 商品档案
│   ├── 商品分类
│   ├── 商品属性
│   └── 计量单位
├── 进销存管理
│   ├── 采购管理
│   │   ├── 采购申请
│   │   ├── 采购订单
│   │   ├── 采购入库
│   │   └── 采购退货
│   ├── 销售管理
│   │   ├── 销售订单
│   │   ├── 销售出库
│   │   ├── 销售退货
│   │   ├── 零售出库
│   │   └── 零售退货
│   └── 库存管理
│       ├── 其他入库
│       ├── 其他出库
│       ├── 调拨出库
│       ├── 组装单
│       └── 拆卸单
├── 财务管理
│   ├── 账户管理
│   ├── 收入单
│   ├── 支出单
│   ├── 收款单
│   ├── 付款单
│   ├── 预收款
│   ├── 预付款
│   └── 转账单
├── 往来单位
│   ├── 供应商管理
│   ├── 客户管理
│   └── 会员管理
├── 报表查询
│   ├── 入库明细
│   ├── 出库明细
│   ├── 库存报表
│   ├── 财务报表
│   └── 统计分析
├── 系统管理
│   ├── 用户管理
│   ├── 角色管理
│   ├── 权限管理
│   ├── 组织架构
│   ├── 系统配置
│   └── 日志管理
└── 基础资料
    ├── 仓库管理
    ├── 人员管理
    ├── 收支项目
    └── 结算账户
```

### 4.2 权限控制设计

#### 4.2.1 RBAC权限模型
```
用户(User) → 角色(Role) → 功能权限(Function)
    ↓           ↓              ↓
用户业务权限 → 数据权限 → 操作权限
```

#### 4.2.2 权限类型
- **菜单权限**: 控制用户可访问的菜单和页面
- **按钮权限**: 控制页面内的操作按钮显示
- **数据权限**: 控制用户可操作的数据范围
  - 仓库权限 (用户-仓库)
  - 客户权限 (用户-客户)
  - 价格权限 (查看成本价、销售价等)

#### 4.2.3 多租户权限
- **租户隔离**: 通过tenant_id实现数据完全隔离
- **租户管理**: 控制租户用户数量和使用期限
- **功能授权**: 不同租户可配置不同功能模块

## 5. 接口设计规范

### 5.1 RESTful API设计
```
GET    /api/{module}/list        # 获取列表数据
GET    /api/{module}/detail/{id} # 获取详情数据
POST   /api/{module}/add         # 新增数据
PUT    /api/{module}/edit        # 修改数据
DELETE /api/{module}/delete      # 删除数据
POST   /api/{module}/batch       # 批量操作
```

### 5.2 统一响应格式
```json
{
  "success": true,
  "message": "操作成功",
  "code": 200,
  "result": {
    "records": [],      // 数据列表
    "total": 100,       // 总记录数
    "size": 10,         // 每页大小
    "current": 1        // 当前页码
  },
  "timestamp": 1640995200000
}
```

### 5.3 错误码规范
- **200**: 操作成功
- **400**: 请求参数错误
- **401**: 未授权访问
- **403**: 权限不足
- **404**: 资源不存在
- **500**: 服务器内部错误

## 6. 安全设计

### 6.1 身份认证
- **Token认证**: 基于JWT Token的无状态认证
- **Session管理**: Redis存储用户会话信息
- **登录超时**: 可配置的会话超时时间
- **多端登录**: 支持PC端、移动端同时登录

### 6.2 数据安全
- **SQL注入防护**: MyBatis预编译语句
- **XSS防护**: 前端输入验证和后端数据清洗
- **CSRF防护**: Token验证机制
- **敏感数据加密**: 密码MD5加密存储

### 6.3 接口安全
- **访问控制**: 基于角色的权限验证
- **参数验证**: 严格的参数类型和格式验证
- **频率限制**: 防止恶意请求的限流机制
- **日志审计**: 完整的操作日志记录

## 7. 性能设计

### 7.1 数据库优化
- **索引优化**: 核心查询字段建立索引
- **查询优化**: 避免N+1查询，合理使用JOIN
- **分页查询**: 统一的分页机制
- **连接池**: HikariCP高性能连接池

### 7.2 缓存策略
- **Redis缓存**: 热点数据缓存
- **会话缓存**: 用户会话信息缓存
- **查询缓存**: 常用查询结果缓存
- **静态资源**: CDN加速静态资源访问

### 7.3 前端优化
- **代码分割**: Webpack按需加载
- **组件缓存**: Vue组件级别缓存
- **静态资源压缩**: Gzip压缩
- **浏览器缓存**: 合理的缓存策略

## 8. 扩展性设计

### 8.1 插件系统
- **插件框架**: SpringBoot Plugin Framework
- **动态加载**: 运行时插件热插拔
- **API扩展**: 插件可扩展系统API
- **UI扩展**: 插件可扩展前端界面

### 8.2 微服务准备
- **模块化设计**: 清晰的模块边界
- **接口抽象**: 良好的接口设计
- **数据隔离**: 模块间数据独立
- **服务化改造**: 便于拆分为微服务

### 8.3 集成能力
- **Open API**: 标准的REST API接口
- **Webhook**: 事件驱动的集成机制
- **数据导入导出**: Excel、CSV等格式支持
- **第三方集成**: 支持与其他系统集成

## 9. 监控和运维

### 9.1 系统监控
- **应用监控**: Spring Boot Actuator
- **性能监控**: JVM性能指标
- **数据库监控**: 连接池、慢查询监控
- **缓存监控**: Redis性能监控

### 9.2 日志管理
- **分级日志**: ERROR、WARN、INFO、DEBUG
- **日志轮转**: 按日期和大小轮转
- **操作日志**: 完整的业务操作记录
- **错误追踪**: 异常堆栈完整记录

### 9.3 备份策略
- **数据库备份**: 定期全量备份
- **增量备份**: 实时增量备份
- **文件备份**: 上传文件备份
- **配置备份**: 系统配置备份

## 10. 部署说明

### 10.1 服务器要求
- **操作系统**: Linux CentOS 7+ / Windows Server
- **内存**: 最低4GB（推荐8GB以上）
- **存储**: 最低50GB可用空间
- **网络**: 稳定的网络连接

### 10.2 软件环境
- **JDK**: Oracle JDK 1.8+
- **MySQL**: 5.7.33+
- **Redis**: 6.2.1+
- **Nginx**: 1.12.2+

### 10.3 部署步骤
1. **环境准备**: 安装JDK、MySQL、Redis、Nginx
2. **数据库初始化**: 导入jsh_erp.sql文件
3. **后端部署**: 部署jshERP.jar包
4. **前端部署**: 部署Vue构建文件到Nginx
5. **配置调整**: 修改数据库连接等配置
6. **服务启动**: 启动各个服务组件
7. **系统初始化**: 创建管理员账户和基础数据

此系统设计文档为二次开发提供了完整的技术架构参考，涵盖了系统的各个技术层面和业务层面的设计要点。