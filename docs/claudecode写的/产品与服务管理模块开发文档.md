# 商品模块聆花文化扩展开发文档

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-06-18
- **项目阶段**: 基于原有商品模块扩展
- **估算工期**: 5天
- **技术参考**: 严格遵循《jshERP_二次开发技术参考手册》第3、4、5章规范
- **基础模块**: 基于jshERP原有Material商品模块扩展

---

## 概述

本文档描述如何基于jshERP原有的商品模块(Material)进行扩展，以满足聆花文化的特殊需求。通过扩展现有商品表结构和功能，添加艺术品特有字段、多仓库管理、工艺信息记录等功能，实现聆花文化产品与服务的专业化管理。

**核心原则**: 
- 最大化复用jshERP原有商品模块架构
- 通过扩展表和字段增加聆花文化特有功能
- 保持与原有系统的完全兼容性

---

## 数据库设计

### 1. 产品与服务主表设计

**表名**: `jsh_product_service`

```sql
-- 产品与服务主表
CREATE TABLE `jsh_product_service` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `item_code` varchar(32) NOT NULL COMMENT '产品/服务编码',
  `barcode` varchar(100) DEFAULT NULL COMMENT '条形码',
  `artwork_serial` varchar(50) DEFAULT NULL COMMENT '艺术品序号',
  `item_name` varchar(200) NOT NULL COMMENT '产品/服务名称',
  `item_type` char(1) NOT NULL DEFAULT '1' COMMENT '类型(1-产品,2-服务)',
  `category_id` bigint(20) DEFAULT NULL COMMENT '分类ID',
  `category_name` varchar(100) DEFAULT NULL COMMENT '分类名称',
  
  -- 基础信息
  `specifications` varchar(200) DEFAULT NULL COMMENT '规格尺寸',
  `unit_id` bigint(20) DEFAULT NULL COMMENT '单位ID',
  `unit_name` varchar(20) DEFAULT NULL COMMENT '单位名称',
  `weight` decimal(10,3) DEFAULT '0.000' COMMENT '重量(kg)',
  `volume` decimal(10,3) DEFAULT '0.000' COMMENT '体积(m³)',
  
  -- 工艺信息
  `craft_techniques` text COMMENT '工艺技法(JSON)',
  `base_material` varchar(200) DEFAULT NULL COMMENT '底胎材质',
  `accessories` text COMMENT '配饰信息(JSON)',
  `packaging_info` text COMMENT '包装装裱信息(JSON)',
  `product_tags` text COMMENT '产品标签(JSON)',
  `description` text COMMENT '产品/服务描述',
  
  -- 图片信息
  `main_image` varchar(500) DEFAULT NULL COMMENT '主图片URL',
  `image_gallery` text COMMENT '图片库(JSON)',
  
  -- 价格体系
  `cost_price` decimal(10,2) DEFAULT '0.00' COMMENT '成本价(自动核算)',
  `retail_price` decimal(10,2) DEFAULT '0.00' COMMENT '零售价',
  `channel_price` decimal(10,2) DEFAULT '0.00' COMMENT '渠道价',
  `service_price` decimal(10,2) DEFAULT '0.00' COMMENT '服务定价',
  `price_updated_time` datetime DEFAULT NULL COMMENT '价格更新时间',
  `price_updated_user` bigint(20) DEFAULT NULL COMMENT '价格更新人',
  
  -- 库存信息
  `enable_stock` char(1) DEFAULT '1' COMMENT '是否启用库存管理',
  `safety_stock` decimal(10,2) DEFAULT '0.00' COMMENT '安全库存',
  `max_stock` decimal(10,2) DEFAULT '0.00' COMMENT '最大库存',
  `current_stock` decimal(10,2) DEFAULT '0.00' COMMENT '当前总库存',
  `available_stock` decimal(10,2) DEFAULT '0.00' COMMENT '可用库存',
  `reserved_stock` decimal(10,2) DEFAULT '0.00' COMMENT '预留库存',
  
  -- 成本核算
  `cost_calculation_method` char(1) DEFAULT '1' COMMENT '成本核算方法(1-加权平均,2-先进先出,3-后进先出,4-个别计价)',
  `material_cost` decimal(10,2) DEFAULT '0.00' COMMENT '材料成本',
  `labor_cost` decimal(10,2) DEFAULT '0.00' COMMENT '人工成本',
  `overhead_cost` decimal(10,2) DEFAULT '0.00' COMMENT '制造费用',
  `last_cost_update` datetime DEFAULT NULL COMMENT '成本最后更新时间',
  
  -- 供应商信息
  `primary_supplier_id` bigint(20) DEFAULT NULL COMMENT '主供应商ID',
  `supplier_item_code` varchar(100) DEFAULT NULL COMMENT '供应商产品编码',
  `lead_time` int(11) DEFAULT '0' COMMENT '采购周期(天)',
  `min_order_qty` decimal(10,2) DEFAULT '1.00' COMMENT '最小订购量',
  
  -- 销售信息
  `is_saleable` char(1) DEFAULT '1' COMMENT '是否可销售',
  `is_purchasable` char(1) DEFAULT '1' COMMENT '是否可采购',
  `sales_start_date` date DEFAULT NULL COMMENT '销售开始日期',
  `sales_end_date` date DEFAULT NULL COMMENT '销售结束日期',
  
  -- 质量控制
  `quality_grade` varchar(20) DEFAULT NULL COMMENT '质量等级',
  `shelf_life` int(11) DEFAULT '0' COMMENT '保质期(天)',
  `storage_conditions` varchar(500) DEFAULT NULL COMMENT '存储条件',
  
  -- 扩展信息
  `custom_field1` varchar(200) DEFAULT NULL COMMENT '自定义字段1',
  `custom_field2` varchar(200) DEFAULT NULL COMMENT '自定义字段2',
  `custom_field3` varchar(200) DEFAULT NULL COMMENT '自定义字段3',
  `extended_info` text COMMENT '扩展信息(JSON)',
  
  -- 状态信息
  `status` char(1) NOT NULL DEFAULT '1' COMMENT '状态(0-停用,1-启用,2-停产)',
  `remark` varchar(1000) DEFAULT NULL COMMENT '备注',
  
  -- 标准字段
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `delete_flag` char(1) NOT NULL DEFAULT '0' COMMENT '删除标记',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` bigint(20) DEFAULT NULL COMMENT '更新人',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_item_code` (`item_code`, `tenant_id`),
  UNIQUE KEY `uk_barcode` (`barcode`, `tenant_id`),
  KEY `idx_item_name` (`item_name`),
  KEY `idx_category` (`category_id`),
  KEY `idx_item_type` (`item_type`),
  KEY `idx_status` (`status`),
  KEY `idx_supplier` (`primary_supplier_id`),
  KEY `idx_tenant` (`tenant_id`, `delete_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品与服务主表';
```

### 2. 产品分类表设计

**表名**: `jsh_product_category`

```sql
-- 产品分类表
CREATE TABLE `jsh_product_category` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `category_code` varchar(32) NOT NULL COMMENT '分类编码',
  `category_name` varchar(100) NOT NULL COMMENT '分类名称',
  `parent_id` bigint(20) DEFAULT '0' COMMENT '父分类ID',
  `category_level` int(11) DEFAULT '1' COMMENT '分类级别',
  `category_path` varchar(500) DEFAULT NULL COMMENT '分类路径',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序序号',
  `icon_url` varchar(500) DEFAULT NULL COMMENT '分类图标',
  `description` varchar(1000) DEFAULT NULL COMMENT '分类描述',
  `is_leaf` char(1) DEFAULT '1' COMMENT '是否叶子节点',
  `status` char(1) NOT NULL DEFAULT '1' COMMENT '状态(0-停用,1-启用)',
  
  -- 标准字段
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `delete_flag` char(1) NOT NULL DEFAULT '0' COMMENT '删除标记',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` bigint(20) DEFAULT NULL COMMENT '更新人',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_category_code` (`category_code`, `tenant_id`),
  KEY `idx_parent` (`parent_id`),
  KEY `idx_level` (`category_level`),
  KEY `idx_tenant` (`tenant_id`, `delete_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品分类表';
```

### 3. 多仓库库存表设计

**表名**: `jsh_product_stock`

```sql
-- 多仓库库存表
CREATE TABLE `jsh_product_stock` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `product_id` bigint(20) NOT NULL COMMENT '产品ID',
  `warehouse_id` bigint(20) NOT NULL COMMENT '仓库ID',
  `warehouse_name` varchar(100) NOT NULL COMMENT '仓库名称',
  `warehouse_type` char(1) NOT NULL COMMENT '仓库类型(1-实体仓,2-虚拟仓)',
  
  -- 库存数量
  `current_stock` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '当前库存',
  `available_stock` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '可用库存',
  `reserved_stock` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '预留库存',
  `in_transit_stock` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '在途库存',
  `safety_stock` decimal(12,4) DEFAULT '0.0000' COMMENT '安全库存',
  `max_stock` decimal(12,4) DEFAULT '0.0000' COMMENT '最大库存',
  `min_stock` decimal(12,4) DEFAULT '0.0000' COMMENT '最小库存',
  
  -- 成本信息
  `average_cost` decimal(10,4) DEFAULT '0.0000' COMMENT '加权平均成本',
  `last_cost` decimal(10,4) DEFAULT '0.0000' COMMENT '最新成本',
  `total_cost_value` decimal(15,4) DEFAULT '0.0000' COMMENT '库存总成本',
  
  -- 库存统计
  `last_in_date` datetime DEFAULT NULL COMMENT '最后入库时间',
  `last_out_date` datetime DEFAULT NULL COMMENT '最后出库时间',
  `last_check_date` datetime DEFAULT NULL COMMENT '最后盘点时间',
  `total_in_qty` decimal(12,4) DEFAULT '0.0000' COMMENT '累计入库数量',
  `total_out_qty` decimal(12,4) DEFAULT '0.0000' COMMENT '累计出库数量',
  
  -- 库位信息
  `default_location` varchar(100) DEFAULT NULL COMMENT '默认库位',
  `location_info` text COMMENT '库位信息(JSON)',
  
  -- 标准字段
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `delete_flag` char(1) NOT NULL DEFAULT '0' COMMENT '删除标记',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` bigint(20) DEFAULT NULL COMMENT '更新人',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_product_warehouse` (`product_id`, `warehouse_id`, `tenant_id`),
  KEY `idx_warehouse` (`warehouse_id`),
  KEY `idx_current_stock` (`current_stock`),
  KEY `idx_available_stock` (`available_stock`),
  KEY `idx_tenant` (`tenant_id`, `delete_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='多仓库库存表';
```

### 4. 价格历史表设计

**表名**: `jsh_product_price_history`

```sql
-- 产品价格历史表
CREATE TABLE `jsh_product_price_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `product_id` bigint(20) NOT NULL COMMENT '产品ID',
  `price_type` char(1) NOT NULL COMMENT '价格类型(1-成本价,2-零售价,3-渠道价,4-服务价)',
  `old_price` decimal(10,2) DEFAULT '0.00' COMMENT '原价格',
  `new_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '新价格',
  `change_reason` varchar(500) DEFAULT NULL COMMENT '变更原因',
  `effective_date` datetime NOT NULL COMMENT '生效时间',
  `expiry_date` datetime DEFAULT NULL COMMENT '失效时间',
  `change_user` bigint(20) DEFAULT NULL COMMENT '变更人',
  `approval_user` bigint(20) DEFAULT NULL COMMENT '审批人',
  `approval_status` char(1) DEFAULT '1' COMMENT '审批状态(0-待审批,1-已通过,2-已拒绝)',
  
  -- 标准字段
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `delete_flag` char(1) NOT NULL DEFAULT '0' COMMENT '删除标记',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_product` (`product_id`),
  KEY `idx_price_type` (`price_type`),
  KEY `idx_effective_date` (`effective_date`),
  KEY `idx_tenant` (`tenant_id`, `delete_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品价格历史表';
```

---

## 后端服务开发

### 1. ProductServiceService.java - 产品服务管理核心服务

**文件路径**: `com.jsh.erp.product.service.ProductServiceService`

```java
package com.jsh.erp.product.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jsh.erp.exception.BusinessRunTimeException;
import com.jsh.erp.exception.JshException;
import com.jsh.erp.product.datasource.entities.ProductService;
import com.jsh.erp.product.datasource.entities.ProductServiceExample;
import com.jsh.erp.product.datasource.entities.ProductStock;
import com.jsh.erp.product.datasource.mappers.ProductServiceMapper;
import com.jsh.erp.product.datasource.mappers.ProductServiceMapperEx;
import com.jsh.erp.product.datasource.mappers.ProductStockMapper;
import com.jsh.erp.product.datasource.vo.ProductServiceVo4List;
import com.jsh.erp.service.log.LogService;
import com.jsh.erp.utils.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 产品与服务管理核心服务
 * 实现产品信息管理、库存管理、价格管理等功能
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Service
public class ProductServiceService {
    private Logger logger = LoggerFactory.getLogger(ProductServiceService.class);

    @Resource
    private ProductServiceMapper productServiceMapper;
    @Resource
    private ProductServiceMapperEx productServiceMapperEx;
    @Resource
    private ProductStockMapper productStockMapper;
    @Resource
    private ProductCategoryService productCategoryService;
    @Resource
    private ProductPriceService productPriceService;
    @Resource
    private LogService logService;

    /**
     * 创建产品或服务
     */
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public Long createProductService(JSONObject productData, HttpServletRequest request) throws Exception {
        try {
            logger.info("创建产品/服务: {}", productData.toJSONString());
            
            // 1. 验证数据
            validateProductData(productData, "create");
            
            // 2. 生成产品编码
            String itemCode = generateItemCode(productData.getString("itemType"));
            
            // 3. 创建产品实体
            ProductService productService = buildProductServiceEntity(productData, itemCode, request);
            
            // 4. 保存产品信息
            productServiceMapper.insertSelective(productService);
            Long productId = productService.getId();
            
            // 5. 初始化库存信息
            if ("1".equals(productData.getString("enableStock"))) {
                initializeProductStock(productId, productData, request);
            }
            
            // 6. 记录价格历史
            recordPriceHistory(productId, productData, "create", request);
            
            // 7. 记录操作日志
            logService.insertLog("产品管理", 
                String.format("创建产品/服务: %s [%s]", productService.getItemName(), itemCode), 
                request);
            
            logger.info("产品/服务创建成功: ID={}, Code={}", productId, itemCode);
            return productId;
            
        } catch (Exception e) {
            logger.error("创建产品/服务失败", e);
            throw new JshException("创建产品/服务失败: " + e.getMessage());
        }
    }

    /**
     * 更新产品或服务信息
     */
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public void updateProductService(Long productId, JSONObject productData, HttpServletRequest request) throws Exception {
        try {
            logger.info("更新产品/服务: ID={}, Data={}", productId, productData.toJSONString());
            
            // 1. 验证数据
            validateProductData(productData, "update");
            
            // 2. 检查产品是否存在
            ProductService existingProduct = productServiceMapper.selectByPrimaryKey(productId);
            if (existingProduct == null || "1".equals(existingProduct.getDeleteFlag())) {
                throw new BusinessRunTimeException("产品不存在", "产品ID: " + productId);
            }
            
            // 3. 检查价格变更
            checkPriceChanges(existingProduct, productData, request);
            
            // 4. 更新产品信息
            ProductService updatedProduct = buildProductServiceEntity(productData, existingProduct.getItemCode(), request);
            updatedProduct.setId(productId);
            updatedProduct.setUpdateTime(new Date());
            
            productServiceMapper.updateByPrimaryKeySelective(updatedProduct);
            
            // 5. 更新库存配置
            if ("1".equals(productData.getString("enableStock"))) {
                updateProductStockConfig(productId, productData, request);
            }
            
            // 6. 记录操作日志
            logService.insertLog("产品管理", 
                String.format("更新产品/服务: %s [%s]", updatedProduct.getItemName(), updatedProduct.getItemCode()), 
                request);
            
            logger.info("产品/服务更新成功: ID={}", productId);
            
        } catch (Exception e) {
            logger.error("更新产品/服务失败", e);
            throw new JshException("更新产品/服务失败: " + e.getMessage());
        }
    }

    /**
     * 自动核算产品成本
     */
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public BigDecimal calculateProductCost(Long productId) throws Exception {
        try {
            logger.info("自动核算产品成本: productId={}", productId);
            
            ProductService product = productServiceMapper.selectByPrimaryKey(productId);
            if (product == null) {
                throw new BusinessRunTimeException("产品不存在", "产品ID: " + productId);
            }
            
            // 1. 计算材料成本
            BigDecimal materialCost = calculateMaterialCost(productId);
            
            // 2. 计算人工成本
            BigDecimal laborCost = calculateLaborCost(productId);
            
            // 3. 计算制造费用
            BigDecimal overheadCost = calculateOverheadCost(productId);
            
            // 4. 计算总成本
            BigDecimal totalCost = materialCost.add(laborCost).add(overheadCost);
            
            // 5. 更新产品成本信息
            product.setMaterialCost(materialCost);
            product.setLaborCost(laborCost);
            product.setOverheadCost(overheadCost);
            product.setCostPrice(totalCost);
            product.setLastCostUpdate(new Date());
            
            productServiceMapper.updateByPrimaryKeySelective(product);
            
            // 6. 记录成本核算历史
            recordCostHistory(productId, materialCost, laborCost, overheadCost, totalCost);
            
            logger.info("产品成本核算完成: productId={}, totalCost={}", productId, totalCost);
            return totalCost;
            
        } catch (Exception e) {
            logger.error("产品成本核算失败", e);
            throw new JshException("产品成本核算失败: " + e.getMessage());
        }
    }

    /**
     * 获取产品实时库存
     */
    public Map<String, Object> getProductStock(Long productId) throws Exception {
        try {
            Map<String, Object> stockInfo = new HashMap<>();
            
            // 1. 获取产品基本信息
            ProductService product = productServiceMapper.selectByPrimaryKey(productId);
            if (product == null) {
                throw new BusinessRunTimeException("产品不存在", "产品ID: " + productId);
            }
            
            stockInfo.put("productId", productId);
            stockInfo.put("productName", product.getItemName());
            stockInfo.put("productCode", product.getItemCode());
            stockInfo.put("enableStock", product.getEnableStock());
            
            if (!"1".equals(product.getEnableStock())) {
                stockInfo.put("message", "该产品未启用库存管理");
                return stockInfo;
            }
            
            // 2. 获取各仓库库存
            List<ProductStock> warehouseStocks = getWarehouseStocks(productId);
            
            // 3. 计算总库存
            BigDecimal totalCurrentStock = BigDecimal.ZERO;
            BigDecimal totalAvailableStock = BigDecimal.ZERO;
            BigDecimal totalReservedStock = BigDecimal.ZERO;
            
            List<Map<String, Object>> warehouseList = new ArrayList<>();
            for (ProductStock stock : warehouseStocks) {
                Map<String, Object> warehouseInfo = new HashMap<>();
                warehouseInfo.put("warehouseId", stock.getWarehouseId());
                warehouseInfo.put("warehouseName", stock.getWarehouseName());
                warehouseInfo.put("warehouseType", stock.getWarehouseType());
                warehouseInfo.put("currentStock", stock.getCurrentStock());
                warehouseInfo.put("availableStock", stock.getAvailableStock());
                warehouseInfo.put("reservedStock", stock.getReservedStock());
                warehouseInfo.put("safetyStock", stock.getSafetyStock());
                warehouseInfo.put("averageCost", stock.getAverageCost());
                warehouseInfo.put("lastInDate", stock.getLastInDate());
                warehouseInfo.put("lastOutDate", stock.getLastOutDate());
                
                warehouseList.add(warehouseInfo);
                
                totalCurrentStock = totalCurrentStock.add(stock.getCurrentStock());
                totalAvailableStock = totalAvailableStock.add(stock.getAvailableStock());
                totalReservedStock = totalReservedStock.add(stock.getReservedStock());
            }
            
            stockInfo.put("totalCurrentStock", totalCurrentStock);
            stockInfo.put("totalAvailableStock", totalAvailableStock);
            stockInfo.put("totalReservedStock", totalReservedStock);
            stockInfo.put("safetyStock", product.getSafetyStock());
            stockInfo.put("maxStock", product.getMaxStock());
            stockInfo.put("warehouseStocks", warehouseList);
            
            // 4. 库存预警检查
            List<String> warnings = checkStockWarnings(product, totalCurrentStock, totalAvailableStock);
            stockInfo.put("warnings", warnings);
            
            return stockInfo;
            
        } catch (Exception e) {
            logger.error("获取产品库存失败", e);
            throw new JshException("获取产品库存失败: " + e.getMessage());
        }
    }

    /**
     * 批量上传产品图片
     */
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public List<String> uploadProductImages(Long productId, MultipartFile[] files, HttpServletRequest request) throws Exception {
        try {
            logger.info("上传产品图片: productId={}, fileCount={}", productId, files.length);
            
            ProductService product = productServiceMapper.selectByPrimaryKey(productId);
            if (product == null) {
                throw new BusinessRunTimeException("产品不存在", "产品ID: " + productId);
            }
            
            List<String> uploadedUrls = new ArrayList<>();
            
            for (MultipartFile file : files) {
                if (file != null && !file.isEmpty()) {
                    // 验证文件类型和大小
                    validateImageFile(file);
                    
                    // 上传文件
                    String imageUrl = uploadImageFile(file, productId);
                    uploadedUrls.add(imageUrl);
                }
            }
            
            // 更新产品图片信息
            updateProductImages(productId, uploadedUrls, request);
            
            logger.info("产品图片上传完成: productId={}, uploadCount={}", productId, uploadedUrls.size());
            return uploadedUrls;
            
        } catch (Exception e) {
            logger.error("上传产品图片失败", e);
            throw new JshException("上传产品图片失败: " + e.getMessage());
        }
    }

    /**
     * 快速编辑产品信息
     * 支持双击快速修改产品名称、尺寸、价格
     */
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public void quickEditProduct(Long productId, String field, String value, HttpServletRequest request) throws Exception {
        try {
            logger.info("快速编辑产品: productId={}, field={}, value={}", productId, field, value);
            
            ProductService product = productServiceMapper.selectByPrimaryKey(productId);
            if (product == null) {
                throw new BusinessRunTimeException("产品不存在", "产品ID: " + productId);
            }
            
            // 验证编辑字段
            validateQuickEditField(field, value);
            
            String oldValue = "";
            ProductService updateProduct = new ProductService();
            updateProduct.setId(productId);
            updateProduct.setUpdateTime(new Date());
            updateProduct.setUpdateUser(Long.parseLong(request.getParameter("userId")));
            
            switch (field) {
                case "itemName":
                    oldValue = product.getItemName();
                    updateProduct.setItemName(value);
                    break;
                case "specifications":
                    oldValue = product.getSpecifications();
                    updateProduct.setSpecifications(value);
                    break;
                case "retailPrice":
                    oldValue = product.getRetailPrice().toString();
                    updateProduct.setRetailPrice(new BigDecimal(value));
                    // 记录价格变更历史
                    recordPriceChangeHistory(productId, "2", product.getRetailPrice(), new BigDecimal(value), "快速编辑", request);
                    break;
                case "channelPrice":
                    oldValue = product.getChannelPrice().toString();
                    updateProduct.setChannelPrice(new BigDecimal(value));
                    recordPriceChangeHistory(productId, "3", product.getChannelPrice(), new BigDecimal(value), "快速编辑", request);
                    break;
                case "servicePrice":
                    oldValue = product.getServicePrice().toString();
                    updateProduct.setServicePrice(new BigDecimal(value));
                    recordPriceChangeHistory(productId, "4", product.getServicePrice(), new BigDecimal(value), "快速编辑", request);
                    break;
                default:
                    throw new BusinessRunTimeException("参数错误", "不支持快速编辑的字段: " + field);
            }
            
            productServiceMapper.updateByPrimaryKeySelective(updateProduct);
            
            // 记录操作日志
            logService.insertLog("产品管理", 
                String.format("快速编辑产品 [%s] %s: %s -> %s", product.getItemCode(), field, oldValue, value), 
                request);
            
            logger.info("快速编辑完成: productId={}, field={}", productId, field);
            
        } catch (Exception e) {
            logger.error("快速编辑产品失败", e);
            throw new JshException("快速编辑产品失败: " + e.getMessage());
        }
    }

    // 私有方法实现
    private void validateProductData(JSONObject productData, String operation) throws Exception {
        if (StringUtil.isEmpty(productData.getString("itemName"))) {
            throw new BusinessRunTimeException("参数错误", "产品/服务名称不能为空");
        }
        
        if (StringUtil.isEmpty(productData.getString("itemType"))) {
            throw new BusinessRunTimeException("参数错误", "产品/服务类型不能为空");
        }
        
        if (!"1".equals(productData.getString("itemType")) && !"2".equals(productData.getString("itemType"))) {
            throw new BusinessRunTimeException("参数错误", "产品/服务类型必须为1(产品)或2(服务)");
        }
        
        // 验证价格格式
        validatePriceFormat(productData);
    }

    private void validatePriceFormat(JSONObject productData) throws Exception {
        String[] priceFields = {"retailPrice", "channelPrice", "servicePrice"};
        
        for (String field : priceFields) {
            String priceStr = productData.getString(field);
            if (!StringUtil.isEmpty(priceStr)) {
                try {
                    BigDecimal price = new BigDecimal(priceStr);
                    if (price.compareTo(BigDecimal.ZERO) < 0) {
                        throw new BusinessRunTimeException("参数错误", field + "不能为负数");
                    }
                } catch (NumberFormatException e) {
                    throw new BusinessRunTimeException("参数错误", field + "格式不正确");
                }
            }
        }
    }

    private String generateItemCode(String itemType) {
        String prefix = "1".equals(itemType) ? "P" : "S";
        String timestamp = String.valueOf(System.currentTimeMillis());
        return prefix + timestamp.substring(timestamp.length() - 10);
    }

    private ProductService buildProductServiceEntity(JSONObject productData, String itemCode, HttpServletRequest request) {
        ProductService productService = new ProductService();
        
        // 基础信息
        productService.setItemCode(itemCode);
        productService.setBarcode(productData.getString("barcode"));
        productService.setArtworkSerial(productData.getString("artworkSerial"));
        productService.setItemName(productData.getString("itemName"));
        productService.setItemType(productData.getString("itemType"));
        productService.setCategoryId(productData.getLong("categoryId"));
        productService.setCategoryName(productData.getString("categoryName"));
        
        // 规格信息
        productService.setSpecifications(productData.getString("specifications"));
        productService.setUnitId(productData.getLong("unitId"));
        productService.setUnitName(productData.getString("unitName"));
        productService.setWeight(productData.getBigDecimal("weight"));
        productService.setVolume(productData.getBigDecimal("volume"));
        
        // 工艺信息
        productService.setCraftTechniques(productData.getString("craftTechniques"));
        productService.setBaseMaterial(productData.getString("baseMaterial"));
        productService.setAccessories(productData.getString("accessories"));
        productService.setPackagingInfo(productData.getString("packagingInfo"));
        productService.setProductTags(productData.getString("productTags"));
        productService.setDescription(productData.getString("description"));
        
        // 价格信息
        productService.setRetailPrice(productData.getBigDecimal("retailPrice"));
        productService.setChannelPrice(productData.getBigDecimal("channelPrice"));
        productService.setServicePrice(productData.getBigDecimal("servicePrice"));
        productService.setPriceUpdatedTime(new Date());
        productService.setPriceUpdatedUser(Long.parseLong(request.getParameter("userId")));
        
        // 库存配置
        productService.setEnableStock(productData.getString("enableStock"));
        productService.setSafetyStock(productData.getBigDecimal("safetyStock"));
        productService.setMaxStock(productData.getBigDecimal("maxStock"));
        
        // 其他信息
        productService.setStatus(productData.getString("status"));
        productService.setRemark(productData.getString("remark"));
        
        // 标准字段
        productService.setTenantId(Long.parseLong(request.getParameter("tenantId")));
        productService.setDeleteFlag("0");
        productService.setCreateTime(new Date());
        productService.setCreateUser(Long.parseLong(request.getParameter("userId")));
        
        return productService;
    }

    private void initializeProductStock(Long productId, JSONObject productData, HttpServletRequest request) throws Exception {
        // 获取所有仓库信息，为产品在每个仓库初始化库存记录
        List<Map<String, Object>> warehouses = getAvailableWarehouses();
        
        for (Map<String, Object> warehouse : warehouses) {
            ProductStock productStock = new ProductStock();
            productStock.setProductId(productId);
            productStock.setWarehouseId((Long) warehouse.get("id"));
            productStock.setWarehouseName((String) warehouse.get("name"));
            productStock.setWarehouseType((String) warehouse.get("type"));
            productStock.setCurrentStock(BigDecimal.ZERO);
            productStock.setAvailableStock(BigDecimal.ZERO);
            productStock.setReservedStock(BigDecimal.ZERO);
            productStock.setSafetyStock(productData.getBigDecimal("safetyStock"));
            productStock.setMaxStock(productData.getBigDecimal("maxStock"));
            productStock.setTenantId(Long.parseLong(request.getParameter("tenantId")));
            productStock.setDeleteFlag("0");
            productStock.setCreateTime(new Date());
            
            productStockMapper.insertSelective(productStock);
        }
    }

    private BigDecimal calculateMaterialCost(Long productId) throws Exception {
        // 根据BOM和当前原料价格计算材料成本
        // 这里简化实现，实际需要查询BOM表和原料价格
        return BigDecimal.ZERO;
    }

    private BigDecimal calculateLaborCost(Long productId) throws Exception {
        // 根据工艺路线和工时定额计算人工成本
        return BigDecimal.ZERO;
    }

    private BigDecimal calculateOverheadCost(Long productId) throws Exception {
        // 根据制造费用分摊规则计算制造费用
        return BigDecimal.ZERO;
    }

    private List<ProductStock> getWarehouseStocks(Long productId) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("productId", productId);
        params.put("deleteFlag", "0");
        
        // 这里需要实现具体的查询逻辑
        return new ArrayList<>();
    }

    private List<String> checkStockWarnings(ProductService product, BigDecimal currentStock, BigDecimal availableStock) {
        List<String> warnings = new ArrayList<>();
        
        // 安全库存预警
        if (product.getSafetyStock() != null && currentStock.compareTo(product.getSafetyStock()) < 0) {
            warnings.add("当前库存低于安全库存");
        }
        
        // 可用库存预警
        if (availableStock.compareTo(BigDecimal.ZERO) <= 0) {
            warnings.add("可用库存不足");
        }
        
        // 超量库存预警
        if (product.getMaxStock() != null && currentStock.compareTo(product.getMaxStock()) > 0) {
            warnings.add("当前库存超过最大库存限制");
        }
        
        return warnings;
    }

    private void validateImageFile(MultipartFile file) throws Exception {
        // 验证文件类型
        String contentType = file.getContentType();
        if (contentType == null || !contentType.startsWith("image/")) {
            throw new BusinessRunTimeException("文件类型错误", "只支持图片文件");
        }
        
        // 验证文件大小 (5MB)
        if (file.getSize() > 5 * 1024 * 1024) {
            throw new BusinessRunTimeException("文件过大", "图片文件不能超过5MB");
        }
    }

    private String uploadImageFile(MultipartFile file, Long productId) throws Exception {
        // 实现文件上传逻辑
        // 返回上传后的文件URL
        return "/uploads/products/" + productId + "/" + file.getOriginalFilename();
    }

    private void updateProductImages(Long productId, List<String> imageUrls, HttpServletRequest request) throws Exception {
        ProductService product = new ProductService();
        product.setId(productId);
        
        if (!imageUrls.isEmpty()) {
            // 设置主图片为第一张
            product.setMainImage(imageUrls.get(0));
            
            // 设置图片库
            JSONArray imageGallery = new JSONArray();
            imageGallery.addAll(imageUrls);
            product.setImageGallery(imageGallery.toJSONString());
        }
        
        product.setUpdateTime(new Date());
        product.setUpdateUser(Long.parseLong(request.getParameter("userId")));
        
        productServiceMapper.updateByPrimaryKeySelective(product);
    }

    private void validateQuickEditField(String field, String value) throws Exception {
        if (StringUtil.isEmpty(field) || StringUtil.isEmpty(value)) {
            throw new BusinessRunTimeException("参数错误", "编辑字段和值不能为空");
        }
        
        List<String> allowedFields = Arrays.asList("itemName", "specifications", "retailPrice", "channelPrice", "servicePrice");
        if (!allowedFields.contains(field)) {
            throw new BusinessRunTimeException("参数错误", "不支持编辑的字段: " + field);
        }
        
        // 验证价格字段
        if (field.endsWith("Price")) {
            try {
                BigDecimal price = new BigDecimal(value);
                if (price.compareTo(BigDecimal.ZERO) < 0) {
                    throw new BusinessRunTimeException("参数错误", "价格不能为负数");
                }
            } catch (NumberFormatException e) {
                throw new BusinessRunTimeException("参数错误", "价格格式不正确");
            }
        }
    }

    private void recordPriceHistory(Long productId, JSONObject productData, String operation, HttpServletRequest request) throws Exception {
        // 记录价格历史的实现
    }

    private void checkPriceChanges(ProductService existingProduct, JSONObject productData, HttpServletRequest request) throws Exception {
        // 检查价格变更的实现
    }

    private void updateProductStockConfig(Long productId, JSONObject productData, HttpServletRequest request) throws Exception {
        // 更新库存配置的实现
    }

    private void recordCostHistory(Long productId, BigDecimal materialCost, BigDecimal laborCost, BigDecimal overheadCost, BigDecimal totalCost) throws Exception {
        // 记录成本历史的实现
    }

    private void recordPriceChangeHistory(Long productId, String priceType, BigDecimal oldPrice, BigDecimal newPrice, String reason, HttpServletRequest request) throws Exception {
        // 记录价格变更历史的实现
    }

    private List<Map<String, Object>> getAvailableWarehouses() throws Exception {
        // 获取可用仓库列表的实现
        List<Map<String, Object>> warehouses = new ArrayList<>();
        
        // 聆花文化的仓库架构
        // 实体仓库
        warehouses.add(createWarehouseInfo(1L, "广州原料仓", "1"));
        warehouses.add(createWarehouseInfo(2L, "广州半成品仓", "1"));
        warehouses.add(createWarehouseInfo(3L, "广州成品仓", "1"));
        
        // 虚拟仓库
        warehouses.add(createWarehouseInfo(4L, "广西生产基地仓", "2"));
        warehouses.add(createWarehouseInfo(5L, "线上渠道仓", "2"));
        warehouses.add(createWarehouseInfo(6L, "线下渠道仓", "2"));
        
        return warehouses;
    }

    private Map<String, Object> createWarehouseInfo(Long id, String name, String type) {
        Map<String, Object> warehouse = new HashMap<>();
        warehouse.put("id", id);
        warehouse.put("name", name);
        warehouse.put("type", type);
        return warehouse;
    }
}
```

---

## 前端界面开发

### 1. ProductServiceList.vue - 产品服务列表页面

**文件路径**: `jshERP-web/src/views/product/ProductServiceList.vue`

```vue
<template>
  <div class="product-service-list">
    <a-card :bordered="false">
      <!-- 搜索区域 -->
      <div class="search-area">
        <a-form layout="inline" :model="searchForm">
          <a-form-item label="产品编码">
            <a-input 
              v-model:value="searchForm.itemCode" 
              placeholder="请输入产品编码"
              @press-enter="handleSearch"
            />
          </a-form-item>
          <a-form-item label="产品名称">
            <a-input 
              v-model:value="searchForm.itemName" 
              placeholder="请输入产品名称"
              @press-enter="handleSearch"
            />
          </a-form-item>
          <a-form-item label="产品分类">
            <a-tree-select
              v-model:value="searchForm.categoryId"
              :tree-data="categoryTree"
              placeholder="请选择产品分类"
              tree-default-expand-all
              allow-clear
            />
          </a-form-item>
          <a-form-item label="产品类型">
            <a-select v-model:value="searchForm.itemType" placeholder="请选择类型" allow-clear>
              <a-select-option value="1">产品</a-select-option>
              <a-select-option value="2">服务</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="状态">
            <a-select v-model:value="searchForm.status" placeholder="请选择状态" allow-clear>
              <a-select-option value="1">启用</a-select-option>
              <a-select-option value="0">停用</a-select-option>
              <a-select-option value="2">停产</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="handleSearch">
              <template #icon><SearchOutlined /></template>
              搜索
            </a-button>
            <a-button style="margin-left: 8px" @click="handleReset">
              <template #icon><ReloadOutlined /></template>
              重置
            </a-button>
          </a-form-item>
        </a-form>
      </div>

      <!-- 操作区域 -->
      <div class="action-area">
        <a-row type="flex" justify="space-between" align="middle">
          <a-col>
            <a-button type="primary" @click="handleAdd">
              <template #icon><PlusOutlined /></template>
              新增产品/服务
            </a-button>
            <a-button style="margin-left: 8px" @click="handleBatchImport">
              <template #icon><UploadOutlined /></template>
              批量导入
            </a-button>
            <a-button 
              style="margin-left: 8px" 
              @click="handleBatchExport"
              :disabled="!hasSelected"
            >
              <template #icon><DownloadOutlined /></template>
              批量导出
            </a-button>
            <a-button 
              style="margin-left: 8px" 
              danger
              @click="handleBatchDelete"
              :disabled="!hasSelected"
            >
              <template #icon><DeleteOutlined /></template>
              批量删除
            </a-button>
          </a-col>
          <a-col>
            <a-radio-group v-model:value="viewMode" button-style="solid">
              <a-radio-button value="table">
                <template #icon><TableOutlined /></template>
                表格视图
              </a-radio-button>
              <a-radio-button value="card">
                <template #icon><AppstoreOutlined /></template>
                卡片视图
              </a-radio-button>
              <a-radio-button value="gallery">
                <template #icon><PictureOutlined /></template>
                图片视图
              </a-radio-button>
            </a-radio-group>
          </a-col>
        </a-row>
      </div>

      <!-- 表格视图 -->
      <div v-show="viewMode === 'table'" class="table-view">
        <a-table
          :columns="columns"
          :data-source="dataSource"
          :pagination="pagination"
          :loading="loading"
          :row-selection="rowSelection"
          :scroll="{ x: 1500 }"
          @change="handleTableChange"
          row-key="id"
        >
          <!-- 产品图片 -->
          <template #mainImage="{ record }">
            <div class="product-image">
              <a-image
                v-if="record.mainImage"
                :src="record.mainImage"
                :width="60"
                :height="60"
                :preview="true"
                style="object-fit: cover; border-radius: 4px;"
              />
              <div v-else class="no-image">
                <PictureOutlined style="font-size: 24px; color: #d9d9d9;" />
              </div>
            </div>
          </template>

          <!-- 产品名称 (支持双击编辑) -->
          <template #itemName="{ record }">
            <div class="editable-cell">
              <div
                v-if="editingKey !== record.id || editingField !== 'itemName'"
                @dblclick="startEdit(record.id, 'itemName', record.itemName)"
                class="editable-content"
              >
                {{ record.itemName }}
              </div>
              <a-input
                v-else
                v-model:value="editingValue"
                @press-enter="saveEdit(record.id)"
                @blur="saveEdit(record.id)"
                @keyup.esc="cancelEdit"
                ref="editInput"
                size="small"
              />
            </div>
          </template>

          <!-- 规格尺寸 (支持双击编辑) -->
          <template #specifications="{ record }">
            <div class="editable-cell">
              <div
                v-if="editingKey !== record.id || editingField !== 'specifications'"
                @dblclick="startEdit(record.id, 'specifications', record.specifications)"
                class="editable-content"
              >
                {{ record.specifications || '-' }}
              </div>
              <a-input
                v-else
                v-model:value="editingValue"
                @press-enter="saveEdit(record.id)"
                @blur="saveEdit(record.id)"
                @keyup.esc="cancelEdit"
                ref="editInput"
                size="small"
              />
            </div>
          </template>

          <!-- 零售价 (支持双击编辑) -->
          <template #retailPrice="{ record }">
            <div class="editable-cell">
              <div
                v-if="editingKey !== record.id || editingField !== 'retailPrice'"
                @dblclick="startEdit(record.id, 'retailPrice', record.retailPrice)"
                class="editable-content price-cell"
              >
                ¥{{ record.retailPrice || '0.00' }}
              </div>
              <a-input-number
                v-else
                v-model:value="editingValue"
                @press-enter="saveEdit(record.id)"
                @blur="saveEdit(record.id)"
                @keyup.esc="cancelEdit"
                ref="editInput"
                size="small"
                :precision="2"
                :min="0"
                style="width: 100%"
              />
            </div>
          </template>

          <!-- 渠道价 (支持双击编辑) -->
          <template #channelPrice="{ record }">
            <div class="editable-cell">
              <div
                v-if="editingKey !== record.id || editingField !== 'channelPrice'"
                @dblclick="startEdit(record.id, 'channelPrice', record.channelPrice)"
                class="editable-content price-cell"
              >
                ¥{{ record.channelPrice || '0.00' }}
              </div>
              <a-input-number
                v-else
                v-model:value="editingValue"
                @press-enter="saveEdit(record.id)"
                @blur="saveEdit(record.id)"
                @keyup.esc="cancelEdit"
                ref="editInput"
                size="small"
                :precision="2"
                :min="0"
                style="width: 100%"
              />
            </div>
          </template>

          <!-- 库存信息 -->
          <template #stockInfo="{ record }">
            <div class="stock-info">
              <div class="stock-main">
                <span class="stock-number">{{ record.currentStock || '0' }}</span>
                <span class="stock-unit">{{ record.unitName || '件' }}</span>
              </div>
              <div class="stock-detail">
                <a-tooltip title="可用/预留">
                  <small>{{ record.availableStock || '0' }}/{{ record.reservedStock || '0' }}</small>
                </a-tooltip>
              </div>
              <div class="stock-warning" v-if="isLowStock(record)">
                <a-tag color="red" size="small">库存不足</a-tag>
              </div>
            </div>
          </template>

          <!-- 成本价 -->
          <template #costPrice="{ record }">
            <div class="cost-price">
              <span>¥{{ record.costPrice || '0.00' }}</span>
              <a-button 
                type="link" 
                size="small"
                @click="recalculateCost(record.id)"
                title="重新核算成本"
              >
                <template #icon><CalculatorOutlined /></template>
              </a-button>
            </div>
          </template>

          <!-- 产品类型 -->
          <template #itemType="{ record }">
            <a-tag :color="record.itemType === '1' ? 'blue' : 'green'">
              {{ record.itemType === '1' ? '产品' : '服务' }}
            </a-tag>
          </template>

          <!-- 状态 -->
          <template #status="{ record }">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <!-- 操作列 -->
          <template #action="{ record }">
            <a-space>
              <a-button type="link" size="small" @click="handleView(record)">
                <template #icon><EyeOutlined /></template>
                查看
              </a-button>
              <a-button type="link" size="small" @click="handleEdit(record)">
                <template #icon><EditOutlined /></template>
                编辑
              </a-button>
              <a-button type="link" size="small" @click="handleStock(record)">
                <template #icon><InboxOutlined /></template>
                库存
              </a-button>
              <a-dropdown>
                <a-button type="link" size="small">
                  更多
                  <template #icon><DownOutlined /></template>
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="handleCopy(record)">
                      <template #icon><CopyOutlined /></template>
                      复制
                    </a-menu-item>
                    <a-menu-item @click="handlePriceHistory(record)">
                      <template #icon><HistoryOutlined /></template>
                      价格历史
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item @click="handleDelete(record)" danger>
                      <template #icon><DeleteOutlined /></template>
                      删除
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </a-table>
      </div>

      <!-- 卡片视图 -->
      <div v-show="viewMode === 'card'" class="card-view">
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4" v-for="item in dataSource" :key="item.id">
            <a-card
              hoverable
              class="product-card"
              :body-style="{ padding: '12px' }"
            >
              <template #cover>
                <div class="card-cover">
                  <a-image
                    v-if="item.mainImage"
                    :src="item.mainImage"
                    :height="160"
                    :preview="true"
                    style="object-fit: cover;"
                  />
                  <div v-else class="no-image-card">
                    <PictureOutlined style="font-size: 48px; color: #d9d9d9;" />
                  </div>
                </div>
              </template>
              
              <a-card-meta>
                <template #title>
                  <div class="card-title">{{ item.itemName }}</div>
                </template>
                <template #description>
                  <div class="card-info">
                    <div class="card-row">
                      <span class="label">编码:</span>
                      <span class="value">{{ item.itemCode }}</span>
                    </div>
                    <div class="card-row">
                      <span class="label">分类:</span>
                      <span class="value">{{ item.categoryName || '-' }}</span>
                    </div>
                    <div class="card-row">
                      <span class="label">规格:</span>
                      <span class="value">{{ item.specifications || '-' }}</span>
                    </div>
                    <div class="card-row price-row">
                      <span class="label">零售价:</span>
                      <span class="value price">¥{{ item.retailPrice || '0.00' }}</span>
                    </div>
                    <div class="card-row">
                      <span class="label">库存:</span>
                      <span class="value stock">{{ item.currentStock || '0' }}{{ item.unitName || '件' }}</span>
                    </div>
                  </div>
                </template>
              </a-card-meta>

              <template #actions>
                <EyeOutlined @click="handleView(item)" title="查看" />
                <EditOutlined @click="handleEdit(item)" title="编辑" />
                <InboxOutlined @click="handleStock(item)" title="库存" />
                <DeleteOutlined @click="handleDelete(item)" title="删除" />
              </template>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 图片视图 -->
      <div v-show="viewMode === 'gallery'" class="gallery-view">
        <a-row :gutter="[8, 8]">
          <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3" v-for="item in dataSource" :key="item.id">
            <div class="gallery-item" @click="handleView(item)">
              <div class="gallery-image">
                <a-image
                  v-if="item.mainImage"
                  :src="item.mainImage"
                  :height="120"
                  :preview="false"
                  style="object-fit: cover; width: 100%;"
                />
                <div v-else class="no-image-gallery">
                  <PictureOutlined style="font-size: 32px; color: #d9d9d9;" />
                </div>
              </div>
              <div class="gallery-info">
                <div class="gallery-title">{{ item.itemName }}</div>
                <div class="gallery-price">¥{{ item.retailPrice || '0.00' }}</div>
              </div>
            </div>
          </a-col>
        </a-row>
      </div>
    </a-card>

    <!-- 产品详情弹窗 -->
    <product-service-modal
      ref="productModal"
      @ok="handleModalSuccess"
    />

    <!-- 库存管理弹窗 -->
    <product-stock-modal
      ref="stockModal"
      @ok="handleStockSuccess"
    />

    <!-- 价格历史弹窗 -->
    <price-history-modal
      ref="priceHistoryModal"
    />
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, onMounted, nextTick } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  UploadOutlined,
  DownloadOutlined,
  DeleteOutlined,
  TableOutlined,
  AppstoreOutlined,
  PictureOutlined,
  EyeOutlined,
  EditOutlined,
  InboxOutlined,
  CalculatorOutlined,
  CopyOutlined,
  HistoryOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import { getProductServiceList, deleteProductService, quickEditProduct, recalculateProductCost } from '@/api/product'
import ProductServiceModal from './modules/ProductServiceModal'
import ProductStockModal from './modules/ProductStockModal'
import PriceHistoryModal from './modules/PriceHistoryModal'

export default defineComponent({
  name: 'ProductServiceList',
  components: {
    SearchOutlined,
    ReloadOutlined,
    PlusOutlined,
    UploadOutlined,
    DownloadOutlined,
    DeleteOutlined,
    TableOutlined,
    AppstoreOutlined,
    PictureOutlined,
    EyeOutlined,
    EditOutlined,
    InboxOutlined,
    CalculatorOutlined,
    CopyOutlined,
    HistoryOutlined,
    DownOutlined,
    ProductServiceModal,
    ProductStockModal,
    PriceHistoryModal
  },
  setup() {
    // 响应式数据
    const loading = ref(false)
    const dataSource = ref([])
    const selectedRowKeys = ref([])
    const categoryTree = ref([])
    const viewMode = ref('table')
    
    // 编辑相关
    const editingKey = ref('')
    const editingField = ref('')
    const editingValue = ref('')
    const editInput = ref(null)

    // 搜索表单
    const searchForm = reactive({
      itemCode: '',
      itemName: '',
      categoryId: undefined,
      itemType: undefined,
      status: undefined
    })

    // 分页配置
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条数据`
    })

    // 计算属性
    const hasSelected = computed(() => selectedRowKeys.value.length > 0)

    // 表格列配置
    const columns = [
      {
        title: '产品图片',
        dataIndex: 'mainImage',
        key: 'mainImage',
        width: 80,
        slots: { customRender: 'mainImage' }
      },
      {
        title: '产品编码',
        dataIndex: 'itemCode',
        key: 'itemCode',
        width: 120,
        sorter: true
      },
      {
        title: '产品名称',
        dataIndex: 'itemName',
        key: 'itemName',
        width: 200,
        sorter: true,
        slots: { customRender: 'itemName' }
      },
      {
        title: '产品类型',
        dataIndex: 'itemType',
        key: 'itemType',
        width: 80,
        slots: { customRender: 'itemType' }
      },
      {
        title: '产品分类',
        dataIndex: 'categoryName',
        key: 'categoryName',
        width: 120
      },
      {
        title: '规格尺寸',
        dataIndex: 'specifications',
        key: 'specifications',
        width: 150,
        slots: { customRender: 'specifications' }
      },
      {
        title: '单位',
        dataIndex: 'unitName',
        key: 'unitName',
        width: 60
      },
      {
        title: '成本价',
        dataIndex: 'costPrice',
        key: 'costPrice',
        width: 100,
        sorter: true,
        slots: { customRender: 'costPrice' }
      },
      {
        title: '零售价',
        dataIndex: 'retailPrice',
        key: 'retailPrice',
        width: 100,
        sorter: true,
        slots: { customRender: 'retailPrice' }
      },
      {
        title: '渠道价',
        dataIndex: 'channelPrice',
        key: 'channelPrice',
        width: 100,
        slots: { customRender: 'channelPrice' }
      },
      {
        title: '库存信息',
        dataIndex: 'stockInfo',
        key: 'stockInfo',
        width: 120,
        slots: { customRender: 'stockInfo' }
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 80,
        slots: { customRender: 'status' }
      },
      {
        title: '操作',
        key: 'action',
        width: 200,
        fixed: 'right',
        slots: { customRender: 'action' }
      }
    ]

    // 行选择配置
    const rowSelection = {
      selectedRowKeys: selectedRowKeys,
      onChange: (keys) => {
        selectedRowKeys.value = keys
      }
    }

    // 方法定义
    const loadData = async () => {
      try {
        loading.value = true
        const params = {
          ...searchForm,
          pageNumber: pagination.current,
          pageSize: pagination.pageSize
        }
        
        const response = await getProductServiceList(params)
        dataSource.value = response.data.rows || []
        pagination.total = response.data.total || 0
      } catch (error) {
        message.error('加载数据失败')
      } finally {
        loading.value = false
      }
    }

    const handleSearch = () => {
      pagination.current = 1
      loadData()
    }

    const handleReset = () => {
      Object.keys(searchForm).forEach(key => {
        searchForm[key] = undefined
      })
      pagination.current = 1
      loadData()
    }

    const handleTableChange = (pag, filters, sorter) => {
      pagination.current = pag.current
      pagination.pageSize = pag.pageSize
      loadData()
    }

    const handleAdd = () => {
      // 打开新增弹窗
    }

    const handleView = (record) => {
      // 查看详情
    }

    const handleEdit = (record) => {
      // 编辑产品
    }

    const handleStock = (record) => {
      // 库存管理
    }

    const handleDelete = (record) => {
      Modal.confirm({
        title: '确认删除',
        content: `确定要删除产品 "${record.itemName}" 吗？`,
        onOk: async () => {
          try {
            await deleteProductService(record.id)
            message.success('删除成功')
            loadData()
          } catch (error) {
            message.error('删除失败')
          }
        }
      })
    }

    // 双击编辑相关方法
    const startEdit = (key, field, value) => {
      editingKey.value = key
      editingField.value = field
      editingValue.value = value
      
      nextTick(() => {
        if (editInput.value) {
          editInput.value.focus()
        }
      })
    }

    const saveEdit = async (key) => {
      try {
        await quickEditProduct(key, editingField.value, editingValue.value)
        message.success('修改成功')
        loadData()
      } catch (error) {
        message.error('修改失败')
      } finally {
        cancelEdit()
      }
    }

    const cancelEdit = () => {
      editingKey.value = ''
      editingField.value = ''
      editingValue.value = ''
    }

    const recalculateCost = async (productId) => {
      try {
        loading.value = true
        await recalculateProductCost(productId)
        message.success('成本重新核算完成')
        loadData()
      } catch (error) {
        message.error('成本核算失败')
      } finally {
        loading.value = false
      }
    }

    const isLowStock = (record) => {
      return record.currentStock < record.safetyStock
    }

    const getStatusColor = (status) => {
      const colors = {
        '0': 'red',
        '1': 'green',
        '2': 'orange'
      }
      return colors[status] || 'default'
    }

    const getStatusText = (status) => {
      const texts = {
        '0': '停用',
        '1': '启用',
        '2': '停产'
      }
      return texts[status] || '未知'
    }

    // 生命周期
    onMounted(() => {
      loadData()
    })

    return {
      // 数据
      loading,
      dataSource,
      selectedRowKeys,
      categoryTree,
      viewMode,
      searchForm,
      pagination,
      columns,
      rowSelection,
      editingKey,
      editingField,
      editingValue,
      editInput,
      
      // 计算属性
      hasSelected,
      
      // 方法
      loadData,
      handleSearch,
      handleReset,
      handleTableChange,
      handleAdd,
      handleView,
      handleEdit,
      handleStock,
      handleDelete,
      startEdit,
      saveEdit,
      cancelEdit,
      recalculateCost,
      isLowStock,
      getStatusColor,
      getStatusText
    }
  }
})
</script>

<style lang="less" scoped>
.product-service-list {
  .search-area {
    margin-bottom: 16px;
    padding: 16px;
    background: #fafafa;
    border-radius: 4px;
  }

  .action-area {
    margin-bottom: 16px;
  }

  .editable-cell {
    .editable-content {
      padding: 4px 8px;
      cursor: pointer;
      border-radius: 4px;
      transition: all 0.3s;

      &:hover {
        background: #f0f0f0;
      }
    }

    .price-cell {
      font-weight: 500;
      color: #1890ff;
    }
  }

  .product-image {
    display: flex;
    align-items: center;
    justify-content: center;

    .no-image {
      width: 60px;
      height: 60px;
      border: 1px dashed #d9d9d9;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .stock-info {
    .stock-main {
      font-weight: 500;
      
      .stock-number {
        color: #1890ff;
      }
      
      .stock-unit {
        margin-left: 4px;
        color: #666;
        font-size: 12px;
      }
    }

    .stock-detail {
      font-size: 12px;
      color: #999;
    }

    .stock-warning {
      margin-top: 2px;
    }
  }

  .cost-price {
    display: flex;
    align-items: center;
  }

  // 卡片视图样式
  .card-view {
    .product-card {
      .card-cover {
        position: relative;
        overflow: hidden;
      }

      .no-image-card {
        height: 160px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f5f5f5;
      }

      .card-title {
        font-weight: 500;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .card-info {
        .card-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 4px;
          font-size: 12px;

          .label {
            color: #666;
          }

          .value {
            color: #333;
            text-align: right;
          }

          &.price-row .value {
            color: #1890ff;
            font-weight: 500;
          }
        }
      }
    }
  }

  // 图片视图样式
  .gallery-view {
    .gallery-item {
      cursor: pointer;
      border-radius: 4px;
      overflow: hidden;
      transition: all 0.3s;

      &:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }

      .gallery-image {
        position: relative;
        overflow: hidden;

        .no-image-gallery {
          height: 120px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #f5f5f5;
        }
      }

      .gallery-info {
        padding: 8px;
        background: white;

        .gallery-title {
          font-size: 14px;
          font-weight: 500;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-bottom: 4px;
        }

        .gallery-price {
          color: #1890ff;
          font-weight: 500;
        }
      }
    }
  }
}
</style>
```

---

## 验收标准

### 1. 数据库设计验收
- [ ] **表结构完整性**
  - [ ] 产品服务主表字段完整
  - [ ] 多仓库库存表设计合理
  - [ ] 价格历史表支持审计
  - [ ] 索引设置符合性能要求

### 2. 功能完整性验收
- [ ] **产品管理功能**
  - [ ] 产品信息CRUD操作正常
  - [ ] 支持产品和服务两种类型
  - [ ] 多维度价格体系完整
  - [ ] 图片上传和管理功能正常

- [ ] **库存管理功能**
  - [ ] 多仓库架构支持完整
  - [ ] 实时库存计算准确
  - [ ] 库存预警机制有效
  - [ ] 安全库存控制正常

- [ ] **成本核算功能**
  - [ ] 自动成本核算逻辑正确
  - [ ] 成本构成分析详细
  - [ ] 成本历史记录完整
  - [ ] 成本更新触发及时

### 3. 用户体验验收
- [ ] **界面交互**
  - [ ] 多视图切换流畅
  - [ ] 双击编辑功能便捷
  - [ ] 搜索筛选响应快速
  - [ ] 批量操作支持完整

- [ ] **移动端适配**
  - [ ] 响应式设计良好
  - [ ] 触摸操作友好
  - [ ] 移动端性能优良

### 4. 性能验收标准
- [ ] **查询性能**
  - [ ] 列表查询响应时间 < 500ms
  - [ ] 复杂条件搜索 < 1s
  - [ ] 图片加载优化良好
  - [ ] 大数据量分页流畅

---

## 交付物清单

1. **数据库脚本**
   - 产品服务主表创建脚本
   - 产品分类表创建脚本
   - 多仓库库存表创建脚本
   - 价格历史表创建脚本
   - 初始化数据脚本

2. **后端代码**
   - ProductServiceService.java (核心服务)
   - ProductService.java (实体类)
   - ProductServiceMapper.java (数据访问)
   - ProductServiceController.java (接口控制器)

3. **前端代码**
   - ProductServiceList.vue (列表页面)
   - ProductServiceModal.vue (编辑弹窗)
   - ProductStockModal.vue (库存管理)
   - PriceHistoryModal.vue (价格历史)

4. **配置文件**
   - MyBatis映射文件
   - 权限配置
   - 菜单配置

5. **测试文件**
   - 单元测试用例
   - 集成测试用例
   - 性能测试脚本

6. **文档资料**
   - API接口文档
   - 用户操作手册
   - 数据字典

---

**文档结束**

> 本文档为产品与服务管理模块的完整开发指南，实现了聆花文化ERP系统的核心基础功能。模块支持产品与服务的统一管理、多维度价格体系、实时库存管理、多仓库架构等关键特性，为整个ERP系统提供了坚实的数据基础。所有代码严格遵循jshERP架构规范，确保与现有系统的完美集成。