# Week 11-12: 薪酬核算中心模块开发文档

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-06-18
- **项目阶段**: 第二阶段 - 业务支撑模块
- **估算工期**: 10天
- **技术参考**: 严格遵循《jshERP_二次开发技术参考手册》第3、4、5章规范

---

## 概述

本文档为Week 11-12的薪酬核算中心模块开发提供详细的实施指导。主要实现自动化薪酬核算、多维度薪酬计算、薪资条生成等核心功能，建立完整的薪酬管理体系，为聆花文化的员工薪酬管理提供专业的自动化工具。

---

## 数据库设计 (1天)

### 1. 薪酬核算主表设计

**表名**: `jsh_salary_calculation`

```sql
-- 薪酬核算主表
CREATE TABLE `jsh_salary_calculation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `calculation_no` varchar(32) NOT NULL COMMENT '核算编号',
  `salary_period` varchar(20) NOT NULL COMMENT '薪酬周期(YYYY-MM)',
  `employee_id` bigint(20) NOT NULL COMMENT '员工ID',
  `employee_name` varchar(100) NOT NULL COMMENT '员工姓名',
  `employee_no` varchar(50) NOT NULL COMMENT '员工工号',
  `department_id` bigint(20) DEFAULT NULL COMMENT '部门ID',
  `department_name` varchar(100) DEFAULT NULL COMMENT '部门名称',
  `position` varchar(100) DEFAULT NULL COMMENT '职位',
  `salary_type` varchar(50) NOT NULL COMMENT '薪酬类型(月薪/计件/混合)',
  
  -- 基础薪酬
  `base_salary` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '基础工资',
  `position_allowance` decimal(8,2) DEFAULT '0.00' COMMENT '岗位津贴',
  `performance_bonus` decimal(8,2) DEFAULT '0.00' COMMENT '绩效奖金',
  `overtime_pay` decimal(8,2) DEFAULT '0.00' COMMENT '加班费',
  
  -- 生产工费
  `production_salary` decimal(10,2) DEFAULT '0.00' COMMENT '生产工费',
  `production_quantity` decimal(10,2) DEFAULT '0.00' COMMENT '生产数量',
  `production_unit_price` decimal(8,2) DEFAULT '0.00' COMMENT '生产单价',
  `production_bonus` decimal(8,2) DEFAULT '0.00' COMMENT '生产奖金',
  
  -- 团建提成
  `teambuilding_commission` decimal(10,2) DEFAULT '0.00' COMMENT '团建提成',
  `teambuilding_activities` int(11) DEFAULT '0' COMMENT '团建活动次数',
  `teambuilding_hours` decimal(6,2) DEFAULT '0.00' COMMENT '团建工作时长',
  `teambuilding_revenue` decimal(12,2) DEFAULT '0.00' COMMENT '团建收入',
  
  -- 排班工资
  `schedule_salary` decimal(10,2) DEFAULT '0.00' COMMENT '排班工资',
  `schedule_days` int(11) DEFAULT '0' COMMENT '排班天数',
  `schedule_hours` decimal(6,2) DEFAULT '0.00' COMMENT '排班小时数',
  `schedule_overtime_hours` decimal(6,2) DEFAULT '0.00' COMMENT '排班加班小时',
  
  -- 补贴扣除
  `meal_allowance` decimal(8,2) DEFAULT '0.00' COMMENT '餐费补贴',
  `transport_allowance` decimal(8,2) DEFAULT '0.00' COMMENT '交通补贴',
  `communication_allowance` decimal(8,2) DEFAULT '0.00' COMMENT '通讯补贴',
  `housing_allowance` decimal(8,2) DEFAULT '0.00' COMMENT '住房补贴',
  `other_allowance` decimal(8,2) DEFAULT '0.00' COMMENT '其他补贴',
  
  -- 扣除项
  `social_insurance` decimal(8,2) DEFAULT '0.00' COMMENT '社保扣除',
  `housing_fund` decimal(8,2) DEFAULT '0.00' COMMENT '公积金扣除',
  `personal_tax` decimal(8,2) DEFAULT '0.00' COMMENT '个人所得税',
  `late_penalty` decimal(8,2) DEFAULT '0.00' COMMENT '迟到罚款',
  `absence_deduction` decimal(8,2) DEFAULT '0.00' COMMENT '缺勤扣除',
  `other_deduction` decimal(8,2) DEFAULT '0.00' COMMENT '其他扣除',
  
  -- 总计
  `gross_salary` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '应发工资',
  `total_allowance` decimal(10,2) DEFAULT '0.00' COMMENT '补贴合计',
  `total_deduction` decimal(10,2) DEFAULT '0.00' COMMENT '扣除合计',
  `net_salary` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实发工资',
  
  -- 状态信息
  `calculation_status` char(1) NOT NULL DEFAULT '0' COMMENT '核算状态(0-计算中,1-已完成,2-已审核,3-已发放)',
  `calculation_date` datetime NOT NULL COMMENT '核算日期',
  `approver_id` bigint(20) DEFAULT NULL COMMENT '审核人ID',
  `approval_date` datetime DEFAULT NULL COMMENT '审核日期',
  `payment_date` datetime DEFAULT NULL COMMENT '发放日期',
  `payment_method` varchar(50) DEFAULT NULL COMMENT '发放方式',
  `bank_account` varchar(100) DEFAULT NULL COMMENT '银行账号',
  
  -- 附加信息
  `work_days` decimal(4,1) DEFAULT '0.0' COMMENT '工作天数',
  `attendance_rate` decimal(5,2) DEFAULT '0.00' COMMENT '出勤率%',
  `performance_score` decimal(5,2) DEFAULT '0.00' COMMENT '绩效评分',
  `calculation_formula` text COMMENT '计算公式',
  `remark` varchar(1000) DEFAULT NULL COMMENT '备注',
  
  -- 标准字段
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `delete_flag` char(1) NOT NULL DEFAULT '0' COMMENT '删除标记',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` bigint(20) DEFAULT NULL COMMENT '更新人',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_calculation_no` (`calculation_no`, `tenant_id`),
  UNIQUE KEY `uk_employee_period` (`employee_id`, `salary_period`, `tenant_id`),
  KEY `idx_salary_period` (`salary_period`),
  KEY `idx_employee` (`employee_id`),
  KEY `idx_department` (`department_id`),
  KEY `idx_status` (`calculation_status`),
  KEY `idx_tenant` (`tenant_id`, `delete_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='薪酬核算主表';
```

### 2. 薪酬明细表设计

**表名**: `jsh_salary_detail`

```sql
-- 薪酬明细表
CREATE TABLE `jsh_salary_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `calculation_id` bigint(20) NOT NULL COMMENT '薪酬核算ID',
  `detail_type` varchar(50) NOT NULL COMMENT '明细类型(生产工费/团建提成/排班工资/加班费/奖金/扣除)',
  `source_type` varchar(50) NOT NULL COMMENT '数据来源(生产工单/团建活动/排班记录/其他)',
  `source_id` bigint(20) DEFAULT NULL COMMENT '来源记录ID',
  `source_no` varchar(100) DEFAULT NULL COMMENT '来源单号',
  `detail_name` varchar(200) NOT NULL COMMENT '明细名称',
  `detail_description` varchar(500) DEFAULT NULL COMMENT '明细描述',
  `calculation_base` decimal(12,2) DEFAULT '0.00' COMMENT '计算基数',
  `calculation_rate` decimal(8,4) DEFAULT '0.0000' COMMENT '计算比例',
  `calculation_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '计算金额',
  `actual_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实际金额',
  `quantity` decimal(10,2) DEFAULT '0.00' COMMENT '数量',
  `unit_price` decimal(8,2) DEFAULT '0.00' COMMENT '单价',
  `work_date` date DEFAULT NULL COMMENT '工作日期',
  `work_hours` decimal(6,2) DEFAULT '0.00' COMMENT '工作时长',
  `quality_score` decimal(5,2) DEFAULT '0.00' COMMENT '质量评分',
  `difficulty_level` decimal(3,1) DEFAULT '1.0' COMMENT '难度系数',
  `urgency_level` decimal(3,1) DEFAULT '1.0' COMMENT '紧急系数',
  `team_cooperation` decimal(3,1) DEFAULT '1.0' COMMENT '团队协作系数',
  `adjustment_reason` varchar(500) DEFAULT NULL COMMENT '调整原因',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  
  -- 标准字段
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `delete_flag` char(1) NOT NULL DEFAULT '0' COMMENT '删除标记',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` bigint(20) DEFAULT NULL COMMENT '更新人',
  
  PRIMARY KEY (`id`),
  KEY `idx_calculation` (`calculation_id`),
  KEY `idx_detail_type` (`detail_type`),
  KEY `idx_source` (`source_type`, `source_id`),
  KEY `idx_work_date` (`work_date`),
  KEY `idx_tenant` (`tenant_id`, `delete_flag`),
  CONSTRAINT `fk_detail_calculation` FOREIGN KEY (`calculation_id`) REFERENCES `jsh_salary_calculation` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='薪酬明细表';
```

### 3. 薪酬配置表设计

**表名**: `jsh_salary_config`

```sql
-- 薪酬配置表
CREATE TABLE `jsh_salary_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_type` varchar(50) NOT NULL COMMENT '配置类型(基础工资/计件单价/提成比例/扣除标准)',
  `config_name` varchar(200) NOT NULL COMMENT '配置名称',
  `config_code` varchar(100) NOT NULL COMMENT '配置编码',
  `config_category` varchar(50) DEFAULT NULL COMMENT '配置分类',
  `applicable_scope` varchar(100) DEFAULT NULL COMMENT '适用范围(全员/部门/岗位/个人)',
  `scope_value` varchar(200) DEFAULT NULL COMMENT '范围值',
  `config_value` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '配置值',
  `config_unit` varchar(20) DEFAULT NULL COMMENT '配置单位',
  `min_value` decimal(12,4) DEFAULT NULL COMMENT '最小值',
  `max_value` decimal(12,4) DEFAULT NULL COMMENT '最大值',
  `calculation_formula` varchar(1000) DEFAULT NULL COMMENT '计算公式',
  `effective_date` date NOT NULL COMMENT '生效日期',
  `expiry_date` date DEFAULT NULL COMMENT '失效日期',
  `priority` int(11) DEFAULT '0' COMMENT '优先级',
  `is_active` char(1) NOT NULL DEFAULT '1' COMMENT '是否启用(0-否,1-是)',
  `remark` varchar(1000) DEFAULT NULL COMMENT '备注',
  
  -- 标准字段
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `delete_flag` char(1) NOT NULL DEFAULT '0' COMMENT '删除标记',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` bigint(20) DEFAULT NULL COMMENT '更新人',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_code` (`config_code`, `tenant_id`),
  KEY `idx_config_type` (`config_type`),
  KEY `idx_config_category` (`config_category`),
  KEY `idx_applicable_scope` (`applicable_scope`),
  KEY `idx_effective_date` (`effective_date`),
  KEY `idx_tenant` (`tenant_id`, `delete_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='薪酬配置表';
```

### 4. 员工信息扩展表设计

**表名**: `jsh_user_employee`

```sql
-- 员工信息扩展表
CREATE TABLE `jsh_user_employee` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `employee_no` varchar(50) NOT NULL COMMENT '员工工号',
  `employee_name` varchar(100) NOT NULL COMMENT '员工姓名',
  `id_card` varchar(18) DEFAULT NULL COMMENT '身份证号',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号码',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `department_id` bigint(20) DEFAULT NULL COMMENT '部门ID',
  `position` varchar(100) DEFAULT NULL COMMENT '职位',
  `employment_type` varchar(50) DEFAULT NULL COMMENT '用工类型(全职/兼职/实习/外包)',
  `skill_level` varchar(50) DEFAULT NULL COMMENT '技能等级(初级/中级/高级/专家)',
  `hire_date` date DEFAULT NULL COMMENT '入职日期',
  `probation_end_date` date DEFAULT NULL COMMENT '试用期结束日期',
  `contract_start_date` date DEFAULT NULL COMMENT '合同开始日期',
  `contract_end_date` date DEFAULT NULL COMMENT '合同结束日期',
  
  -- 薪酬相关
  `salary_type` varchar(50) DEFAULT NULL COMMENT '薪酬类型(月薪/计件/混合)',
  `base_salary` decimal(10,2) DEFAULT '0.00' COMMENT '基础工资',
  `hourly_rate` decimal(8,2) DEFAULT '0.00' COMMENT '时薪标准',
  `piece_rate` decimal(8,4) DEFAULT '0.0000' COMMENT '计件单价',
  `commission_rate` decimal(5,2) DEFAULT '0.00' COMMENT '提成比例%',
  `allowance_standard` decimal(8,2) DEFAULT '0.00' COMMENT '津贴标准',
  
  -- 银行信息
  `bank_name` varchar(100) DEFAULT NULL COMMENT '开户银行',
  `bank_account` varchar(50) DEFAULT NULL COMMENT '银行账号',
  `account_holder` varchar(100) DEFAULT NULL COMMENT '账户户名',
  
  -- 紧急联系人
  `emergency_contact` varchar(100) DEFAULT NULL COMMENT '紧急联系人',
  `emergency_phone` varchar(20) DEFAULT NULL COMMENT '紧急联系电话',
  `emergency_relation` varchar(50) DEFAULT NULL COMMENT '紧急联系人关系',
  
  -- 地址信息
  `home_address` varchar(500) DEFAULT NULL COMMENT '家庭地址',
  `current_address` varchar(500) DEFAULT NULL COMMENT '现住址',
  
  -- 状态信息
  `employee_status` char(1) NOT NULL DEFAULT '1' COMMENT '员工状态(0-离职,1-在职,2-停薪留职,3-试用)',
  `leave_date` date DEFAULT NULL COMMENT '离职日期',
  `leave_reason` varchar(500) DEFAULT NULL COMMENT '离职原因',
  `remark` varchar(1000) DEFAULT NULL COMMENT '备注',
  
  -- 标准字段
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `delete_flag` char(1) NOT NULL DEFAULT '0' COMMENT '删除标记',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` bigint(20) DEFAULT NULL COMMENT '更新人',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`, `tenant_id`),
  UNIQUE KEY `uk_employee_no` (`employee_no`, `tenant_id`),
  KEY `idx_department` (`department_id`),
  KEY `idx_employment_type` (`employment_type`),
  KEY `idx_skill_level` (`skill_level`),
  KEY `idx_employee_status` (`employee_status`),
  KEY `idx_tenant` (`tenant_id`, `delete_flag`),
  CONSTRAINT `fk_employee_user` FOREIGN KEY (`user_id`) REFERENCES `jsh_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='员工信息扩展表';
```

---

## 核算引擎开发 (4天)

### 1. SalaryCalculationEngine.java - 薪酬核算引擎

**文件路径**: `com.jsh.erp.salary.service.SalaryCalculationEngine`

```java
package com.jsh.erp.salary.service;

import com.alibaba.fastjson.JSONObject;
import com.jsh.erp.exception.BusinessRunTimeException;
import com.jsh.erp.salary.datasource.entities.SalaryCalculation;
import com.jsh.erp.salary.datasource.entities.SalaryDetail;
import com.jsh.erp.salary.datasource.entities.SalaryConfig;
import com.jsh.erp.salary.datasource.entities.UserEmployee;
import com.jsh.erp.salary.datasource.mappers.SalaryCalculationMapper;
import com.jsh.erp.salary.datasource.mappers.SalaryDetailMapper;
import com.jsh.erp.salary.datasource.mappers.SalaryConfigMapper;
import com.jsh.erp.salary.datasource.mappers.UserEmployeeMapper;
import com.jsh.erp.production.service.ProductionOrderService;
import com.jsh.erp.teambuilding.service.TeambuildingActivityService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 薪酬核算引擎
 * 提供自动化的薪酬计算服务
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Service
public class SalaryCalculationEngine {
    private Logger logger = LoggerFactory.getLogger(SalaryCalculationEngine.class);

    @Resource
    private SalaryCalculationMapper salaryCalculationMapper;
    @Resource
    private SalaryDetailMapper salaryDetailMapper;
    @Resource
    private SalaryConfigMapper salaryConfigMapper;
    @Resource
    private UserEmployeeMapper userEmployeeMapper;
    @Resource
    private SalaryDataCollectionService dataCollectionService;
    @Resource
    private ProductionOrderService productionOrderService;
    @Resource
    private TeambuildingActivityService teambuildingActivityService;

    /**
     * 计算月度薪酬
     * 为指定员工计算指定月份的薪酬
     */
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public String calculateMonthlySalary(Long employeeId, String salaryPeriod, HttpServletRequest request) throws Exception {
        try {
            // 1. 验证参数
            validateCalculationParams(employeeId, salaryPeriod);
            
            // 2. 检查是否已计算
            SalaryCalculation existing = salaryCalculationMapper.findByEmployeeAndPeriod(employeeId, salaryPeriod);
            if (existing != null && !"0".equals(existing.getCalculationStatus())) {
                throw new BusinessRunTimeException("该员工" + salaryPeriod + "薪酬已计算完成");
            }
            
            // 3. 获取员工信息
            UserEmployee employee = userEmployeeMapper.selectByUserId(employeeId);
            if (employee == null) {
                throw new BusinessRunTimeException("员工信息不存在: " + employeeId);
            }
            
            // 4. 创建或更新薪酬核算记录
            SalaryCalculation calculation = existing != null ? existing : new SalaryCalculation();
            if (existing == null) {
                calculation.setCalculationNo(generateCalculationNo(salaryPeriod));
                calculation.setSalaryPeriod(salaryPeriod);
                calculation.setEmployeeId(employeeId);
                calculation.setEmployeeName(employee.getEmployeeName());
                calculation.setEmployeeNo(employee.getEmployeeNo());
                calculation.setDepartmentId(employee.getDepartmentId());
                calculation.setPosition(employee.getPosition());
                calculation.setSalaryType(employee.getSalaryType());
                calculation.setCalculationStatus("0"); // 计算中
                
                // 设置标准字段
                calculation.setTenantId(getCurrentTenantId());
                calculation.setDeleteFlag("0");
                calculation.setCreateTime(new Date());
                calculation.setCreateUser(getCurrentUserId(request));
            }
            
            // 5. 收集薪酬数据
            JSONObject salaryData = dataCollectionService.collectAllSalaryData(employeeId, salaryPeriod);
            
            // 6. 计算各项薪酬
            calculateBaseSalary(calculation, employee, salaryData);
            calculateProductionSalary(calculation, salaryData);
            calculateTeambuildingCommission(calculation, salaryData);
            calculateScheduleSalary(calculation, salaryData);
            calculateAllowances(calculation, employee, salaryData);
            calculateDeductions(calculation, employee, salaryData);
            
            // 7. 计算总计
            calculateTotals(calculation);
            
            // 8. 保存计算结果
            calculation.setCalculationDate(new Date());
            calculation.setCalculationStatus("1"); // 已完成
            calculation.setUpdateTime(new Date());
            calculation.setUpdateUser(getCurrentUserId(request));
            
            if (existing == null) {
                salaryCalculationMapper.insertSelective(calculation);
            } else {
                salaryCalculationMapper.updateByPrimaryKeySelective(calculation);
            }
            
            // 9. 保存明细记录
            saveSalaryDetails(calculation.getId(), salaryData, request);
            
            logger.info("月度薪酬计算完成: employeeId={}, period={}, 应发={}, 实发={}", 
                employeeId, salaryPeriod, calculation.getGrossSalary(), calculation.getNetSalary());
            
            return String.valueOf(calculation.getId());
            
        } catch (Exception e) {
            logger.error("月度薪酬计算失败", e);
            throw new BusinessRunTimeException("月度薪酬计算失败: " + e.getMessage());
        }
    }

    /**
     * 计算生产工费
     * 基于生产工单数据计算生产相关薪酬
     */
    private void calculateProductionSalary(SalaryCalculation calculation, JSONObject salaryData) throws Exception {
        try {
            JSONObject productionData = salaryData.getJSONObject("productionData");
            if (productionData == null) {
                return;
            }
            
            BigDecimal totalProductionSalary = BigDecimal.ZERO;
            BigDecimal totalQuantity = productionData.getBigDecimal("totalQuantity");
            BigDecimal averageUnitPrice = productionData.getBigDecimal("averageUnitPrice");
            
            // 1. 基础计件工资
            if (totalQuantity != null && averageUnitPrice != null) {
                BigDecimal basePieceSalary = totalQuantity.multiply(averageUnitPrice);
                totalProductionSalary = totalProductionSalary.add(basePieceSalary);
            }
            
            // 2. 质量奖金
            BigDecimal qualityScore = productionData.getBigDecimal("averageQualityScore");
            if (qualityScore != null && qualityScore.compareTo(new BigDecimal("90")) >= 0) {
                BigDecimal qualityBonus = totalProductionSalary.multiply(new BigDecimal("0.1"));
                totalProductionSalary = totalProductionSalary.add(qualityBonus);
                calculation.setProductionBonus(qualityBonus);
            }
            
            // 3. 效率奖金
            BigDecimal efficiencyRatio = productionData.getBigDecimal("efficiencyRatio");
            if (efficiencyRatio != null && efficiencyRatio.compareTo(new BigDecimal("1.2")) >= 0) {
                BigDecimal efficiencyBonus = totalProductionSalary.multiply(new BigDecimal("0.05"));
                totalProductionSalary = totalProductionSalary.add(efficiencyBonus);
            }
            
            calculation.setProductionSalary(totalProductionSalary);
            calculation.setProductionQuantity(totalQuantity);
            calculation.setProductionUnitPrice(averageUnitPrice);
            
            logger.debug("生产工费计算完成: 总数量={}, 平均单价={}, 总工费={}", 
                totalQuantity, averageUnitPrice, totalProductionSalary);
            
        } catch (Exception e) {
            logger.error("计算生产工费失败", e);
            throw new BusinessRunTimeException("计算生产工费失败: " + e.getMessage());
        }
    }

    /**
     * 计算团建提成
     * 基于团建活动数据计算提成收入
     */
    private void calculateTeambuildingCommission(SalaryCalculation calculation, JSONObject salaryData) throws Exception {
        try {
            JSONObject teambuildingData = salaryData.getJSONObject("teambuildingData");
            if (teambuildingData == null) {
                return;
            }
            
            BigDecimal totalCommission = BigDecimal.ZERO;
            Integer activityCount = teambuildingData.getInteger("activityCount");
            BigDecimal totalRevenue = teambuildingData.getBigDecimal("totalRevenue");
            BigDecimal totalHours = teambuildingData.getBigDecimal("totalHours");
            
            // 1. 基础提成计算
            if (totalRevenue != null && totalRevenue.compareTo(BigDecimal.ZERO) > 0) {
                // 获取提成比例配置
                BigDecimal commissionRate = getConfigValue("TEAMBUILDING_COMMISSION_RATE", calculation.getEmployeeId());
                if (commissionRate == null) {
                    commissionRate = new BigDecimal("0.15"); // 默认15%
                }
                
                BigDecimal baseCommission = totalRevenue.multiply(commissionRate);
                totalCommission = totalCommission.add(baseCommission);
            }
            
            // 2. 活动次数奖金
            if (activityCount != null && activityCount >= 5) {
                BigDecimal activityBonus = new BigDecimal(activityCount).multiply(new BigDecimal("100"));
                totalCommission = totalCommission.add(activityBonus);
            }
            
            // 3. 客户满意度奖金
            BigDecimal satisfactionScore = teambuildingData.getBigDecimal("averageSatisfaction");
            if (satisfactionScore != null && satisfactionScore.compareTo(new BigDecimal("4.5")) >= 0) {
                BigDecimal satisfactionBonus = totalCommission.multiply(new BigDecimal("0.1"));
                totalCommission = totalCommission.add(satisfactionBonus);
            }
            
            calculation.setTeambuildingCommission(totalCommission);
            calculation.setTeambuildingActivities(activityCount);
            calculation.setTeambuildingHours(totalHours);
            calculation.setTeambuildingRevenue(totalRevenue);
            
            logger.debug("团建提成计算完成: 活动次数={}, 总收入={}, 总提成={}", 
                activityCount, totalRevenue, totalCommission);
            
        } catch (Exception e) {
            logger.error("计算团建提成失败", e);
            throw new BusinessRunTimeException("计算团建提成失败: " + e.getMessage());
        }
    }

    /**
     * 计算排班工资
     * 基于排班记录计算工资
     */
    private void calculateScheduleSalary(SalaryCalculation calculation, JSONObject salaryData) throws Exception {
        try {
            JSONObject scheduleData = salaryData.getJSONObject("scheduleData");
            if (scheduleData == null) {
                return;
            }
            
            BigDecimal totalScheduleSalary = BigDecimal.ZERO;
            Integer scheduleDays = scheduleData.getInteger("scheduleDays");
            BigDecimal scheduleHours = scheduleData.getBigDecimal("scheduleHours");
            BigDecimal overtimeHours = scheduleData.getBigDecimal("overtimeHours");
            
            // 1. 正常排班工资
            if (scheduleHours != null && scheduleHours.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal hourlyRate = getConfigValue("SCHEDULE_HOURLY_RATE", calculation.getEmployeeId());
                if (hourlyRate == null) {
                    hourlyRate = new BigDecimal("50"); // 默认50元/小时
                }
                
                BigDecimal normalSalary = scheduleHours.multiply(hourlyRate);
                totalScheduleSalary = totalScheduleSalary.add(normalSalary);
            }
            
            // 2. 加班工资
            if (overtimeHours != null && overtimeHours.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal overtimeRate = getConfigValue("OVERTIME_RATE", calculation.getEmployeeId());
                if (overtimeRate == null) {
                    overtimeRate = new BigDecimal("75"); // 默认75元/小时(1.5倍)
                }
                
                BigDecimal overtimeSalary = overtimeHours.multiply(overtimeRate);
                totalScheduleSalary = totalScheduleSalary.add(overtimeSalary);
                calculation.setOvertimePay(overtimeSalary);
            }
            
            // 3. 满勤奖
            if (scheduleDays != null && scheduleDays >= 22) {
                BigDecimal attendanceBonus = new BigDecimal("200");
                totalScheduleSalary = totalScheduleSalary.add(attendanceBonus);
            }
            
            calculation.setScheduleSalary(totalScheduleSalary);
            calculation.setScheduleDays(scheduleDays);
            calculation.setScheduleHours(scheduleHours);
            calculation.setScheduleOvertimeHours(overtimeHours);
            
            logger.debug("排班工资计算完成: 排班天数={}, 排班小时={}, 加班小时={}, 总工资={}", 
                scheduleDays, scheduleHours, overtimeHours, totalScheduleSalary);
            
        } catch (Exception e) {
            logger.error("计算排班工资失败", e);
            throw new BusinessRunTimeException("计算排班工资失败: " + e.getMessage());
        }
    }

    /**
     * 计算基础工资
     */
    private void calculateBaseSalary(SalaryCalculation calculation, UserEmployee employee, JSONObject salaryData) throws Exception {
        // 基础工资
        BigDecimal baseSalary = employee.getBaseSalary();
        if (baseSalary == null) {
            baseSalary = BigDecimal.ZERO;
        }
        
        // 岗位津贴
        BigDecimal positionAllowance = getConfigValue("POSITION_ALLOWANCE_" + employee.getPosition(), employee.getId());
        if (positionAllowance == null) {
            positionAllowance = BigDecimal.ZERO;
        }
        
        // 绩效奖金
        JSONObject attendanceData = salaryData.getJSONObject("attendanceData");
        BigDecimal performanceBonus = BigDecimal.ZERO;
        if (attendanceData != null) {
            BigDecimal attendanceRate = attendanceData.getBigDecimal("attendanceRate");
            if (attendanceRate != null && attendanceRate.compareTo(new BigDecimal("95")) >= 0) {
                performanceBonus = baseSalary.multiply(new BigDecimal("0.1"));
            }
        }
        
        calculation.setBaseSalary(baseSalary);
        calculation.setPositionAllowance(positionAllowance);
        calculation.setPerformanceBonus(performanceBonus);
    }

    /**
     * 计算各项补贴
     */
    private void calculateAllowances(SalaryCalculation calculation, UserEmployee employee, JSONObject salaryData) throws Exception {
        // 餐费补贴
        BigDecimal mealAllowance = getConfigValue("MEAL_ALLOWANCE", employee.getId());
        calculation.setMealAllowance(mealAllowance != null ? mealAllowance : BigDecimal.ZERO);
        
        // 交通补贴
        BigDecimal transportAllowance = getConfigValue("TRANSPORT_ALLOWANCE", employee.getId());
        calculation.setTransportAllowance(transportAllowance != null ? transportAllowance : BigDecimal.ZERO);
        
        // 通讯补贴
        BigDecimal communicationAllowance = getConfigValue("COMMUNICATION_ALLOWANCE", employee.getId());
        calculation.setCommunicationAllowance(communicationAllowance != null ? communicationAllowance : BigDecimal.ZERO);
        
        // 住房补贴
        BigDecimal housingAllowance = getConfigValue("HOUSING_ALLOWANCE", employee.getId());
        calculation.setHousingAllowance(housingAllowance != null ? housingAllowance : BigDecimal.ZERO);
    }

    /**
     * 计算各项扣除
     */
    private void calculateDeductions(SalaryCalculation calculation, UserEmployee employee, JSONObject salaryData) throws Exception {
        // 社保扣除
        BigDecimal socialInsurance = getConfigValue("SOCIAL_INSURANCE_RATE", employee.getId());
        if (socialInsurance != null) {
            BigDecimal socialDeduction = calculation.getBaseSalary().multiply(socialInsurance).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
            calculation.setSocialInsurance(socialDeduction);
        }
        
        // 公积金扣除
        BigDecimal housingFund = getConfigValue("HOUSING_FUND_RATE", employee.getId());
        if (housingFund != null) {
            BigDecimal fundDeduction = calculation.getBaseSalary().multiply(housingFund).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
            calculation.setHousingFund(fundDeduction);
        }
        
        // 考勤扣除
        JSONObject attendanceData = salaryData.getJSONObject("attendanceData");
        if (attendanceData != null) {
            BigDecimal latePenalty = attendanceData.getBigDecimal("latePenalty");
            BigDecimal absenceDeduction = attendanceData.getBigDecimal("absenceDeduction");
            
            calculation.setLatePenalty(latePenalty != null ? latePenalty : BigDecimal.ZERO);
            calculation.setAbsenceDeduction(absenceDeduction != null ? absenceDeduction : BigDecimal.ZERO);
        }
    }

    /**
     * 计算总计金额
     */
    private void calculateTotals(SalaryCalculation calculation) {
        // 应发工资 = 基础工资 + 岗位津贴 + 绩效奖金 + 加班费 + 生产工费 + 团建提成 + 排班工资
        BigDecimal grossSalary = calculation.getBaseSalary()
            .add(calculation.getPositionAllowance())
            .add(calculation.getPerformanceBonus())
            .add(calculation.getOvertimePay())
            .add(calculation.getProductionSalary())
            .add(calculation.getTeambuildingCommission())
            .add(calculation.getScheduleSalary());
        
        // 补贴合计
        BigDecimal totalAllowance = calculation.getMealAllowance()
            .add(calculation.getTransportAllowance())
            .add(calculation.getCommunicationAllowance())
            .add(calculation.getHousingAllowance())
            .add(calculation.getOtherAllowance());
        
        // 扣除合计
        BigDecimal totalDeduction = calculation.getSocialInsurance()
            .add(calculation.getHousingFund())
            .add(calculation.getPersonalTax())
            .add(calculation.getLatePenalty())
            .add(calculation.getAbsenceDeduction())
            .add(calculation.getOtherDeduction());
        
        // 实发工资 = 应发工资 + 补贴合计 - 扣除合计
        BigDecimal netSalary = grossSalary.add(totalAllowance).subtract(totalDeduction);
        
        calculation.setGrossSalary(grossSalary);
        calculation.setTotalAllowance(totalAllowance);
        calculation.setTotalDeduction(totalDeduction);
        calculation.setNetSalary(netSalary);
    }

    /**
     * 保存薪酬明细
     */
    private void saveSalaryDetails(Long calculationId, JSONObject salaryData, HttpServletRequest request) throws Exception {
        // 删除旧明细
        salaryDetailMapper.deleteByCalculationId(calculationId);
        
        // 保存生产工费明细
        saveProductionDetails(calculationId, salaryData.getJSONObject("productionData"), request);
        
        // 保存团建提成明细
        saveTeambuildingDetails(calculationId, salaryData.getJSONObject("teambuildingData"), request);
        
        // 保存排班工资明细
        saveScheduleDetails(calculationId, salaryData.getJSONObject("scheduleData"), request);
    }

    /**
     * 保存生产工费明细
     */
    private void saveProductionDetails(Long calculationId, JSONObject productionData, HttpServletRequest request) throws Exception {
        if (productionData == null) return;
        
        // 这里可以保存详细的生产工单明细
        // 实现略...
    }

    /**
     * 保存团建提成明细
     */
    private void saveTeambuildingDetails(Long calculationId, JSONObject teambuildingData, HttpServletRequest request) throws Exception {
        if (teambuildingData == null) return;
        
        // 这里可以保存详细的团建活动明细
        // 实现略...
    }

    /**
     * 保存排班工资明细
     */
    private void saveScheduleDetails(Long calculationId, JSONObject scheduleData, HttpServletRequest request) throws Exception {
        if (scheduleData == null) return;
        
        // 这里可以保存详细的排班记录明细
        // 实现略...
    }

    /**
     * 获取配置值
     */
    private BigDecimal getConfigValue(String configCode, Long employeeId) {
        try {
            SalaryConfig config = salaryConfigMapper.findActiveConfig(configCode, employeeId);
            return config != null ? config.getConfigValue() : null;
        } catch (Exception e) {
            logger.warn("获取配置值失败: configCode={}, employeeId={}", configCode, employeeId);
            return null;
        }
    }

    /**
     * 生成核算编号
     */
    private String generateCalculationNo(String salaryPeriod) {
        String prefix = "SC" + salaryPeriod.replace("-", "");
        
        // 查询当月最大序号
        String maxNo = salaryCalculationMapper.findMaxCalculationNoByPrefix(prefix);
        
        int sequence = 1;
        if (maxNo != null && maxNo.length() > prefix.length()) {
            String seqStr = maxNo.substring(prefix.length());
            try {
                sequence = Integer.parseInt(seqStr) + 1;
            } catch (NumberFormatException e) {
                sequence = 1;
            }
        }
        
        return prefix + String.format("%04d", sequence);
    }

    /**
     * 验证计算参数
     */
    private void validateCalculationParams(Long employeeId, String salaryPeriod) throws Exception {
        if (employeeId == null) {
            throw new BusinessRunTimeException("员工ID不能为空");
        }
        
        if (salaryPeriod == null || !salaryPeriod.matches("\\d{4}-\\d{2}")) {
            throw new BusinessRunTimeException("薪酬周期格式错误，正确格式：YYYY-MM");
        }
    }

    /**
     * 获取当前租户ID
     */
    private Long getCurrentTenantId() {
        return 1L; // 临时实现
    }

    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId(HttpServletRequest request) {
        return 1L; // 临时实现
    }
}
```

### 2. SalaryDataCollectionService.java - 薪酬数据收集服务

**文件路径**: `com.jsh.erp.salary.service.SalaryDataCollectionService`

```java
package com.jsh.erp.salary.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jsh.erp.exception.BusinessRunTimeException;
import com.jsh.erp.production.datasource.mappers.ProductionOrderMapperEx;
import com.jsh.erp.production.datasource.mappers.WorkReportMapperEx;
import com.jsh.erp.teambuilding.datasource.mappers.TeambuildingParticipantMapperEx;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 薪酬数据收集服务
 * 从各个业务模块收集薪酬相关数据
 */
@Service
public class SalaryDataCollectionService {
    private Logger logger = LoggerFactory.getLogger(SalaryDataCollectionService.class);

    @Resource
    private ProductionOrderMapperEx productionOrderMapperEx;
    @Resource
    private WorkReportMapperEx workReportMapperEx;
    @Resource
    private TeambuildingParticipantMapperEx teambuildingParticipantMapperEx;

    /**
     * 收集所有薪酬数据
     * 汇总员工在指定月份的所有薪酬相关数据
     */
    public JSONObject collectAllSalaryData(Long employeeId, String salaryPeriod) throws Exception {
        try {
            JSONObject allData = new JSONObject();
            
            // 1. 收集生产数据
            JSONObject productionData = collectProductionData(employeeId, salaryPeriod);
            allData.put("productionData", productionData);
            
            // 2. 收集团建数据
            JSONObject teambuildingData = collectTeambuildingData(employeeId, salaryPeriod);
            allData.put("teambuildingData", teambuildingData);
            
            // 3. 收集排班数据
            JSONObject scheduleData = collectScheduleData(employeeId, salaryPeriod);
            allData.put("scheduleData", scheduleData);
            
            // 4. 收集考勤数据
            JSONObject attendanceData = collectAttendanceData(employeeId, salaryPeriod);
            allData.put("attendanceData", attendanceData);
            
            logger.info("薪酬数据收集完成: employeeId={}, period={}", employeeId, salaryPeriod);
            return allData;
            
        } catch (Exception e) {
            logger.error("收集薪酬数据失败", e);
            throw new BusinessRunTimeException("收集薪酬数据失败: " + e.getMessage());
        }
    }

    /**
     * 收集生产数据
     * 收集员工在指定月份的生产工单和报工数据
     */
    public JSONObject collectProductionData(Long employeeId, String salaryPeriod) throws Exception {
        try {
            JSONObject productionData = new JSONObject();
            
            // 计算月份起止日期
            Date[] dateRange = calculateMonthDateRange(salaryPeriod);
            Date startDate = dateRange[0];
            Date endDate = dateRange[1];
            
            // 1. 查询生产工单数据
            List<Map<String, Object>> productionOrders = productionOrderMapperEx.findByWorkerAndDateRange(
                employeeId, startDate, endDate);
            
            BigDecimal totalQuantity = BigDecimal.ZERO;
            BigDecimal totalAmount = BigDecimal.ZERO;
            BigDecimal totalQualityScore = BigDecimal.ZERO;
            int orderCount = 0;
            
            for (Map<String, Object> order : productionOrders) {
                BigDecimal quantity = (BigDecimal) order.get("quantity");
                BigDecimal unitPrice = (BigDecimal) order.get("unit_price");
                BigDecimal qualityScore = (BigDecimal) order.get("quality_score");
                
                if (quantity != null) {
                    totalQuantity = totalQuantity.add(quantity);
                }
                
                if (quantity != null && unitPrice != null) {
                    totalAmount = totalAmount.add(quantity.multiply(unitPrice));
                }
                
                if (qualityScore != null) {
                    totalQualityScore = totalQualityScore.add(qualityScore);
                }
                
                orderCount++;
            }
            
            // 2. 查询报工数据
            List<Map<String, Object>> workReports = workReportMapperEx.findByWorkerAndDateRange(
                employeeId, startDate, endDate);
            
            BigDecimal totalWorkHours = BigDecimal.ZERO;
            BigDecimal totalCompletedQuantity = BigDecimal.ZERO;
            
            for (Map<String, Object> report : workReports) {
                BigDecimal workHours = (BigDecimal) report.get("work_hours");
                BigDecimal completedQuantity = (BigDecimal) report.get("completed_quantity");
                
                if (workHours != null) {
                    totalWorkHours = totalWorkHours.add(workHours);
                }
                
                if (completedQuantity != null) {
                    totalCompletedQuantity = totalCompletedQuantity.add(completedQuantity);
                }
            }
            
            // 3. 计算统计数据
            BigDecimal averageUnitPrice = BigDecimal.ZERO;
            if (totalQuantity.compareTo(BigDecimal.ZERO) > 0) {
                averageUnitPrice = totalAmount.divide(totalQuantity, 4, RoundingMode.HALF_UP);
            }
            
            BigDecimal averageQualityScore = BigDecimal.ZERO;
            if (orderCount > 0) {
                averageQualityScore = totalQualityScore.divide(new BigDecimal(orderCount), 2, RoundingMode.HALF_UP);
            }
            
            BigDecimal efficiencyRatio = BigDecimal.ZERO;
            if (totalWorkHours.compareTo(BigDecimal.ZERO) > 0) {
                // 假设标准效率为每小时完成10件
                BigDecimal standardQuantity = totalWorkHours.multiply(new BigDecimal("10"));
                efficiencyRatio = totalCompletedQuantity.divide(standardQuantity, 4, RoundingMode.HALF_UP);
            }
            
            // 4. 封装返回数据
            productionData.put("totalQuantity", totalQuantity);
            productionData.put("totalAmount", totalAmount);
            productionData.put("averageUnitPrice", averageUnitPrice);
            productionData.put("averageQualityScore", averageQualityScore);
            productionData.put("totalWorkHours", totalWorkHours);
            productionData.put("totalCompletedQuantity", totalCompletedQuantity);
            productionData.put("efficiencyRatio", efficiencyRatio);
            productionData.put("orderCount", orderCount);
            productionData.put("reportCount", workReports.size());
            
            logger.debug("生产数据收集完成: 工单数={}, 总数量={}, 总金额={}", 
                orderCount, totalQuantity, totalAmount);
            
            return productionData;
            
        } catch (Exception e) {
            logger.error("收集生产数据失败", e);
            throw new BusinessRunTimeException("收集生产数据失败: " + e.getMessage());
        }
    }

    /**
     * 收集团建数据
     * 收集员工在指定月份的团建活动参与数据
     */
    public JSONObject collectTeambuildingData(Long employeeId, String salaryPeriod) throws Exception {
        try {
            JSONObject teambuildingData = new JSONObject();
            
            // 计算月份起止日期
            Date[] dateRange = calculateMonthDateRange(salaryPeriod);
            Date startDate = dateRange[0];
            Date endDate = dateRange[1];
            
            // 查询团建参与数据
            List<Map<String, Object>> participations = teambuildingParticipantMapperEx.findByEmployeeAndDateRange(
                employeeId, startDate, endDate);
            
            int activityCount = 0;
            BigDecimal totalRevenue = BigDecimal.ZERO;
            BigDecimal totalHours = BigDecimal.ZERO;
            BigDecimal totalSatisfaction = BigDecimal.ZERO;
            int satisfactionCount = 0;
            
            for (Map<String, Object> participation : participations) {
                BigDecimal revenue = (BigDecimal) participation.get("activity_revenue");
                BigDecimal hours = (BigDecimal) participation.get("work_hours");
                BigDecimal satisfaction = (BigDecimal) participation.get("feedback_score");
                
                if (revenue != null) {
                    totalRevenue = totalRevenue.add(revenue);
                }
                
                if (hours != null) {
                    totalHours = totalHours.add(hours);
                }
                
                if (satisfaction != null) {
                    totalSatisfaction = totalSatisfaction.add(satisfaction);
                    satisfactionCount++;
                }
                
                activityCount++;
            }
            
            // 计算平均满意度
            BigDecimal averageSatisfaction = BigDecimal.ZERO;
            if (satisfactionCount > 0) {
                averageSatisfaction = totalSatisfaction.divide(new BigDecimal(satisfactionCount), 2, RoundingMode.HALF_UP);
            }
            
            // 封装返回数据
            teambuildingData.put("activityCount", activityCount);
            teambuildingData.put("totalRevenue", totalRevenue);
            teambuildingData.put("totalHours", totalHours);
            teambuildingData.put("averageSatisfaction", averageSatisfaction);
            
            logger.debug("团建数据收集完成: 活动数={}, 总收入={}, 总时长={}", 
                activityCount, totalRevenue, totalHours);
            
            return teambuildingData;
            
        } catch (Exception e) {
            logger.error("收集团建数据失败", e);
            throw new BusinessRunTimeException("收集团建数据失败: " + e.getMessage());
        }
    }

    /**
     * 收集排班数据
     * 收集员工在指定月份的排班和加班数据
     */
    public JSONObject collectScheduleData(Long employeeId, String salaryPeriod) throws Exception {
        try {
            JSONObject scheduleData = new JSONObject();
            
            // 这里应该从排班系统获取数据
            // 由于排班模块还未实现，这里先返回模拟数据
            
            scheduleData.put("scheduleDays", 22);
            scheduleData.put("scheduleHours", new BigDecimal("176"));
            scheduleData.put("overtimeHours", new BigDecimal("20"));
            scheduleData.put("lateCount", 2);
            scheduleData.put("absentCount", 0);
            
            logger.debug("排班数据收集完成: 排班天数=22, 排班小时=176, 加班小时=20");
            
            return scheduleData;
            
        } catch (Exception e) {
            logger.error("收集排班数据失败", e);
            throw new BusinessRunTimeException("收集排班数据失败: " + e.getMessage());
        }
    }

    /**
     * 收集考勤数据
     * 收集员工在指定月份的考勤相关数据
     */
    public JSONObject collectAttendanceData(Long employeeId, String salaryPeriod) throws Exception {
        try {
            JSONObject attendanceData = new JSONObject();
            
            // 这里应该从考勤系统获取数据
            // 由于考勤模块还未实现，这里先返回模拟数据
            
            attendanceData.put("workDays", new BigDecimal("22"));
            attendanceData.put("attendanceRate", new BigDecimal("96.5"));
            attendanceData.put("lateCount", 2);
            attendanceData.put("latePenalty", new BigDecimal("50"));
            attendanceData.put("absentCount", 0);
            attendanceData.put("absenceDeduction", new BigDecimal("0"));
            
            logger.debug("考勤数据收集完成: 工作天数=22, 出勤率=96.5%, 迟到次数=2");
            
            return attendanceData;
            
        } catch (Exception e) {
            logger.error("收集考勤数据失败", e);
            throw new BusinessRunTimeException("收集考勤数据失败: " + e.getMessage());
        }
    }

    /**
     * 计算月份起止日期
     */
    private Date[] calculateMonthDateRange(String salaryPeriod) throws Exception {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
            Date periodDate = sdf.parse(salaryPeriod);
            
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(periodDate);
            
            // 月初
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            Date startDate = calendar.getTime();
            
            // 月末
            calendar.add(Calendar.MONTH, 1);
            calendar.add(Calendar.DAY_OF_MONTH, -1);
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            calendar.set(Calendar.MILLISECOND, 999);
            Date endDate = calendar.getTime();
            
            return new Date[]{startDate, endDate};
            
        } catch (Exception e) {
            throw new BusinessRunTimeException("计算月份起止日期失败: " + e.getMessage());
        }
    }
}
```

---

## 前端开发 (3天)

### 1. SalaryCalculationList.vue - 薪酬核算列表页面

**文件路径**: `jshERP-web/src/views/salary/SalaryCalculationList.vue`

```vue
<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :md="6" :sm="24">
            <a-form-item label="薪酬周期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-month-picker 
                v-model="queryParam.salaryPeriod" 
                placeholder="选择薪酬周期"
                format="YYYY-MM" />
            </a-form-item>
          </a-col>
          
          <a-col :md="6" :sm="24">
            <a-form-item label="员工姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-model="queryParam.employeeName" placeholder="请输入员工姓名" />
            </a-form-item>
          </a-col>
          
          <a-col :md="6" :sm="24">
            <a-form-item label="员工工号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-model="queryParam.employeeNo" placeholder="请输入员工工号" />
            </a-form-item>
          </a-col>
          
          <a-col :md="6" :sm="24">
            <a-form-item label="核算状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select v-model="queryParam.calculationStatus" placeholder="请选择状态" allowClear>
                <a-select-option value="">全部</a-select-option>
                <a-select-option value="0">计算中</a-select-option>
                <a-select-option value="1">已完成</a-select-option>
                <a-select-option value="2">已审核</a-select-option>
                <a-select-option value="3">已发放</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          
          <a-col :md="6" :sm="24">
            <a-form-item label="部门" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select v-model="queryParam.departmentId" placeholder="请选择部门" allowClear>
                <a-select-option v-for="dept in departmentList" :key="dept.id" :value="dept.id">
                  {{ dept.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          
          <a-col :md="6" :sm="24">
            <span class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button style="margin-left: 8px" @click="searchReset" icon="reload">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button v-has="'salary:calculation:batch'" type="primary" icon="calculator" @click="handleBatchCalculate">批量核算</a-button>
      <a-button v-has="'salary:calculation:add'" type="default" icon="plus" @click="handleAdd">单独核算</a-button>
      <a-button v-has="'salary:calculation:export'" type="default" icon="export" @click="handleExport">导出薪资条</a-button>
      
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchApprove">
            <a-icon type="check-circle" />批量审核
          </a-menu-item>
          <a-menu-item key="2" @click="batchPay">
            <a-icon type="dollar" />批量发放
          </a-menu-item>
          <a-menu-item key="3" @click="batchRecalculate">
            <a-icon type="reload" />重新核算
          </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px">
          批量操作 <a-icon type="down" />
        </a-button>
      </a-dropdown>
    </div>

    <!-- 薪酬统计卡片 -->
    <div class="salary-stats-cards" style="margin-bottom: 16px;">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="本月核算人数"
              :value="stats.calculatedCount"
              value-style="color: #3f8600"
              prefix-icon="team" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="待审核人数"
              :value="stats.pendingApprovalCount"
              value-style="color: #cf1322"
              prefix-icon="clock-circle" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="本月薪酬总额"
              :value="stats.totalSalary"
              :precision="2"
              suffix="元"
              value-style="color: #3f8600"
              prefix-icon="dollar" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="平均薪酬"
              :value="stats.averageSalary"
              :precision="2"
              suffix="元"
              value-style="color: #3f8600"
              prefix-icon="bar-chart" />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 数据表格 -->
    <a-table
      ref="table"
      size="small"
      :columns="columns"
      :dataSource="dataSource"
      :pagination="ipagination"
      :loading="loading"
      :rowSelection="rowSelection"
      @change="handleTableChange"
      bordered>
      
      <!-- 员工信息 -->
      <template slot="employeeInfo" slot-scope="text, record">
        <div>
          <strong>{{ record.employeeName }}</strong>
          <br>
          <small style="color: #999;">{{ record.employeeNo }} | {{ record.position }}</small>
        </div>
      </template>
      
      <!-- 薪酬类型 -->
      <template slot="salaryType" slot-scope="text">
        <a-tag :color="getSalaryTypeColor(text)">{{ text }}</a-tag>
      </template>
      
      <!-- 核算状态 -->
      <template slot="calculationStatus" slot-scope="text">
        <a-badge :status="getStatusBadge(text).status" :text="getStatusBadge(text).text" />
      </template>
      
      <!-- 应发工资 -->
      <template slot="grossSalary" slot-scope="text">
        <span style="color: #f50; font-weight: bold;">￥{{ text | currency }}</span>
      </template>
      
      <!-- 实发工资 -->
      <template slot="netSalary" slot-scope="text">
        <span style="color: #52c41a; font-weight: bold;">￥{{ text | currency }}</span>
      </template>
      
      <!-- 薪酬构成 -->
      <template slot="salaryComposition" slot-scope="text, record">
        <a-tooltip title="点击查看详细构成">
          <a @click="showSalaryDetail(record)">
            <a-icon type="pie-chart" /> 查看构成
          </a>
        </a-tooltip>
      </template>
      
      <!-- 操作列 -->
      <template slot="action" slot-scope="text, record">
        <a-dropdown>
          <a class="ant-dropdown-link">
            操作 <a-icon type="down" />
          </a>
          <a-menu slot="overlay">
            <a-menu-item v-has="'salary:calculation:view'">
              <a @click="handleDetail(record)"><a-icon type="eye" />查看详情</a>
            </a-menu-item>
            <a-menu-item v-has="'salary:calculation:edit'" v-if="record.calculationStatus === '0'">
              <a @click="handleEdit(record)"><a-icon type="edit" />编辑</a>
            </a-menu-item>
            <a-menu-item v-has="'salary:calculation:recalculate'" v-if="record.calculationStatus <= '1'">
              <a @click="handleRecalculate(record)"><a-icon type="reload" />重新核算</a>
            </a-menu-item>
            <a-menu-item v-has="'salary:calculation:approve'" v-if="record.calculationStatus === '1'">
              <a @click="handleApprove(record)"><a-icon type="check-circle" />审核</a>
            </a-menu-item>
            <a-menu-item v-has="'salary:calculation:pay'" v-if="record.calculationStatus === '2'">
              <a @click="handlePay(record)"><a-icon type="dollar" />发放</a>
            </a-menu-item>
            <a-menu-item v-has="'salary:calculation:payroll'">
              <a @click="handlePayroll(record)"><a-icon type="file-pdf" />生成薪资条</a>
            </a-menu-item>
            <a-menu-divider />
            <a-menu-item v-has="'salary:calculation:delete'" v-if="record.calculationStatus === '0'">
              <a @click="handleDelete(record)" style="color: #ff4d4f;">
                <a-icon type="delete" />删除
              </a>
            </a-menu-item>
          </a-menu>
        </a-dropdown>
      </template>
    </a-table>

    <!-- 薪酬核算弹窗 -->
    <salary-calculation-modal 
      ref="modalForm" 
      @ok="modalFormOk"
      :employeeList="employeeList">
    </salary-calculation-modal>

    <!-- 薪资条查看器 -->
    <payroll-viewer 
      ref="payrollViewer"
      @print="handlePrint">
    </payroll-viewer>

    <!-- 薪酬配置弹窗 -->
    <salary-config-modal 
      ref="configModal"
      @ok="configModalOk">
    </salary-config-modal>

    <!-- 批量核算弹窗 -->
    <batch-calculate-modal
      ref="batchCalculateModal"
      @ok="batchCalculateOk">
    </batch-calculate-modal>
  </a-card>
</template>

<script>
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import SalaryCalculationModal from './modules/SalaryCalculationModal'
import PayrollViewer from './modules/PayrollViewer'
import SalaryConfigModal from './modules/SalaryConfigModal'
import BatchCalculateModal from './modules/BatchCalculateModal'

export default {
  name: 'SalaryCalculationList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    SalaryCalculationModal,
    PayrollViewer,
    SalaryConfigModal,
    BatchCalculateModal
  },
  data() {
    return {
      description: '薪酬核算管理',
      // 表格列定义
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: (text, record, index) => index + 1
        },
        {
          title: '核算编号',
          align: 'center',
          dataIndex: 'calculationNo',
          width: 140
        },
        {
          title: '薪酬周期',
          align: 'center',
          dataIndex: 'salaryPeriod',
          width: 100
        },
        {
          title: '员工信息',
          align: 'left',
          dataIndex: 'employeeInfo',
          width: 180,
          scopedSlots: { customRender: 'employeeInfo' }
        },
        {
          title: '部门',
          align: 'center',
          dataIndex: 'departmentName',
          width: 120
        },
        {
          title: '薪酬类型',
          align: 'center',
          dataIndex: 'salaryType',
          width: 100,
          scopedSlots: { customRender: 'salaryType' }
        },
        {
          title: '应发工资',
          align: 'right',
          dataIndex: 'grossSalary',
          width: 120,
          scopedSlots: { customRender: 'grossSalary' }
        },
        {
          title: '扣除金额',
          align: 'right',
          dataIndex: 'totalDeduction',
          width: 110,
          customRender: (text) => `￥${this.$options.filters.currency(text)}`
        },
        {
          title: '实发工资',
          align: 'right',
          dataIndex: 'netSalary',
          width: 120,
          scopedSlots: { customRender: 'netSalary' }
        },
        {
          title: '薪酬构成',
          align: 'center',
          dataIndex: 'salaryComposition',
          width: 100,
          scopedSlots: { customRender: 'salaryComposition' }
        },
        {
          title: '核算状态',
          align: 'center',
          dataIndex: 'calculationStatus',
          width: 100,
          scopedSlots: { customRender: 'calculationStatus' }
        },
        {
          title: '核算日期',
          align: 'center',
          dataIndex: 'calculationDate',
          width: 110,
          customRender: (text) => text ? this.$moment(text).format('YYYY-MM-DD') : ''
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 120,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/salary/calculation/list',
        delete: '/salary/calculation/delete',
        deleteBatch: '/salary/calculation/deleteBatch',
        exportXlsUrl: '/salary/calculation/exportXls',
        importExcelUrl: '/salary/calculation/importExcel'
      },
      dictOptions: {},
      // 统计数据
      stats: {
        calculatedCount: 0,
        pendingApprovalCount: 0,
        totalSalary: 0,
        averageSalary: 0
      },
      // 下拉选项数据
      departmentList: [],
      employeeList: []
    }
  },
  created() {
    this.getSuperFieldList()
    this.loadDepartmentList()
    this.loadEmployeeList()
    this.loadStats()
  },
  methods: {
    initDictConfig() {
      // 初始化字典配置
    },
    getSuperFieldList() {
      // 获取字段配置
    },
    
    // 获取薪酬类型颜色
    getSalaryTypeColor(type) {
      const colorMap = {
        '月薪': 'blue',
        '计件': 'green',
        '混合': 'purple'
      }
      return colorMap[type] || 'default'
    },
    
    // 获取状态徽章
    getStatusBadge(status) {
      const badgeMap = {
        '0': { status: 'processing', text: '计算中' },
        '1': { status: 'warning', text: '已完成' },
        '2': { status: 'success', text: '已审核' },
        '3': { status: 'default', text: '已发放' }
      }
      return badgeMap[status] || { status: 'default', text: '未知' }
    },

    // 批量核算
    handleBatchCalculate() {
      this.$refs.batchCalculateModal.show()
    },

    // 重新核算
    handleRecalculate(record) {
      this.$confirm({
        title: '重新核算',
        content: `确定要重新核算 "${record.employeeName}" 的薪酬吗？`,
        onOk: () => {
          this.$http.post('/salary/calculation/recalculate', { id: record.id })
            .then((res) => {
              if (res.success) {
                this.$message.success('重新核算成功')
                this.loadData()
              } else {
                this.$message.error(res.message || '重新核算失败')
              }
            })
        }
      })
    },

    // 审核薪酬
    handleApprove(record) {
      this.$confirm({
        title: '审核薪酬',
        content: `确定要审核 "${record.employeeName}" 的薪酬吗？`,
        onOk: () => {
          this.$http.post('/salary/calculation/approve', { id: record.id })
            .then((res) => {
              if (res.success) {
                this.$message.success('审核成功')
                this.loadData()
              } else {
                this.$message.error(res.message || '审核失败')
              }
            })
        }
      })
    },

    // 发放薪酬
    handlePay(record) {
      this.$confirm({
        title: '发放薪酬',
        content: `确定要发放 "${record.employeeName}" 的薪酬吗？`,
        onOk: () => {
          this.$http.post('/salary/calculation/pay', { id: record.id })
            .then((res) => {
              if (res.success) {
                this.$message.success('发放成功')
                this.loadData()
              } else {
                this.$message.error(res.message || '发放失败')
              }
            })
        }
      })
    },

    // 生成薪资条
    handlePayroll(record) {
      this.$refs.payrollViewer.show(record.id)
    },

    // 查看薪酬详情
    showSalaryDetail(record) {
      this.$router.push({
        name: 'SalaryCalculationDetail',
        params: { id: record.id }
      })
    },

    // 批量操作方法
    batchApprove() {
      this.batchOperation('approve', '批量审核')
    },

    batchPay() {
      this.batchOperation('pay', '批量发放')
    },

    batchRecalculate() {
      this.batchOperation('recalculate', '重新核算')
    },

    batchOperation(operation, operationName) {
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning(`请选择要${operationName}的记录`)
        return
      }
      
      this.$confirm({
        title: operationName,
        content: `确定要${operationName}选中的 ${this.selectedRowKeys.length} 条记录吗？`,
        onOk: () => {
          this.$http.post(`/salary/calculation/batch${operation}`, {
            ids: this.selectedRowKeys.join(',')
          }).then((res) => {
            if (res.success) {
              this.$message.success(`${operationName}成功`)
              this.loadData()
              this.onClearSelected()
            } else {
              this.$message.error(res.message || `${operationName}失败`)
            }
          })
        }
      })
    },

    // 批量核算完成
    batchCalculateOk() {
      this.loadData()
      this.loadStats()
    },

    // 加载部门列表
    loadDepartmentList() {
      this.$http.get('/sys/sysDepart/list').then((res) => {
        if (res.success) {
          this.departmentList = res.result.records || res.result
        }
      })
    },

    // 加载员工列表
    loadEmployeeList() {
      this.$http.get('/sys/user/employeeList').then((res) => {
        if (res.success) {
          this.employeeList = res.result
        }
      })
    },

    // 加载统计数据
    loadStats() {
      this.$http.get('/salary/calculation/stats').then((res) => {
        if (res.success) {
          this.stats = res.result
        }
      })
    }
  }
}
</script>

<style scoped>
.salary-stats-cards {
  margin-bottom: 16px;
}

.ant-statistic-content {
  font-size: 20px;
  font-weight: bold;
}

.table-operator {
  margin-bottom: 16px;
}

.table-operator .ant-btn {
  margin-right: 8px;
}
</style>
```

---

## 验收标准

### 功能验收标准
1. **自动化核算**: 支持基于生产、团建、排班数据的自动薪酬核算
2. **多维度计算**: 支持基础工资、计件工资、提成、奖金、扣除等多维度计算
3. **薪资条生成**: 支持标准格式薪资条的生成和导出
4. **审批流程**: 支持薪酬核算的审核和发放流程

### 质量验收标准
1. **计算准确性**: 薪酬计算准确无误，支持复杂的计算规则
2. **数据完整性**: 所有薪酬相关数据完整记录
3. **多租户支持**: 正确实现租户数据隔离
4. **权限控制**: 按角色控制薪酬数据访问权限

### 业务验收标准
1. **业务流程完整**: 涵盖薪酬核算的完整业务流程
2. **配置灵活性**: 支持灵活的薪酬配置和规则设置
3. **报表功能**: 提供准确的薪酬统计分析功能
4. **集成性良好**: 与生产、团建等模块良好集成

---

## 交付物清单

### 代码交付物
1. **后端代码**: Service、Controller、Mapper等Java类
2. **前端代码**: Vue组件和页面
3. **数据库脚本**: 表结构和初始数据
4. **配置文件**: 薪酬计算规则配置

### 文档交付物
1. **开发文档**: 本文档
2. **接口文档**: API接口说明文档
3. **用户手册**: 薪酬管理操作手册
4. **配置说明**: 薪酬规则配置说明

---

## 后续优化建议

### 功能优化
1. **智能预测**: 基于历史数据预测薪酬趋势
2. **税务计算**: 集成个人所得税自动计算
3. **银行接口**: 集成银行代发接口
4. **移动端**: 开发移动端薪资查询应用

### 技术优化
1. **性能优化**: 优化大批量数据的计算性能
2. **计算引擎**: 开发更强大的薪酬计算引擎
3. **规则引擎**: 实现可视化的薪酬规则配置
4. **数据分析**: 增强薪酬数据分析能力