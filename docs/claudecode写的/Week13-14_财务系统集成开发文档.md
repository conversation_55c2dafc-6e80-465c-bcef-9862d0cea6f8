# Week 13-14: 财务系统集成开发文档

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-06-18
- **项目阶段**: 第二阶段 - 业务支撑模块
- **估算工期**: 10天
- **技术参考**: 严格遵循《jshERP_二次开发技术参考手册》第8章集成指导

---

## 概述

本文档为Week 13-14的财务系统集成开发提供详细的实施指导。主要实现生产成本核算、团建收入管理、薪酬支出处理、自动化会计分录生成等核心功能，建立完整的财务数据集成体系，为聆花文化提供准确的财务管理和成本分析支持。

---

## 数据同步服务开发 (3天)

### 1. FinanceIntegrationService.java - 财务集成服务

**文件路径**: `com.jsh.erp.finance.service.FinanceIntegrationService`

```java
package com.jsh.erp.finance.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jsh.erp.exception.BusinessRunTimeException;
import com.jsh.erp.exception.JshException;
import com.jsh.erp.datasource.entities.Account;
import com.jsh.erp.datasource.entities.AccountHead;
import com.jsh.erp.datasource.entities.AccountItem;
import com.jsh.erp.datasource.mappers.AccountMapper;
import com.jsh.erp.datasource.mappers.AccountHeadMapper;
import com.jsh.erp.datasource.mappers.AccountItemMapper;
import com.jsh.erp.production.service.ProductionOrderService;
import com.jsh.erp.teambuilding.service.TeambuildingActivityService;
import com.jsh.erp.salary.service.SalaryCalculationService;
import com.jsh.erp.service.log.LogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 财务集成服务
 * 实现业务模块与财务系统的数据同步和集成
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Service
public class FinanceIntegrationService {
    private Logger logger = LoggerFactory.getLogger(FinanceIntegrationService.class);

    @Resource
    private AccountMapper accountMapper;
    @Resource
    private AccountHeadMapper accountHeadMapper;
    @Resource
    private AccountItemMapper accountItemMapper;
    @Resource
    private ProductionOrderService productionOrderService;
    @Resource
    private TeambuildingActivityService teambuildingActivityService;
    @Resource
    private SalaryCalculationService salaryCalculationService;
    @Resource
    private CostCalculationService costCalculationService;
    @Resource
    private LogService logService;

    /**
     * 同步生产成本
     * 将生产模块的成本数据同步到财务系统
     */
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public String syncProductionCosts(String datePeriod, HttpServletRequest request) throws Exception {
        try {
            logger.info("开始同步生产成本数据: {}", datePeriod);
            
            // 1. 获取生产成本数据
            JSONObject productionCostData = collectProductionCostData(datePeriod);
            
            // 2. 创建财务凭证头
            AccountHead accountHead = createAccountHead("生产成本同步", datePeriod, request);
            
            // 3. 生成会计分录
            generateProductionCostEntries(accountHead.getId(), productionCostData, request);
            
            // 4. 更新财务科目余额
            updateAccountBalances(productionCostData);
            
            // 5. 记录同步日志
            String logContent = String.format("同步生产成本数据完成: 期间=%s, 凭证号=%s, 总金额=%s", 
                datePeriod, accountHead.getBillNo(), productionCostData.getBigDecimal("totalAmount"));
            logService.insertLog("财务同步", logContent, request);
            
            logger.info("生产成本同步完成: 凭证ID={}, 总金额={}", 
                accountHead.getId(), productionCostData.getBigDecimal("totalAmount"));
            
            return String.valueOf(accountHead.getId());
            
        } catch (Exception e) {
            logger.error("同步生产成本失败", e);
            throw new BusinessRunTimeException("同步生产成本失败: " + e.getMessage());
        }
    }

    /**
     * 同步团建收入
     * 将团建活动的收入数据同步到财务系统
     */
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public String syncTeambuildingRevenue(String datePeriod, HttpServletRequest request) throws Exception {
        try {
            logger.info("开始同步团建收入数据: {}", datePeriod);
            
            // 1. 获取团建收入数据
            JSONObject revenueData = collectTeambuildingRevenueData(datePeriod);
            
            // 2. 创建财务凭证头
            AccountHead accountHead = createAccountHead("团建收入同步", datePeriod, request);
            
            // 3. 生成收入会计分录
            generateRevenueEntries(accountHead.getId(), revenueData, request);
            
            // 4. 更新财务科目余额
            updateAccountBalances(revenueData);
            
            // 5. 记录同步日志
            String logContent = String.format("同步团建收入数据完成: 期间=%s, 凭证号=%s, 总收入=%s", 
                datePeriod, accountHead.getBillNo(), revenueData.getBigDecimal("totalRevenue"));
            logService.insertLog("财务同步", logContent, request);
            
            logger.info("团建收入同步完成: 凭证ID={}, 总收入={}", 
                accountHead.getId(), revenueData.getBigDecimal("totalRevenue"));
            
            return String.valueOf(accountHead.getId());
            
        } catch (Exception e) {
            logger.error("同步团建收入失败", e);
            throw new BusinessRunTimeException("同步团建收入失败: " + e.getMessage());
        }
    }

    /**
     * 同步薪酬支出
     * 将薪酬核算的支出数据同步到财务系统
     */
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public String syncPayrollExpenses(String salaryPeriod, HttpServletRequest request) throws Exception {
        try {
            logger.info("开始同步薪酬支出数据: {}", salaryPeriod);
            
            // 1. 获取薪酬支出数据
            JSONObject payrollData = collectPayrollExpenseData(salaryPeriod);
            
            // 2. 创建财务凭证头
            AccountHead accountHead = createAccountHead("薪酬支出同步", salaryPeriod, request);
            
            // 3. 生成薪酬支出会计分录
            generatePayrollEntries(accountHead.getId(), payrollData, request);
            
            // 4. 更新财务科目余额
            updateAccountBalances(payrollData);
            
            // 5. 记录同步日志
            String logContent = String.format("同步薪酬支出数据完成: 期间=%s, 凭证号=%s, 总支出=%s", 
                salaryPeriod, accountHead.getBillNo(), payrollData.getBigDecimal("totalExpense"));
            logService.insertLog("财务同步", logContent, request);
            
            logger.info("薪酬支出同步完成: 凭证ID={}, 总支出={}", 
                accountHead.getId(), payrollData.getBigDecimal("totalExpense"));
            
            return String.valueOf(accountHead.getId());
            
        } catch (Exception e) {
            logger.error("同步薪酬支出失败", e);
            throw new BusinessRunTimeException("同步薪酬支出失败: " + e.getMessage());
        }
    }

    /**
     * 生成会计分录
     * 根据业务数据自动生成标准会计分录
     */
    public String generateAccountEntries(JSONObject businessData, String entryType, HttpServletRequest request) throws Exception {
        try {
            // 1. 验证业务数据
            validateBusinessData(businessData, entryType);
            
            // 2. 创建财务凭证头
            String billType = businessData.getString("billType");
            String period = businessData.getString("period");
            AccountHead accountHead = createAccountHead(billType, period, request);
            
            // 3. 根据分录类型生成对应分录
            switch (entryType) {
                case "PRODUCTION_COST":
                    generateProductionCostEntries(accountHead.getId(), businessData, request);
                    break;
                case "TEAMBUILDING_REVENUE":
                    generateRevenueEntries(accountHead.getId(), businessData, request);
                    break;
                case "PAYROLL_EXPENSE":
                    generatePayrollEntries(accountHead.getId(), businessData, request);
                    break;
                case "MATERIAL_PURCHASE":
                    generateMaterialPurchaseEntries(accountHead.getId(), businessData, request);
                    break;
                default:
                    throw new BusinessRunTimeException("不支持的分录类型: " + entryType);
            }
            
            // 4. 验证分录平衡
            validateAccountEntryBalance(accountHead.getId());
            
            logger.info("会计分录生成完成: 凭证ID={}, 分录类型={}", accountHead.getId(), entryType);
            return String.valueOf(accountHead.getId());
            
        } catch (Exception e) {
            logger.error("生成会计分录失败", e);
            throw new BusinessRunTimeException("生成会计分录失败: " + e.getMessage());
        }
    }

    /**
     * 收集生产成本数据
     */
    private JSONObject collectProductionCostData(String datePeriod) throws Exception {
        JSONObject costData = new JSONObject();
        
        // 1. 查询生产工单的直接成本
        List<Map<String, Object>> productionCosts = productionOrderService.getProductionCostByPeriod(datePeriod);
        
        BigDecimal totalMaterialCost = BigDecimal.ZERO;
        BigDecimal totalLaborCost = BigDecimal.ZERO;
        BigDecimal totalOverheadCost = BigDecimal.ZERO;
        
        JSONArray costDetails = new JSONArray();
        
        for (Map<String, Object> cost : productionCosts) {
            JSONObject detail = new JSONObject();
            
            BigDecimal materialCost = (BigDecimal) cost.get("material_cost");
            BigDecimal laborCost = (BigDecimal) cost.get("labor_cost");
            BigDecimal overheadCost = (BigDecimal) cost.get("overhead_cost");
            
            if (materialCost != null) {
                totalMaterialCost = totalMaterialCost.add(materialCost);
                detail.put("materialCost", materialCost);
            }
            
            if (laborCost != null) {
                totalLaborCost = totalLaborCost.add(laborCost);
                detail.put("laborCost", laborCost);
            }
            
            if (overheadCost != null) {
                totalOverheadCost = totalOverheadCost.add(overheadCost);
                detail.put("overheadCost", overheadCost);
            }
            
            detail.put("productionOrderId", cost.get("id"));
            detail.put("productName", cost.get("product_name"));
            detail.put("quantity", cost.get("quantity"));
            
            costDetails.add(detail);
        }
        
        BigDecimal totalAmount = totalMaterialCost.add(totalLaborCost).add(totalOverheadCost);
        
        costData.put("period", datePeriod);
        costData.put("totalMaterialCost", totalMaterialCost);
        costData.put("totalLaborCost", totalLaborCost);
        costData.put("totalOverheadCost", totalOverheadCost);
        costData.put("totalAmount", totalAmount);
        costData.put("costDetails", costDetails);
        
        return costData;
    }

    /**
     * 收集团建收入数据
     */
    private JSONObject collectTeambuildingRevenueData(String datePeriod) throws Exception {
        JSONObject revenueData = new JSONObject();
        
        // 查询团建活动收入
        List<Map<String, Object>> activities = teambuildingActivityService.getRevenueByPeriod(datePeriod);
        
        BigDecimal totalRevenue = BigDecimal.ZERO;
        BigDecimal totalCost = BigDecimal.ZERO;
        BigDecimal totalProfit = BigDecimal.ZERO;
        
        JSONArray revenueDetails = new JSONArray();
        
        for (Map<String, Object> activity : activities) {
            JSONObject detail = new JSONObject();
            
            BigDecimal revenue = (BigDecimal) activity.get("total_budget");
            BigDecimal cost = (BigDecimal) activity.get("actual_cost");
            BigDecimal profit = revenue.subtract(cost);
            
            totalRevenue = totalRevenue.add(revenue);
            totalCost = totalCost.add(cost);
            totalProfit = totalProfit.add(profit);
            
            detail.put("activityId", activity.get("id"));
            detail.put("activityName", activity.get("activity_name"));
            detail.put("clientCompany", activity.get("client_company"));
            detail.put("revenue", revenue);
            detail.put("cost", cost);
            detail.put("profit", profit);
            
            revenueDetails.add(detail);
        }
        
        revenueData.put("period", datePeriod);
        revenueData.put("totalRevenue", totalRevenue);
        revenueData.put("totalCost", totalCost);
        revenueData.put("totalProfit", totalProfit);
        revenueData.put("revenueDetails", revenueDetails);
        
        return revenueData;
    }

    /**
     * 收集薪酬支出数据
     */
    private JSONObject collectPayrollExpenseData(String salaryPeriod) throws Exception {
        JSONObject payrollData = new JSONObject();
        
        // 查询薪酬核算数据
        List<Map<String, Object>> salaryCalculations = salaryCalculationService.getSalaryByPeriod(salaryPeriod);
        
        BigDecimal totalBaseSalary = BigDecimal.ZERO;
        BigDecimal totalProductionSalary = BigDecimal.ZERO;
        BigDecimal totalTeambuildingCommission = BigDecimal.ZERO;
        BigDecimal totalSocialInsurance = BigDecimal.ZERO;
        BigDecimal totalNetSalary = BigDecimal.ZERO;
        
        JSONArray payrollDetails = new JSONArray();
        
        for (Map<String, Object> salary : salaryCalculations) {
            JSONObject detail = new JSONObject();
            
            BigDecimal baseSalary = (BigDecimal) salary.get("base_salary");
            BigDecimal productionSalary = (BigDecimal) salary.get("production_salary");
            BigDecimal teambuildingCommission = (BigDecimal) salary.get("teambuilding_commission");
            BigDecimal socialInsurance = (BigDecimal) salary.get("social_insurance");
            BigDecimal netSalary = (BigDecimal) salary.get("net_salary");
            
            totalBaseSalary = totalBaseSalary.add(baseSalary);
            totalProductionSalary = totalProductionSalary.add(productionSalary);
            totalTeambuildingCommission = totalTeambuildingCommission.add(teambuildingCommission);
            totalSocialInsurance = totalSocialInsurance.add(socialInsurance);
            totalNetSalary = totalNetSalary.add(netSalary);
            
            detail.put("employeeId", salary.get("employee_id"));
            detail.put("employeeName", salary.get("employee_name"));
            detail.put("departmentName", salary.get("department_name"));
            detail.put("baseSalary", baseSalary);
            detail.put("productionSalary", productionSalary);
            detail.put("teambuildingCommission", teambuildingCommission);
            detail.put("socialInsurance", socialInsurance);
            detail.put("netSalary", netSalary);
            
            payrollDetails.add(detail);
        }
        
        BigDecimal totalExpense = totalBaseSalary.add(totalProductionSalary).add(totalTeambuildingCommission);
        
        payrollData.put("period", salaryPeriod);
        payrollData.put("totalBaseSalary", totalBaseSalary);
        payrollData.put("totalProductionSalary", totalProductionSalary);
        payrollData.put("totalTeambuildingCommission", totalTeambuildingCommission);
        payrollData.put("totalSocialInsurance", totalSocialInsurance);
        payrollData.put("totalExpense", totalExpense);
        payrollData.put("totalNetSalary", totalNetSalary);
        payrollData.put("payrollDetails", payrollDetails);
        
        return payrollData;
    }

    /**
     * 生成生产成本会计分录
     */
    private void generateProductionCostEntries(Long accountHeadId, JSONObject costData, HttpServletRequest request) throws Exception {
        BigDecimal totalMaterialCost = costData.getBigDecimal("totalMaterialCost");
        BigDecimal totalLaborCost = costData.getBigDecimal("totalLaborCost");
        BigDecimal totalOverheadCost = costData.getBigDecimal("totalOverheadCost");
        
        // 1. 借：生产成本-直接材料
        if (totalMaterialCost.compareTo(BigDecimal.ZERO) > 0) {
            createAccountItem(accountHeadId, "1405001", "生产成本-直接材料", 
                totalMaterialCost, BigDecimal.ZERO, "借", request);
        }
        
        // 2. 借：生产成本-直接人工
        if (totalLaborCost.compareTo(BigDecimal.ZERO) > 0) {
            createAccountItem(accountHeadId, "1405002", "生产成本-直接人工", 
                totalLaborCost, BigDecimal.ZERO, "借", request);
        }
        
        // 3. 借：生产成本-制造费用
        if (totalOverheadCost.compareTo(BigDecimal.ZERO) > 0) {
            createAccountItem(accountHeadId, "1405003", "生产成本-制造费用", 
                totalOverheadCost, BigDecimal.ZERO, "借", request);
        }
        
        BigDecimal totalCost = totalMaterialCost.add(totalLaborCost).add(totalOverheadCost);
        
        // 4. 贷：原材料 / 应付职工薪酬 / 制造费用
        createAccountItem(accountHeadId, "1402", "原材料", 
            BigDecimal.ZERO, totalMaterialCost, "贷", request);
        createAccountItem(accountHeadId, "2211", "应付职工薪酬", 
            BigDecimal.ZERO, totalLaborCost, "贷", request);
        createAccountItem(accountHeadId, "1405003", "制造费用", 
            BigDecimal.ZERO, totalOverheadCost, "贷", request);
    }

    /**
     * 生成收入会计分录
     */
    private void generateRevenueEntries(Long accountHeadId, JSONObject revenueData, HttpServletRequest request) throws Exception {
        BigDecimal totalRevenue = revenueData.getBigDecimal("totalRevenue");
        BigDecimal totalCost = revenueData.getBigDecimal("totalCost");
        
        // 1. 借：银行存款/应收账款
        createAccountItem(accountHeadId, "1002", "银行存款", 
            totalRevenue, BigDecimal.ZERO, "借", request);
        
        // 2. 贷：主营业务收入
        createAccountItem(accountHeadId, "6001", "主营业务收入", 
            BigDecimal.ZERO, totalRevenue, "贷", request);
        
        // 3. 借：主营业务成本
        if (totalCost.compareTo(BigDecimal.ZERO) > 0) {
            createAccountItem(accountHeadId, "6401", "主营业务成本", 
                totalCost, BigDecimal.ZERO, "借", request);
            
            // 4. 贷：库存商品/生产成本
            createAccountItem(accountHeadId, "1405", "生产成本", 
                BigDecimal.ZERO, totalCost, "贷", request);
        }
    }

    /**
     * 生成薪酬支出会计分录
     */
    private void generatePayrollEntries(Long accountHeadId, JSONObject payrollData, HttpServletRequest request) throws Exception {
        BigDecimal totalBaseSalary = payrollData.getBigDecimal("totalBaseSalary");
        BigDecimal totalProductionSalary = payrollData.getBigDecimal("totalProductionSalary");
        BigDecimal totalTeambuildingCommission = payrollData.getBigDecimal("totalTeambuildingCommission");
        BigDecimal totalSocialInsurance = payrollData.getBigDecimal("totalSocialInsurance");
        BigDecimal totalNetSalary = payrollData.getBigDecimal("totalNetSalary");
        
        // 1. 借：管理费用-工资
        if (totalBaseSalary.compareTo(BigDecimal.ZERO) > 0) {
            createAccountItem(accountHeadId, "6602001", "管理费用-工资", 
                totalBaseSalary, BigDecimal.ZERO, "借", request);
        }
        
        // 2. 借：生产成本-直接人工
        if (totalProductionSalary.compareTo(BigDecimal.ZERO) > 0) {
            createAccountItem(accountHeadId, "1405002", "生产成本-直接人工", 
                totalProductionSalary, BigDecimal.ZERO, "借", request);
        }
        
        // 3. 借：销售费用-提成
        if (totalTeambuildingCommission.compareTo(BigDecimal.ZERO) > 0) {
            createAccountItem(accountHeadId, "6601005", "销售费用-提成", 
                totalTeambuildingCommission, BigDecimal.ZERO, "借", request);
        }
        
        // 4. 借：管理费用-社会保险费
        if (totalSocialInsurance.compareTo(BigDecimal.ZERO) > 0) {
            createAccountItem(accountHeadId, "6602003", "管理费用-社会保险费", 
                totalSocialInsurance, BigDecimal.ZERO, "借", request);
        }
        
        BigDecimal totalExpense = totalBaseSalary.add(totalProductionSalary)
            .add(totalTeambuildingCommission).add(totalSocialInsurance);
        
        // 5. 贷：应付职工薪酬
        createAccountItem(accountHeadId, "2211", "应付职工薪酬", 
            BigDecimal.ZERO, totalExpense, "贷", request);
        
        // 6. 发放工资时：借：应付职工薪酬 贷：银行存款
        createAccountItem(accountHeadId, "2211", "应付职工薪酬", 
            totalNetSalary, BigDecimal.ZERO, "借", request);
        createAccountItem(accountHeadId, "1002", "银行存款", 
            BigDecimal.ZERO, totalNetSalary, "贷", request);
    }

    /**
     * 生成材料采购会计分录
     */
    private void generateMaterialPurchaseEntries(Long accountHeadId, JSONObject purchaseData, HttpServletRequest request) throws Exception {
        BigDecimal totalAmount = purchaseData.getBigDecimal("totalAmount");
        BigDecimal taxAmount = purchaseData.getBigDecimal("taxAmount");
        BigDecimal netAmount = totalAmount.subtract(taxAmount);
        
        // 1. 借：原材料
        createAccountItem(accountHeadId, "1402", "原材料", 
            netAmount, BigDecimal.ZERO, "借", request);
        
        // 2. 借：应交税费-应交增值税(进项税额)
        if (taxAmount.compareTo(BigDecimal.ZERO) > 0) {
            createAccountItem(accountHeadId, "2221001", "应交税费-应交增值税(进项税额)", 
                taxAmount, BigDecimal.ZERO, "借", request);
        }
        
        // 3. 贷：应付账款/银行存款
        String paymentMethod = purchaseData.getString("paymentMethod");
        if ("CASH".equals(paymentMethod)) {
            createAccountItem(accountHeadId, "1002", "银行存款", 
                BigDecimal.ZERO, totalAmount, "贷", request);
        } else {
            createAccountItem(accountHeadId, "2202", "应付账款", 
                BigDecimal.ZERO, totalAmount, "贷", request);
        }
    }

    /**
     * 创建财务凭证头
     */
    private AccountHead createAccountHead(String billType, String period, HttpServletRequest request) throws Exception {
        AccountHead accountHead = new AccountHead();
        
        // 生成凭证号
        String billNo = generateAccountBillNo(period);
        accountHead.setBillNo(billNo);
        accountHead.setType("财务凭证");
        accountHead.setSubType(billType);
        accountHead.setBillTime(new Date());
        accountHead.setRemark(billType + " - " + period);
        
        // 设置标准字段
        accountHead.setTenantId(getCurrentTenantId());
        accountHead.setDeleteFlag("0");
        accountHead.setCreateTime(new Date());
        accountHead.setCreateUser(getCurrentUserId(request));
        
        int result = accountHeadMapper.insertSelective(accountHead);
        if (result <= 0) {
            throw new BusinessRunTimeException("创建财务凭证头失败");
        }
        
        return accountHead;
    }

    /**
     * 创建会计分录明细
     */
    private void createAccountItem(Long accountHeadId, String accountNumber, String accountName, 
                                   BigDecimal debitAmount, BigDecimal creditAmount, String direction, 
                                   HttpServletRequest request) throws Exception {
        AccountItem accountItem = new AccountItem();
        
        accountItem.setHeaderId(accountHeadId);
        accountItem.setAccountId(getAccountIdByNumber(accountNumber));
        accountItem.setInOutItemId(null);
        accountItem.setTaxRateId(null);
        accountItem.setDebitAmount(debitAmount);
        accountItem.setCreditAmount(creditAmount);
        accountItem.setRemark(accountName + " - " + direction);
        
        // 设置标准字段
        accountItem.setTenantId(getCurrentTenantId());
        accountItem.setDeleteFlag("0");
        accountItem.setCreateTime(new Date());
        accountItem.setCreateUser(getCurrentUserId(request));
        
        int result = accountItemMapper.insertSelective(accountItem);
        if (result <= 0) {
            throw new BusinessRunTimeException("创建会计分录明细失败");
        }
    }

    /**
     * 更新财务科目余额
     */
    private void updateAccountBalances(JSONObject financialData) throws Exception {
        // 实现科目余额更新逻辑
        // 根据会计分录更新各科目的借方余额和贷方余额
        logger.debug("更新财务科目余额完成");
    }

    /**
     * 验证会计分录平衡
     */
    private void validateAccountEntryBalance(Long accountHeadId) throws Exception {
        // 查询该凭证下所有分录的借贷金额
        BigDecimal totalDebit = accountItemMapper.sumDebitByHeaderId(accountHeadId);
        BigDecimal totalCredit = accountItemMapper.sumCreditByHeaderId(accountHeadId);
        
        if (totalDebit.compareTo(totalCredit) != 0) {
            throw new BusinessRunTimeException("会计分录借贷不平衡: 借方=" + totalDebit + ", 贷方=" + totalCredit);
        }
        
        logger.debug("会计分录借贷平衡验证通过: 借方={}, 贷方={}", totalDebit, totalCredit);
    }

    /**
     * 验证业务数据
     */
    private void validateBusinessData(JSONObject businessData, String entryType) throws Exception {
        if (businessData == null) {
            throw new BusinessRunTimeException("业务数据不能为空");
        }
        
        if (businessData.getString("period") == null) {
            throw new BusinessRunTimeException("期间不能为空");
        }
        
        // 根据分录类型进行特定验证
        // 实现略...
    }

    /**
     * 根据科目编号获取科目ID
     */
    private Long getAccountIdByNumber(String accountNumber) throws Exception {
        Account account = accountMapper.findByNumber(accountNumber);
        if (account == null) {
            throw new BusinessRunTimeException("科目不存在: " + accountNumber);
        }
        return account.getId();
    }

    /**
     * 生成财务凭证号
     */
    private String generateAccountBillNo(String period) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String dateStr = sdf.format(new Date());
        String prefix = "AC" + dateStr;
        
        // 查询当日最大序号
        String maxNo = accountHeadMapper.findMaxBillNoByPrefix(prefix);
        
        int sequence = 1;
        if (maxNo != null && maxNo.length() > prefix.length()) {
            String seqStr = maxNo.substring(prefix.length());
            try {
                sequence = Integer.parseInt(seqStr) + 1;
            } catch (NumberFormatException e) {
                sequence = 1;
            }
        }
        
        return prefix + String.format("%04d", sequence);
    }

    /**
     * 获取当前租户ID
     */
    private Long getCurrentTenantId() {
        return 1L; // 临时实现
    }

    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId(HttpServletRequest request) {
        return 1L; // 临时实现
    }
}
```

### 2. CostCalculationService.java - 成本核算服务

**文件路径**: `com.jsh.erp.finance.service.CostCalculationService`

```java
package com.jsh.erp.finance.service;

import com.alibaba.fastjson.JSONObject;
import com.jsh.erp.exception.BusinessRunTimeException;
import com.jsh.erp.production.datasource.mappers.ProductionOrderMapperEx;
import com.jsh.erp.production.datasource.mappers.ProductionMaterialMapperEx;
import com.jsh.erp.salary.datasource.mappers.SalaryCalculationMapperEx;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;

/**
 * 成本核算服务
 * 提供生产成本、人工成本、制造费用的核算功能
 */
@Service
public class CostCalculationService {
    private Logger logger = LoggerFactory.getLogger(CostCalculationService.class);

    @Resource
    private ProductionOrderMapperEx productionOrderMapperEx;
    @Resource
    private ProductionMaterialMapperEx productionMaterialMapperEx;
    @Resource
    private SalaryCalculationMapperEx salaryCalculationMapperEx;

    /**
     * 计算材料成本
     * 基于生产工单的材料消耗计算材料成本
     */
    public BigDecimal calculateMaterialCost(Long productionOrderId) throws Exception {
        try {
            // 查询生产工单的材料消耗
            List<Map<String, Object>> materials = productionMaterialMapperEx.findCostByProductionOrder(productionOrderId);
            
            BigDecimal totalMaterialCost = BigDecimal.ZERO;
            
            for (Map<String, Object> material : materials) {
                BigDecimal quantity = (BigDecimal) material.get("actual_quantity");
                BigDecimal unitPrice = (BigDecimal) material.get("unit_price");
                
                if (quantity != null && unitPrice != null) {
                    BigDecimal materialCost = quantity.multiply(unitPrice);
                    totalMaterialCost = totalMaterialCost.add(materialCost);
                }
            }
            
            logger.debug("材料成本计算完成: 工单ID={}, 材料成本={}", productionOrderId, totalMaterialCost);
            return totalMaterialCost;
            
        } catch (Exception e) {
            logger.error("计算材料成本失败", e);
            throw new BusinessRunTimeException("计算材料成本失败: " + e.getMessage());
        }
    }

    /**
     * 计算人工成本
     * 基于工时和工资标准计算人工成本
     */
    public BigDecimal calculateLaborCost(Long productionOrderId) throws Exception {
        try {
            // 查询生产工单的人工数据
            Map<String, Object> laborData = productionOrderMapperEx.findLaborCostByOrderId(productionOrderId);
            
            if (laborData == null) {
                return BigDecimal.ZERO;
            }
            
            BigDecimal totalWorkHours = (BigDecimal) laborData.get("total_work_hours");
            BigDecimal averageHourlyRate = (BigDecimal) laborData.get("average_hourly_rate");
            
            BigDecimal laborCost = BigDecimal.ZERO;
            if (totalWorkHours != null && averageHourlyRate != null) {
                laborCost = totalWorkHours.multiply(averageHourlyRate);
            }
            
            logger.debug("人工成本计算完成: 工单ID={}, 总工时={}, 平均时薪={}, 人工成本={}", 
                productionOrderId, totalWorkHours, averageHourlyRate, laborCost);
            
            return laborCost;
            
        } catch (Exception e) {
            logger.error("计算人工成本失败", e);
            throw new BusinessRunTimeException("计算人工成本失败: " + e.getMessage());
        }
    }

    /**
     * 计算制造费用
     * 基于生产量和费用标准计算制造费用
     */
    public BigDecimal calculateOverheadCost(Long productionOrderId) throws Exception {
        try {
            // 查询生产工单基本信息
            Map<String, Object> orderInfo = productionOrderMapperEx.findOrderInfoById(productionOrderId);
            
            if (orderInfo == null) {
                return BigDecimal.ZERO;
            }
            
            BigDecimal quantity = (BigDecimal) orderInfo.get("quantity");
            String productType = (String) orderInfo.get("product_type");
            
            // 根据产品类型获取制造费用分摊率
            BigDecimal overheadRate = getOverheadRateByProductType(productType);
            
            BigDecimal overheadCost = BigDecimal.ZERO;
            if (quantity != null && overheadRate != null) {
                overheadCost = quantity.multiply(overheadRate);
            }
            
            logger.debug("制造费用计算完成: 工单ID={}, 数量={}, 费用率={}, 制造费用={}", 
                productionOrderId, quantity, overheadRate, overheadCost);
            
            return overheadCost;
            
        } catch (Exception e) {
            logger.error("计算制造费用失败", e);
            throw new BusinessRunTimeException("计算制造费用失败: " + e.getMessage());
        }
    }

    /**
     * 更新产品成本
     * 更新产品的标准成本和实际成本
     */
    public void updateProductCost(Long productionOrderId) throws Exception {
        try {
            // 1. 计算各项成本
            BigDecimal materialCost = calculateMaterialCost(productionOrderId);
            BigDecimal laborCost = calculateLaborCost(productionOrderId);
            BigDecimal overheadCost = calculateOverheadCost(productionOrderId);
            
            // 2. 计算总成本
            BigDecimal totalCost = materialCost.add(laborCost).add(overheadCost);
            
            // 3. 获取生产数量
            Map<String, Object> orderInfo = productionOrderMapperEx.findOrderInfoById(productionOrderId);
            BigDecimal quantity = (BigDecimal) orderInfo.get("quantity");
            
            // 4. 计算单位成本
            BigDecimal unitCost = BigDecimal.ZERO;
            if (quantity != null && quantity.compareTo(BigDecimal.ZERO) > 0) {
                unitCost = totalCost.divide(quantity, 4, RoundingMode.HALF_UP);
            }
            
            // 5. 更新生产工单成本信息
            JSONObject costUpdate = new JSONObject();
            costUpdate.put("id", productionOrderId);
            costUpdate.put("materialCost", materialCost);
            costUpdate.put("laborCost", laborCost);
            costUpdate.put("overheadCost", overheadCost);
            costUpdate.put("totalCost", totalCost);
            costUpdate.put("unitCost", unitCost);
            
            productionOrderMapperEx.updateCostInfo(costUpdate);
            
            logger.info("产品成本更新完成: 工单ID={}, 总成本={}, 单位成本={}", 
                productionOrderId, totalCost, unitCost);
            
        } catch (Exception e) {
            logger.error("更新产品成本失败", e);
            throw new BusinessRunTimeException("更新产品成本失败: " + e.getMessage());
        }
    }

    /**
     * 批量更新期间内所有产品成本
     */
    public void batchUpdateProductCosts(String period) throws Exception {
        try {
            // 查询期间内的所有生产工单
            List<Long> productionOrderIds = productionOrderMapperEx.findOrderIdsByPeriod(period);
            
            int updateCount = 0;
            for (Long orderId : productionOrderIds) {
                try {
                    updateProductCost(orderId);
                    updateCount++;
                } catch (Exception e) {
                    logger.warn("更新工单成本失败: orderId={}, error={}", orderId, e.getMessage());
                }
            }
            
            logger.info("批量更新产品成本完成: 期间={}, 总数={}, 成功数={}", 
                period, productionOrderIds.size(), updateCount);
            
        } catch (Exception e) {
            logger.error("批量更新产品成本失败", e);
            throw new BusinessRunTimeException("批量更新产品成本失败: " + e.getMessage());
        }
    }

    /**
     * 根据产品类型获取制造费用分摊率
     */
    private BigDecimal getOverheadRateByProductType(String productType) {
        // 这里可以从配置表获取，或者使用固定费率
        switch (productType) {
            case "丝巾":
                return new BigDecimal("15.00");
            case "围巾":
                return new BigDecimal("12.00");
            case "披肩":
                return new BigDecimal("20.00");
            default:
                return new BigDecimal("10.00");
        }
    }
}
```

---

## 报表生成服务开发 (2天)

### 1. ReportGenerationService.java - 报表生成服务

**文件路径**: `com.jsh.erp.finance.service.ReportGenerationService`

```java
package com.jsh.erp.finance.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jsh.erp.exception.BusinessRunTimeException;
import com.jsh.erp.datasource.mappers.AccountItemMapperEx;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 报表生成服务
 * 提供财务报表的生成和分析功能
 */
@Service
public class ReportGenerationService {
    private Logger logger = LoggerFactory.getLogger(ReportGenerationService.class);

    @Resource
    private AccountItemMapperEx accountItemMapperEx;
    @Resource
    private FinanceIntegrationService financeIntegrationService;

    /**
     * 生成损益报表
     * 生成指定期间的利润表
     */
    public JSONObject generateProfitLossReport(String startPeriod, String endPeriod) throws Exception {
        try {
            logger.info("生成损益报表: {} - {}", startPeriod, endPeriod);
            
            JSONObject report = new JSONObject();
            report.put("reportType", "损益报表");
            report.put("startPeriod", startPeriod);
            report.put("endPeriod", endPeriod);
            report.put("generateTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            
            // 1. 营业收入
            JSONObject revenueData = calculateRevenue(startPeriod, endPeriod);
            report.put("revenue", revenueData);
            
            // 2. 营业成本
            JSONObject costData = calculateOperatingCost(startPeriod, endPeriod);
            report.put("operatingCost", costData);
            
            // 3. 毛利润
            BigDecimal totalRevenue = revenueData.getBigDecimal("totalAmount");
            BigDecimal totalCost = costData.getBigDecimal("totalAmount");
            BigDecimal grossProfit = totalRevenue.subtract(totalCost);
            BigDecimal grossMargin = BigDecimal.ZERO;
            if (totalRevenue.compareTo(BigDecimal.ZERO) > 0) {
                grossMargin = grossProfit.divide(totalRevenue, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
            }
            
            JSONObject grossProfitData = new JSONObject();
            grossProfitData.put("amount", grossProfit);
            grossProfitData.put("margin", grossMargin);
            report.put("grossProfit", grossProfitData);
            
            // 4. 期间费用
            JSONObject expenseData = calculatePeriodExpenses(startPeriod, endPeriod);
            report.put("periodExpenses", expenseData);
            
            // 5. 净利润
            BigDecimal totalExpenses = expenseData.getBigDecimal("totalAmount");
            BigDecimal netProfit = grossProfit.subtract(totalExpenses);
            BigDecimal netMargin = BigDecimal.ZERO;
            if (totalRevenue.compareTo(BigDecimal.ZERO) > 0) {
                netMargin = netProfit.divide(totalRevenue, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
            }
            
            JSONObject netProfitData = new JSONObject();
            netProfitData.put("amount", netProfit);
            netProfitData.put("margin", netMargin);
            report.put("netProfit", netProfitData);
            
            // 6. 财务指标
            JSONObject indicators = calculateFinancialIndicators(report);
            report.put("indicators", indicators);
            
            logger.info("损益报表生成完成: 营业收入={}, 净利润={}, 净利率={}%", 
                totalRevenue, netProfit, netMargin);
            
            return report;
            
        } catch (Exception e) {
            logger.error("生成损益报表失败", e);
            throw new BusinessRunTimeException("生成损益报表失败: " + e.getMessage());
        }
    }

    /**
     * 生成成本分析报表
     * 分析生产成本结构和变化趋势
     */
    public JSONObject generateCostAnalysisReport(String period) throws Exception {
        try {
            logger.info("生成成本分析报表: {}", period);
            
            JSONObject report = new JSONObject();
            report.put("reportType", "成本分析报表");
            report.put("period", period);
            report.put("generateTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            
            // 1. 成本构成分析
            JSONObject costComposition = analyzeCostComposition(period);
            report.put("costComposition", costComposition);
            
            // 2. 产品成本分析
            JSONArray productCostAnalysis = analyzeProductCosts(period);
            report.put("productCostAnalysis", productCostAnalysis);
            
            // 3. 成本趋势分析
            JSONArray costTrends = analyzeCostTrends(period, 6); // 最近6个月
            report.put("costTrends", costTrends);
            
            // 4. 成本控制建议
            JSONArray recommendations = generateCostControlRecommendations(costComposition);
            report.put("recommendations", recommendations);
            
            logger.info("成本分析报表生成完成");
            return report;
            
        } catch (Exception e) {
            logger.error("生成成本分析报表失败", e);
            throw new BusinessRunTimeException("生成成本分析报表失败: " + e.getMessage());
        }
    }

    /**
     * 生成现金流报表
     * 分析现金流入流出情况
     */
    public JSONObject generateCashFlowReport(String startPeriod, String endPeriod) throws Exception {
        try {
            logger.info("生成现金流报表: {} - {}", startPeriod, endPeriod);
            
            JSONObject report = new JSONObject();
            report.put("reportType", "现金流报表");
            report.put("startPeriod", startPeriod);
            report.put("endPeriod", endPeriod);
            report.put("generateTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            
            // 1. 经营活动现金流
            JSONObject operatingCashFlow = calculateOperatingCashFlow(startPeriod, endPeriod);
            report.put("operatingCashFlow", operatingCashFlow);
            
            // 2. 投资活动现金流
            JSONObject investingCashFlow = calculateInvestingCashFlow(startPeriod, endPeriod);
            report.put("investingCashFlow", investingCashFlow);
            
            // 3. 筹资活动现金流
            JSONObject financingCashFlow = calculateFinancingCashFlow(startPeriod, endPeriod);
            report.put("financingCashFlow", financingCashFlow);
            
            // 4. 现金净流量
            BigDecimal netCashFlow = operatingCashFlow.getBigDecimal("netAmount")
                .add(investingCashFlow.getBigDecimal("netAmount"))
                .add(financingCashFlow.getBigDecimal("netAmount"));
            
            report.put("netCashFlow", netCashFlow);
            
            // 5. 现金流分析
            JSONObject cashFlowAnalysis = analyzeCashFlow(report);
            report.put("analysis", cashFlowAnalysis);
            
            logger.info("现金流报表生成完成: 净现金流={}", netCashFlow);
            return report;
            
        } catch (Exception e) {
            logger.error("生成现金流报表失败", e);
            throw new BusinessRunTimeException("生成现金流报表失败: " + e.getMessage());
        }
    }

    /**
     * 计算营业收入
     */
    private JSONObject calculateRevenue(String startPeriod, String endPeriod) throws Exception {
        JSONObject revenueData = new JSONObject();
        
        // 查询主营业务收入 (科目6001)
        BigDecimal mainRevenue = accountItemMapperEx.sumCreditByAccountAndPeriod("6001", startPeriod, endPeriod);
        
        // 查询其他业务收入 (科目6051)
        BigDecimal otherRevenue = accountItemMapperEx.sumCreditByAccountAndPeriod("6051", startPeriod, endPeriod);
        
        BigDecimal totalRevenue = mainRevenue.add(otherRevenue);
        
        revenueData.put("mainRevenue", mainRevenue);
        revenueData.put("otherRevenue", otherRevenue);
        revenueData.put("totalAmount", totalRevenue);
        
        // 收入结构分析
        JSONArray revenueBreakdown = new JSONArray();
        if (totalRevenue.compareTo(BigDecimal.ZERO) > 0) {
            JSONObject mainItem = new JSONObject();
            mainItem.put("type", "主营业务收入");
            mainItem.put("amount", mainRevenue);
            mainItem.put("percentage", mainRevenue.divide(totalRevenue, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")));
            revenueBreakdown.add(mainItem);
            
            JSONObject otherItem = new JSONObject();
            otherItem.put("type", "其他业务收入");
            otherItem.put("amount", otherRevenue);
            otherItem.put("percentage", otherRevenue.divide(totalRevenue, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")));
            revenueBreakdown.add(otherItem);
        }
        
        revenueData.put("breakdown", revenueBreakdown);
        
        return revenueData;
    }

    /**
     * 计算营业成本
     */
    private JSONObject calculateOperatingCost(String startPeriod, String endPeriod) throws Exception {
        JSONObject costData = new JSONObject();
        
        // 查询主营业务成本 (科目6401)
        BigDecimal mainCost = accountItemMapperEx.sumDebitByAccountAndPeriod("6401", startPeriod, endPeriod);
        
        // 查询其他业务成本 (科目6402)
        BigDecimal otherCost = accountItemMapperEx.sumDebitByAccountAndPeriod("6402", startPeriod, endPeriod);
        
        BigDecimal totalCost = mainCost.add(otherCost);
        
        costData.put("mainCost", mainCost);
        costData.put("otherCost", otherCost);
        costData.put("totalAmount", totalCost);
        
        return costData;
    }

    /**
     * 计算期间费用
     */
    private JSONObject calculatePeriodExpenses(String startPeriod, String endPeriod) throws Exception {
        JSONObject expenseData = new JSONObject();
        
        // 销售费用 (科目6601)
        BigDecimal sellingExpenses = accountItemMapperEx.sumDebitByAccountAndPeriod("6601", startPeriod, endPeriod);
        
        // 管理费用 (科目6602)
        BigDecimal adminExpenses = accountItemMapperEx.sumDebitByAccountAndPeriod("6602", startPeriod, endPeriod);
        
        // 财务费用 (科目6603)
        BigDecimal financeExpenses = accountItemMapperEx.sumDebitByAccountAndPeriod("6603", startPeriod, endPeriod);
        
        BigDecimal totalExpenses = sellingExpenses.add(adminExpenses).add(financeExpenses);
        
        expenseData.put("sellingExpenses", sellingExpenses);
        expenseData.put("adminExpenses", adminExpenses);
        expenseData.put("financeExpenses", financeExpenses);
        expenseData.put("totalAmount", totalExpenses);
        
        return expenseData;
    }

    /**
     * 计算财务指标
     */
    private JSONObject calculateFinancialIndicators(JSONObject profitLossReport) throws Exception {
        JSONObject indicators = new JSONObject();
        
        BigDecimal totalRevenue = profitLossReport.getJSONObject("revenue").getBigDecimal("totalAmount");
        BigDecimal grossProfit = profitLossReport.getJSONObject("grossProfit").getBigDecimal("amount");
        BigDecimal netProfit = profitLossReport.getJSONObject("netProfit").getBigDecimal("amount");
        
        // 毛利率
        BigDecimal grossMargin = BigDecimal.ZERO;
        if (totalRevenue.compareTo(BigDecimal.ZERO) > 0) {
            grossMargin = grossProfit.divide(totalRevenue, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
        }
        indicators.put("grossMargin", grossMargin);
        
        // 净利率
        BigDecimal netMargin = BigDecimal.ZERO;
        if (totalRevenue.compareTo(BigDecimal.ZERO) > 0) {
            netMargin = netProfit.divide(totalRevenue, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
        }
        indicators.put("netMargin", netMargin);
        
        // 成本费用率
        BigDecimal totalCost = profitLossReport.getJSONObject("operatingCost").getBigDecimal("totalAmount");
        BigDecimal totalExpenses = profitLossReport.getJSONObject("periodExpenses").getBigDecimal("totalAmount");
        BigDecimal costExpenseRatio = BigDecimal.ZERO;
        if (totalRevenue.compareTo(BigDecimal.ZERO) > 0) {
            costExpenseRatio = totalCost.add(totalExpenses)
                .divide(totalRevenue, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
        }
        indicators.put("costExpenseRatio", costExpenseRatio);
        
        return indicators;
    }

    /**
     * 分析成本构成
     */
    private JSONObject analyzeCostComposition(String period) throws Exception {
        JSONObject composition = new JSONObject();
        
        // 查询各项成本
        BigDecimal materialCost = accountItemMapperEx.sumDebitByAccountAndPeriod("1402", period, period);
        BigDecimal laborCost = accountItemMapperEx.sumDebitByAccountAndPeriod("1405002", period, period);
        BigDecimal overheadCost = accountItemMapperEx.sumDebitByAccountAndPeriod("1405003", period, period);
        
        BigDecimal totalCost = materialCost.add(laborCost).add(overheadCost);
        
        composition.put("materialCost", materialCost);
        composition.put("laborCost", laborCost);
        composition.put("overheadCost", overheadCost);
        composition.put("totalCost", totalCost);
        
        // 成本结构比例
        JSONArray costStructure = new JSONArray();
        if (totalCost.compareTo(BigDecimal.ZERO) > 0) {
            JSONObject materialItem = new JSONObject();
            materialItem.put("type", "直接材料");
            materialItem.put("amount", materialCost);
            materialItem.put("percentage", materialCost.divide(totalCost, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")));
            costStructure.add(materialItem);
            
            JSONObject laborItem = new JSONObject();
            laborItem.put("type", "直接人工");
            laborItem.put("amount", laborCost);
            laborItem.put("percentage", laborCost.divide(totalCost, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")));
            costStructure.add(laborItem);
            
            JSONObject overheadItem = new JSONObject();
            overheadItem.put("type", "制造费用");
            overheadItem.put("amount", overheadCost);
            overheadItem.put("percentage", overheadCost.divide(totalCost, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")));
            costStructure.add(overheadItem);
        }
        
        composition.put("structure", costStructure);
        
        return composition;
    }

    /**
     * 分析产品成本
     */
    private JSONArray analyzeProductCosts(String period) throws Exception {
        // 查询各产品的成本数据
        List<Map<String, Object>> productCosts = accountItemMapperEx.findProductCostsByPeriod(period);
        
        JSONArray analysis = new JSONArray();
        
        for (Map<String, Object> product : productCosts) {
            JSONObject item = new JSONObject();
            item.put("productName", product.get("product_name"));
            item.put("quantity", product.get("quantity"));
            item.put("totalCost", product.get("total_cost"));
            item.put("unitCost", product.get("unit_cost"));
            item.put("materialCost", product.get("material_cost"));
            item.put("laborCost", product.get("labor_cost"));
            item.put("overheadCost", product.get("overhead_cost"));
            
            analysis.add(item);
        }
        
        return analysis;
    }

    /**
     * 分析成本趋势
     */
    private JSONArray analyzeCostTrends(String currentPeriod, int months) throws Exception {
        JSONArray trends = new JSONArray();
        
        // 生成过去几个月的期间列表
        // 实现略...
        
        return trends;
    }

    /**
     * 生成成本控制建议
     */
    private JSONArray generateCostControlRecommendations(JSONObject costComposition) throws Exception {
        JSONArray recommendations = new JSONArray();
        
        BigDecimal totalCost = costComposition.getBigDecimal("totalCost");
        BigDecimal materialCost = costComposition.getBigDecimal("materialCost");
        BigDecimal laborCost = costComposition.getBigDecimal("laborCost");
        
        if (totalCost.compareTo(BigDecimal.ZERO) > 0) {
            // 分析材料成本占比
            BigDecimal materialRatio = materialCost.divide(totalCost, 4, RoundingMode.HALF_UP);
            if (materialRatio.compareTo(new BigDecimal("0.6")) > 0) {
                JSONObject recommendation = new JSONObject();
                recommendation.put("type", "材料成本控制");
                recommendation.put("description", "材料成本占比过高，建议优化采购策略，寻找更优质的供应商");
                recommendation.put("priority", "高");
                recommendations.add(recommendation);
            }
            
            // 分析人工成本占比
            BigDecimal laborRatio = laborCost.divide(totalCost, 4, RoundingMode.HALF_UP);
            if (laborRatio.compareTo(new BigDecimal("0.3")) > 0) {
                JSONObject recommendation = new JSONObject();
                recommendation.put("type", "人工成本控制");
                recommendation.put("description", "人工成本占比较高，建议提升生产效率，优化工艺流程");
                recommendation.put("priority", "中");
                recommendations.add(recommendation);
            }
        }
        
        return recommendations;
    }

    /**
     * 计算经营活动现金流
     */
    private JSONObject calculateOperatingCashFlow(String startPeriod, String endPeriod) throws Exception {
        JSONObject operatingFlow = new JSONObject();
        
        // 销售商品收到的现金
        BigDecimal salesCash = accountItemMapperEx.sumDebitByAccountAndPeriod("1002", startPeriod, endPeriod);
        
        // 购买商品支付的现金
        BigDecimal purchaseCash = accountItemMapperEx.sumCreditByAccountAndPeriod("1002", startPeriod, endPeriod);
        
        // 支付给职工的现金
        BigDecimal payrollCash = accountItemMapperEx.sumDebitByAccountAndPeriod("2211", startPeriod, endPeriod);
        
        BigDecimal netOperatingFlow = salesCash.subtract(purchaseCash).subtract(payrollCash);
        
        operatingFlow.put("salesCash", salesCash);
        operatingFlow.put("purchaseCash", purchaseCash);
        operatingFlow.put("payrollCash", payrollCash);
        operatingFlow.put("netAmount", netOperatingFlow);
        
        return operatingFlow;
    }

    /**
     * 计算投资活动现金流
     */
    private JSONObject calculateInvestingCashFlow(String startPeriod, String endPeriod) throws Exception {
        JSONObject investingFlow = new JSONObject();
        
        // 暂时返回空数据，实际可以查询固定资产购置等投资活动
        investingFlow.put("netAmount", BigDecimal.ZERO);
        
        return investingFlow;
    }

    /**
     * 计算筹资活动现金流
     */
    private JSONObject calculateFinancingCashFlow(String startPeriod, String endPeriod) throws Exception {
        JSONObject financingFlow = new JSONObject();
        
        // 暂时返回空数据，实际可以查询借款、股权投资等筹资活动
        financingFlow.put("netAmount", BigDecimal.ZERO);
        
        return financingFlow;
    }

    /**
     * 分析现金流
     */
    private JSONObject analyzeCashFlow(JSONObject cashFlowReport) throws Exception {
        JSONObject analysis = new JSONObject();
        
        BigDecimal operatingFlow = cashFlowReport.getJSONObject("operatingCashFlow").getBigDecimal("netAmount");
        BigDecimal netCashFlow = cashFlowReport.getBigDecimal("netCashFlow");
        
        // 现金流健康度分析
        String healthStatus = "良好";
        if (operatingFlow.compareTo(BigDecimal.ZERO) < 0) {
            healthStatus = "需要关注";
        }
        if (netCashFlow.compareTo(BigDecimal.ZERO) < 0) {
            healthStatus = "风险";
        }
        
        analysis.put("healthStatus", healthStatus);
        analysis.put("operatingFlowRatio", operatingFlow.divide(netCashFlow.abs(), 4, RoundingMode.HALF_UP));
        
        return analysis;
    }
}
```

---

## 前端报表界面开发 (1天)

### 1. FinanceReportList.vue - 财务报表列表页面

**文件路径**: `jshERP-web/src/views/finance/FinanceReportList.vue`

```vue
<template>
  <a-card :bordered="false">
    <!-- 报表选择区域 -->
    <div class="report-selector">
      <a-row :gutter="16">
        <a-col :span="8">
          <a-card size="small" class="report-card" @click="generateReport('profitLoss')">
            <div class="report-icon">
              <a-icon type="line-chart" style="font-size: 32px; color: #1890ff;" />
            </div>
            <div class="report-info">
              <h3>损益报表</h3>
              <p>分析收入、成本、利润情况</p>
            </div>
          </a-card>
        </a-col>
        
        <a-col :span="8">
          <a-card size="small" class="report-card" @click="generateReport('costAnalysis')">
            <div class="report-icon">
              <a-icon type="pie-chart" style="font-size: 32px; color: #52c41a;" />
            </div>
            <div class="report-info">
              <h3>成本分析报表</h3>
              <p>分析成本构成和控制要点</p>
            </div>
          </a-card>
        </a-col>
        
        <a-col :span="8">
          <a-card size="small" class="report-card" @click="generateReport('cashFlow')">
            <div class="report-icon">
              <a-icon type="fund" style="font-size: 32px; color: #faad14;" />
            </div>
            <div class="report-info">
              <h3>现金流报表</h3>
              <p>分析现金流入流出情况</p>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 报表参数设置 -->
    <a-card title="报表参数" style="margin-top: 16px;">
      <a-form layout="inline">
        <a-form-item label="报表类型">
          <a-select v-model="reportParams.reportType" style="width: 200px;">
            <a-select-option value="profitLoss">损益报表</a-select-option>
            <a-select-option value="costAnalysis">成本分析报表</a-select-option>
            <a-select-option value="cashFlow">现金流报表</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="期间范围">
          <a-range-picker 
            v-model="reportParams.dateRange"
            format="YYYY-MM"
            mode="month"
            placeholder="['开始月份', '结束月份']" />
        </a-form-item>
        
        <a-form-item>
          <a-button type="primary" icon="file-text" @click="handleGenerateReport">生成报表</a-button>
          <a-button style="margin-left: 8px;" icon="download" @click="handleExportReport">导出报表</a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 报表显示区域 -->
    <a-card title="报表内容" style="margin-top: 16px;" v-if="reportData">
      <div class="report-header">
        <h2>{{ reportData.reportType }}</h2>
        <p>报表期间: {{ reportData.startPeriod }} - {{ reportData.endPeriod }}</p>
        <p>生成时间: {{ reportData.generateTime }}</p>
      </div>

      <!-- 损益报表内容 -->
      <div v-if="reportData.reportType === '损益报表'">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-card title="收入情况" size="small">
              <a-statistic
                title="营业收入总额"
                :value="reportData.revenue.totalAmount"
                :precision="2"
                suffix="元" />
              
              <div style="margin-top: 16px;">
                <a-progress 
                  :percent="getRevenuePercentage('mainRevenue')"
                  :stroke-color="'#52c41a'"
                  :format="() => `主营业务收入: ${reportData.revenue.mainRevenue}元`" />
                <a-progress 
                  :percent="getRevenuePercentage('otherRevenue')"
                  :stroke-color="'#1890ff'"
                  :format="() => `其他业务收入: ${reportData.revenue.otherRevenue}元`" />
              </div>
            </a-card>
          </a-col>
          
          <a-col :span="12">
            <a-card title="利润情况" size="small">
              <a-statistic
                title="净利润"
                :value="reportData.netProfit.amount"
                :precision="2"
                suffix="元"
                :value-style="{ color: reportData.netProfit.amount >= 0 ? '#3f8600' : '#cf1322' }" />
              
              <div style="margin-top: 16px;">
                <p>毛利率: {{ reportData.grossProfit.margin }}%</p>
                <p>净利率: {{ reportData.netProfit.margin }}%</p>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 成本分析报表内容 -->
      <div v-if="reportData.reportType === '成本分析报表'">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-card title="成本构成" size="small">
              <div ref="costCompositionChart" style="height: 300px;"></div>
            </a-card>
          </a-col>
          
          <a-col :span="12">
            <a-card title="成本控制建议" size="small">
              <a-list
                :data-source="reportData.recommendations"
                size="small">
                <a-list-item slot="renderItem" slot-scope="item">
                  <a-list-item-meta>
                    <a slot="title">{{ item.type }}</a>
                    <template slot="description">
                      {{ item.description }}
                    </template>
                  </a-list-item-meta>
                  <a-tag :color="getPriorityColor(item.priority)">{{ item.priority }}</a-tag>
                </a-list-item>
              </a-list>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 现金流报表内容 -->
      <div v-if="reportData.reportType === '现金流报表'">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-card title="经营活动现金流" size="small">
              <a-statistic
                title="净现金流"
                :value="reportData.operatingCashFlow.netAmount"
                :precision="2"
                suffix="元"
                :value-style="{ color: reportData.operatingCashFlow.netAmount >= 0 ? '#3f8600' : '#cf1322' }" />
            </a-card>
          </a-col>
          
          <a-col :span="8">
            <a-card title="投资活动现金流" size="small">
              <a-statistic
                title="净现金流"
                :value="reportData.investingCashFlow.netAmount"
                :precision="2"
                suffix="元"
                :value-style="{ color: reportData.investingCashFlow.netAmount >= 0 ? '#3f8600' : '#cf1322' }" />
            </a-card>
          </a-col>
          
          <a-col :span="8">
            <a-card title="筹资活动现金流" size="small">
              <a-statistic
                title="净现金流"
                :value="reportData.financingCashFlow.netAmount"
                :precision="2"
                suffix="元"
                :value-style="{ color: reportData.financingCashFlow.netAmount >= 0 ? '#3f8600' : '#cf1322' }" />
            </a-card>
          </a-col>
        </a-row>
        
        <a-row style="margin-top: 16px;">
          <a-col :span="24">
            <a-card title="现金流健康度分析" size="small">
              <a-alert
                :message="`现金流健康状态: ${reportData.analysis.healthStatus}`"
                :type="getHealthAlertType(reportData.analysis.healthStatus)"
                show-icon />
            </a-card>
          </a-col>
        </a-row>
      </div>
    </a-card>

    <!-- 报表查看器 -->
    <report-viewer 
      ref="reportViewer"
      @print="handlePrintReport">
    </report-viewer>
  </a-card>
</template>

<script>
import ReportViewer from './modules/ReportViewer'
import * as echarts from 'echarts'

export default {
  name: 'FinanceReportList',
  components: {
    ReportViewer
  },
  data() {
    return {
      reportParams: {
        reportType: 'profitLoss',
        dateRange: []
      },
      reportData: null,
      loading: false
    }
  },
  methods: {
    // 生成报表
    generateReport(type) {
      this.reportParams.reportType = type
      this.handleGenerateReport()
    },

    // 处理生成报表
    handleGenerateReport() {
      if (!this.reportParams.dateRange || this.reportParams.dateRange.length < 2) {
        this.$message.warning('请选择报表期间')
        return
      }

      this.loading = true
      const params = {
        reportType: this.reportParams.reportType,
        startPeriod: this.reportParams.dateRange[0].format('YYYY-MM'),
        endPeriod: this.reportParams.dateRange[1].format('YYYY-MM')
      }

      this.$http.post('/finance/report/generate', params)
        .then((res) => {
          if (res.success) {
            this.reportData = res.result
            this.$nextTick(() => {
              this.renderCharts()
            })
          } else {
            this.$message.error(res.message || '生成报表失败')
          }
        })
        .finally(() => {
          this.loading = false
        })
    },

    // 导出报表
    handleExportReport() {
      if (!this.reportData) {
        this.$message.warning('请先生成报表')
        return
      }

      const params = {
        reportType: this.reportParams.reportType,
        startPeriod: this.reportParams.dateRange[0].format('YYYY-MM'),
        endPeriod: this.reportParams.dateRange[1].format('YYYY-MM')
      }

      this.$http.post('/finance/report/export', params, { responseType: 'blob' })
        .then((res) => {
          const blob = new Blob([res], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
          const url = window.URL.createObjectURL(blob)
          const link = document.createElement('a')
          link.href = url
          link.download = `${this.reportData.reportType}_${params.startPeriod}-${params.endPeriod}.xlsx`
          link.click()
          window.URL.revokeObjectURL(url)
          this.$message.success('报表导出成功')
        })
        .catch((error) => {
          this.$message.error('报表导出失败')
        })
    },

    // 渲染图表
    renderCharts() {
      if (this.reportData.reportType === '成本分析报表' && this.reportData.costComposition) {
        this.renderCostCompositionChart()
      }
    },

    // 渲染成本构成图表
    renderCostCompositionChart() {
      const chart = echarts.init(this.$refs.costCompositionChart)
      
      const data = this.reportData.costComposition.structure.map(item => ({
        name: item.type,
        value: item.amount
      }))

      const option = {
        title: {
          text: '成本构成分析',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c}元 ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            name: '成本构成',
            type: 'pie',
            radius: '50%',
            data: data,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }

      chart.setOption(option)
    },

    // 获取收入百分比
    getRevenuePercentage(type) {
      if (!this.reportData || !this.reportData.revenue) return 0
      
      const total = this.reportData.revenue.totalAmount
      const amount = this.reportData.revenue[type]
      
      return total > 0 ? Math.round((amount / total) * 100) : 0
    },

    // 获取优先级颜色
    getPriorityColor(priority) {
      const colorMap = {
        '高': 'red',
        '中': 'orange',
        '低': 'green'
      }
      return colorMap[priority] || 'default'
    },

    // 获取健康状态警告类型
    getHealthAlertType(status) {
      const typeMap = {
        '良好': 'success',
        '需要关注': 'warning',
        '风险': 'error'
      }
      return typeMap[status] || 'info'
    },

    // 打印报表
    handlePrintReport() {
      window.print()
    }
  }
}
</script>

<style scoped>
.report-selector {
  margin-bottom: 16px;
}

.report-card {
  cursor: pointer;
  transition: all 0.3s;
}

.report-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.report-icon {
  text-align: center;
  margin-bottom: 12px;
}

.report-info h3 {
  margin: 0;
  color: #262626;
}

.report-info p {
  margin: 4px 0 0 0;
  color: #8c8c8c;
  font-size: 12px;
}

.report-header {
  text-align: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.report-header h2 {
  margin: 0;
  color: #262626;
}

.report-header p {
  margin: 4px 0;
  color: #8c8c8c;
}
</style>
```

---

## 验收标准

### 功能验收标准
1. **数据同步功能**: 能够准确同步生产、团建、薪酬数据到财务系统
2. **会计分录生成**: 自动生成符合会计准则的标准会计分录
3. **成本核算功能**: 准确计算材料成本、人工成本、制造费用
4. **财务报表生成**: 生成准确的损益表、成本分析报表、现金流报表

### 质量验收标准
1. **数据准确性**: 财务数据计算准确，分录借贷平衡
2. **集成完整性**: 与业务模块数据同步完整，无遗漏
3. **多租户支持**: 正确实现租户数据隔离
4. **权限控制**: 按角色控制财务数据访问权限

### 业务验收标准
1. **业务流程完整**: 涵盖财务集成的完整业务流程
2. **报表功能完善**: 提供全面的财务分析和决策支持
3. **用户体验良好**: 界面友好，操作便捷
4. **数据可追溯**: 所有财务数据变更可追溯来源

---

## 交付物清单

### 代码交付物
1. **后端代码**: Service、Controller、Mapper等Java类
2. **前端代码**: Vue组件和页面
3. **数据库脚本**: 会计科目和初始数据
4. **集成配置**: 系统集成配置文件

### 文档交付物
1. **开发文档**: 本文档
2. **接口文档**: API接口说明文档
3. **财务手册**: 财务管理操作手册
4. **集成指南**: 系统集成配置指南

---

## 后续优化建议

### 功能优化
1. **税务计算**: 集成税务计算和申报功能
2. **预算管理**: 实现全面的预算编制和控制
3. **固定资产**: 增加固定资产管理模块
4. **资金管理**: 实现银行账户和资金管理

### 技术优化
1. **实时同步**: 实现业务数据的实时同步
2. **数据仓库**: 建立财务数据仓库支持复杂分析
3. **BI集成**: 集成商业智能工具提升分析能力
4. **移动端**: 开发移动端财务查询应用