# jshERP移动端模块说明文档

## 文档信息

| 项目 | 信息 |
|------|------|
| 文档标题 | jshERP移动端模块说明文档 |
| 文档版本 | v1.0.0 |
| 创建日期 | 2024-12-25 |
| 作者 | Augment Agent |
| 审核者 | 待定 |
| 批准者 | 待定 |

## 变更历史

| 版本 | 日期 | 变更内容 | 作者 |
|------|------|----------|------|
| v1.0.0 | 2024-12-25 | 初始版本创建 | Augment Agent |

## 目录

1. [模块架构概述](#1-模块架构概述)
2. [核心业务模块](#2-核心业务模块)
3. [UI组件模块](#3-ui组件模块)
4. [工具和服务模块](#4-工具和服务模块)
5. [配置和常量模块](#5-配置和常量模块)
6. [开发和构建模块](#6-开发和构建模块)

## 1. 模块架构概述

### 1.1 模块划分原则

#### 1.1.1 单一职责原则
每个模块只负责一个特定的功能领域，确保模块的内聚性和可维护性。

#### 1.1.2 依赖倒置原则
高层模块不依赖低层模块，都依赖于抽象接口，降低模块间的耦合度。

#### 1.1.3 开闭原则
模块对扩展开放，对修改关闭，通过接口和配置实现功能扩展。

#### 1.1.4 接口隔离原则
使用小而专的接口，避免模块依赖不需要的功能。

### 1.2 模块依赖关系

```mermaid
graph TB
    subgraph "表现层"
        A[Views] --> B[Components]
        B --> C[Layouts]
    end
    
    subgraph "业务逻辑层"
        D[Stores] --> E[Composables]
        E --> F[Services]
    end
    
    subgraph "数据访问层"
        G[API] --> H[Adapters]
        H --> I[Types]
    end
    
    subgraph "基础设施层"
        J[Utils] --> K[Constants]
        K --> L[Plugins]
    end
    
    A --> D
    D --> G
    G --> J
    B --> E
    E --> H
    H --> K
```### 1.3 模块通信机制

#### 1.3.1 事件总线
```typescript
// utils/eventBus.ts
import { EventEmitter } from 'events'

class EventBus extends EventEmitter {
  private static instance: EventBus
  
  static getInstance(): EventBus {
    if (!EventBus.instance) {
      EventBus.instance = new EventBus()
    }
    return EventBus.instance
  }
}

export const eventBus = EventBus.getInstance()
```

#### 1.3.2 依赖注入
```typescript
// utils/container.ts
class Container {
  private services = new Map<string, any>()
  
  register<T>(name: string, service: T): void {
    this.services.set(name, service)
  }
  
  resolve<T>(name: string): T {
    return this.services.get(name)
  }
}

export const container = new Container()
```

#### 1.3.3 模块接口定义
```typescript
// types/modules.ts
export interface ModuleInterface {
  name: string
  version: string
  dependencies: string[]
  initialize(): Promise<void>
  destroy(): Promise<void>
}

export interface ServiceInterface {
  start(): Promise<void>
  stop(): Promise<void>
  getStatus(): 'running' | 'stopped' | 'error'
}
```

### 1.4 模块生命周期管理

#### 1.4.1 模块注册
```typescript
// utils/moduleManager.ts
class ModuleManager {
  private modules = new Map<string, ModuleInterface>()
  
  register(module: ModuleInterface): void {
    this.modules.set(module.name, module)
  }
  
  async initializeAll(): Promise<void> {
    for (const module of this.modules.values()) {
      await module.initialize()
    }
  }
  
  async destroyAll(): Promise<void> {
    for (const module of this.modules.values()) {
      await module.destroy()
    }
  }
}
```

#### 1.4.2 模块启动顺序
1. **基础设施模块**：工具函数、常量定义
2. **配置模块**：环境配置、主题配置
3. **服务模块**：网络服务、存储服务
4. **业务模块**：认证、状态管理
5. **UI模块**：组件、页面

## 2. 核心业务模块

### 2.1 认证模块（Auth Module）

#### 2.1.1 模块职责
- 用户登录和注销
- Token管理和刷新
- 权限验证和控制
- 用户信息管理

#### 2.1.2 接口定义
```typescript
// api/auth.ts
export interface AuthService {
  login(credentials: LoginCredentials): Promise<LoginResponse>
  logout(): Promise<void>
  refreshToken(): Promise<string>
  getCurrentUser(): Promise<UserInfo>
  checkPermission(permission: string): boolean
}

export interface LoginCredentials {
  username: string
  password: string
  captcha?: string
  rememberMe?: boolean
}

export interface LoginResponse {
  token: string
  refreshToken: string
  userInfo: UserInfo
  permissions: string[]
  expiresIn: number
}
```#### 2.1.3 实现示例
```typescript
// stores/auth.ts
export const useAuthStore = defineStore('auth', () => {
  const token = ref<string>('')
  const userInfo = ref<UserInfo | null>(null)
  const permissions = ref<string[]>([])
  
  const isLoggedIn = computed(() => !!token.value)
  
  const login = async (credentials: LoginCredentials) => {
    try {
      const response = await AuthService.login(credentials)
      token.value = response.token
      userInfo.value = response.userInfo
      permissions.value = response.permissions
      
      // 持久化存储
      localStorage.setItem('auth_token', response.token)
      localStorage.setItem('user_info', JSON.stringify(response.userInfo))
      
      return response
    } catch (error) {
      throw new AuthError('登录失败', error)
    }
  }
  
  const logout = async () => {
    try {
      await AuthService.logout()
    } finally {
      // 清除状态
      token.value = ''
      userInfo.value = null
      permissions.value = []
      
      // 清除存储
      localStorage.removeItem('auth_token')
      localStorage.removeItem('user_info')
    }
  }
  
  const checkPermission = (permission: string): boolean => {
    return permissions.value.includes(permission)
  }
  
  return {
    token,
    userInfo,
    permissions,
    isLoggedIn,
    login,
    logout,
    checkPermission
  }
})
```

#### 2.1.4 配置参数
```typescript
// config/auth.ts
export const authConfig = {
  tokenKey: 'auth_token',
  refreshTokenKey: 'refresh_token',
  userInfoKey: 'user_info',
  tokenExpireTime: 7200, // 2小时
  refreshThreshold: 300,  // 5分钟前刷新
  maxRetryCount: 3,
  loginPath: '/auth/login',
  homePath: '/dashboard'
}
```

### 2.2 状态管理模块（Store Module）

#### 2.2.1 模块职责
- 全局状态管理
- 状态持久化
- 状态同步
- 状态变更监听

#### 2.2.2 Store结构设计
```typescript
// stores/index.ts
export interface RootState {
  auth: AuthState
  app: AppState
  business: BusinessState
}

export interface AuthState {
  token: string
  userInfo: UserInfo | null
  permissions: string[]
}

export interface AppState {
  loading: boolean
  theme: 'light' | 'dark'
  language: 'zh' | 'en'
  networkStatus: boolean
}

export interface BusinessState {
  salesData: SalesData[]
  inventoryData: InventoryData[]
  dashboardData: DashboardData | null
}
```

#### 2.2.3 状态持久化
```typescript
// plugins/persistence.ts
export const persistencePlugin = ({ store }: { store: any }) => {
  // 从localStorage恢复状态
  const savedState = localStorage.getItem('app_state')
  if (savedState) {
    store.$patch(JSON.parse(savedState))
  }
  
  // 监听状态变化并持久化
  store.$subscribe((mutation: any, state: any) => {
    localStorage.setItem('app_state', JSON.stringify(state))
  })
}
```

### 2.3 路由模块（Router Module）

#### 2.3.1 模块职责
- 路由配置和管理
- 路由守卫和权限控制
- 页面导航和跳转
- 路由状态管理

#### 2.3.2 路由配置
```typescript
// router/routes.ts
export const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/auth',
    component: () => import('@/layouts/AuthLayout.vue'),
    children: [
      {
        path: 'login',
        name: 'Login',
        component: () => import('@/views/auth/Login.vue'),
        meta: { 
          requiresAuth: false,
          title: '用户登录'
        }
      }
    ]
  },
  {
    path: '/dashboard',
    component: () => import('@/layouts/DefaultLayout.vue'),
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/Index.vue'),
        meta: { 
          requiresAuth: true,
          title: '仪表板',
          permissions: ['dashboard:view']
        }
      }
    ]
  }
]
```#### 2.3.3 路由守卫
```typescript
// router/guards.ts
export function setupRouterGuards(router: Router) {
  // 全局前置守卫
  router.beforeEach(async (to, from, next) => {
    const authStore = useAuthStore()
    const appStore = useAppStore()
    
    // 显示加载状态
    appStore.setLoading(true)
    
    // 检查认证状态
    if (to.meta.requiresAuth && !authStore.isLoggedIn) {
      next('/auth/login')
      return
    }
    
    // 检查权限
    if (to.meta.permissions) {
      const hasPermission = to.meta.permissions.some(
        (permission: string) => authStore.checkPermission(permission)
      )
      if (!hasPermission) {
        next('/403')
        return
      }
    }
    
    // 设置页面标题
    if (to.meta.title) {
      document.title = `${to.meta.title} - jshERP移动端`
    }
    
    next()
  })
  
  // 全局后置守卫
  router.afterEach(() => {
    const appStore = useAppStore()
    appStore.setLoading(false)
  })
}
```

### 2.4 API通信模块（API Module）

#### 2.4.1 模块职责
- HTTP请求封装
- 请求拦截和响应处理
- 错误处理和重试
- 请求缓存和优化

#### 2.4.2 HTTP客户端封装
```typescript
// utils/request.ts
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'

class HttpClient {
  private instance: AxiosInstance
  
  constructor(config: AxiosRequestConfig) {
    this.instance = axios.create(config)
    this.setupInterceptors()
  }
  
  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        const authStore = useAuthStore()
        if (authStore.token) {
          config.headers.Authorization = `Bearer ${authStore.token}`
        }
        return config
      },
      (error) => Promise.reject(error)
    )
    
    // 响应拦截器
    this.instance.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401) {
          const authStore = useAuthStore()
          await authStore.logout()
          router.push('/auth/login')
        }
        return Promise.reject(error)
      }
    )
  }
  
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.get(url, config)
    return response.data
  }
  
  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.post(url, data, config)
    return response.data
  }
  
  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.put(url, data, config)
    return response.data
  }
  
  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.delete(url, config)
    return response.data
  }
}

export const httpClient = new HttpClient({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})
```

#### 2.4.3 API适配器
```typescript
// api/adapters/base.ts
export abstract class BaseAdapter<TRequest, TResponse> {
  protected abstract endpoint: string
  
  abstract transform(rawData: any): TResponse
  
  async request(params: TRequest): Promise<TResponse> {
    try {
      const response = await httpClient.post(this.endpoint, params)
      return this.transform(response)
    } catch (error) {
      this.handleError(error)
      throw error
    }
  }
  
  protected handleError(error: any): void {
    console.error(`API Error [${this.endpoint}]:`, error)
    // 错误上报和处理逻辑
  }
}

// api/adapters/auth.ts
export class AuthAdapter extends BaseAdapter<LoginCredentials, LoginResponse> {
  protected endpoint = '/sys/login'
  
  transform(rawData: any): LoginResponse {
    return {
      token: rawData.result.token,
      refreshToken: rawData.result.refreshToken,
      userInfo: {
        id: rawData.result.userInfo.id,
        username: rawData.result.userInfo.username,
        realname: rawData.result.userInfo.realname,
        avatar: rawData.result.userInfo.avatar || '/default-avatar.png',
        email: rawData.result.userInfo.email,
        phone: rawData.result.userInfo.phone
      },
      permissions: rawData.result.permissions || [],
      expiresIn: rawData.result.expiresIn || 7200
    }
  }
}
```

## 3. UI组件模块

### 3.1 基础组件模块

#### 3.1.1 按钮组件
```typescript
// components/base/Button.vue
<template>
  <button
    :class="buttonClass"
    :disabled="disabled || loading"
    @click="handleClick"
  >
    <van-loading v-if="loading" size="16" />
    <slot v-else />
  </button>
</template>

<script setup lang="ts">
interface Props {
  type?: 'primary' | 'secondary' | 'danger'
  size?: 'small' | 'medium' | 'large'
  disabled?: boolean
  loading?: boolean
  block?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'primary',
  size: 'medium',
  disabled: false,
  loading: false,
  block: false
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

const buttonClass = computed(() => [
  'base-button',
  `base-button--${props.type}`,
  `base-button--${props.size}`,
  {
    'base-button--block': props.block,
    'base-button--loading': props.loading
  }
])

const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>
```#### 3.1.2 输入框组件
```typescript
// components/base/Input.vue
<template>
  <div class="base-input">
    <label v-if="label" class="base-input__label">{{ label }}</label>
    <div class="base-input__wrapper">
      <input
        v-model="inputValue"
        :type="type"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :class="inputClass"
        @focus="handleFocus"
        @blur="handleBlur"
        @input="handleInput"
      />
      <div v-if="$slots.suffix" class="base-input__suffix">
        <slot name="suffix" />
      </div>
    </div>
    <div v-if="errorMessage" class="base-input__error">{{ errorMessage }}</div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  modelValue: string
  type?: 'text' | 'password' | 'number' | 'email' | 'tel'
  label?: string
  placeholder?: string
  disabled?: boolean
  readonly?: boolean
  errorMessage?: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 'text',
  disabled: false,
  readonly: false
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
  focus: [event: FocusEvent]
  blur: [event: FocusEvent]
}>()

const inputValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const inputClass = computed(() => [
  'base-input__field',
  {
    'base-input__field--error': props.errorMessage,
    'base-input__field--disabled': props.disabled
  }
])

const handleFocus = (event: FocusEvent) => {
  emit('focus', event)
}

const handleBlur = (event: FocusEvent) => {
  emit('blur', event)
}

const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  emit('update:modelValue', target.value)
}
</script>
```

### 3.2 业务组件模块

#### 3.2.1 用户卡片组件
```typescript
// components/business/UserCard.vue
<template>
  <div class="user-card">
    <div class="user-card__avatar">
      <img :src="userInfo.avatar" :alt="userInfo.realname" />
    </div>
    <div class="user-card__info">
      <h3 class="user-card__name">{{ userInfo.realname }}</h3>
      <p class="user-card__username">@{{ userInfo.username }}</p>
      <p class="user-card__role">{{ userRole }}</p>
    </div>
    <div class="user-card__actions">
      <slot name="actions" />
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  userInfo: UserInfo
}

const props = defineProps<Props>()

const userRole = computed(() => {
  // 根据权限计算用户角色
  const authStore = useAuthStore()
  if (authStore.checkPermission('admin')) return '管理员'
  if (authStore.checkPermission('sales')) return '销售员'
  if (authStore.checkPermission('warehouse')) return '仓管员'
  return '普通用户'
})
</script>
```

#### 3.2.2 商品项组件
```typescript
// components/business/ProductItem.vue
<template>
  <div class="product-item" @click="handleClick">
    <div class="product-item__image">
      <img :src="product.image" :alt="product.name" />
    </div>
    <div class="product-item__info">
      <h4 class="product-item__name">{{ product.name }}</h4>
      <p class="product-item__code">编码：{{ product.code }}</p>
      <p class="product-item__price">¥{{ product.price }}</p>
      <p class="product-item__stock" :class="stockClass">
        库存：{{ product.stock }}
      </p>
    </div>
    <div class="product-item__actions">
      <slot name="actions" :product="product" />
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  product: ProductInfo
}

const props = defineProps<Props>()

const emit = defineEmits<{
  click: [product: ProductInfo]
}>()

const stockClass = computed(() => ({
  'product-item__stock--low': props.product.stock < 10,
  'product-item__stock--out': props.product.stock === 0
}))

const handleClick = () => {
  emit('click', props.product)
}
</script>
```

### 3.3 布局组件模块

#### 3.3.1 默认布局组件
```typescript
// layouts/DefaultLayout.vue
<template>
  <div class="default-layout">
    <Header />
    <main class="default-layout__main">
      <router-view />
    </main>
    <TabBar />
  </div>
</template>

<script setup lang="ts">
import Header from '@/components/layout/Header.vue'
import TabBar from '@/components/layout/TabBar.vue'
</script>
```

#### 3.3.2 认证布局组件
```typescript
// layouts/AuthLayout.vue
<template>
  <div class="auth-layout">
    <div class="auth-layout__header">
      <img src="/logo.png" alt="jshERP" class="auth-layout__logo" />
      <h1 class="auth-layout__title">jshERP移动端</h1>
    </div>
    <main class="auth-layout__main">
      <router-view />
    </main>
    <footer class="auth-layout__footer">
      <p>&copy; 2024 jshERP. All rights reserved.</p>
    </footer>
  </div>
</template>
```

## 4. 工具和服务模块

### 4.1 工具函数模块

#### 4.1.1 通用工具函数
```typescript
// utils/common.ts
export const utils = {
  // 防抖函数
  debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout
    return (...args: Parameters<T>) => {
      clearTimeout(timeout)
      timeout = setTimeout(() => func.apply(this, args), wait)
    }
  },
  
  // 节流函数
  throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle: boolean
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func.apply(this, args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  },
  
  // 深拷贝
  deepClone<T>(obj: T): T {
    if (obj === null || typeof obj !== 'object') return obj
    if (obj instanceof Date) return new Date(obj.getTime()) as any
    if (obj instanceof Array) return obj.map(item => this.deepClone(item)) as any
    if (typeof obj === 'object') {
      const clonedObj = {} as any
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          clonedObj[key] = this.deepClone(obj[key])
        }
      }
      return clonedObj
    }
    return obj
  },
  
  // 格式化文件大小
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },
  
  // 生成UUID
  generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
      const r = Math.random() * 16 | 0
      const v = c === 'x' ? r : (r & 0x3 | 0x8)
      return v.toString(16)
    })
  }
}
```#### 4.1.2 日期时间工具
```typescript
// utils/datetime.ts
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'

dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

export const datetime = {
  // 格式化日期
  format(date: Date | string | number, format = 'YYYY-MM-DD HH:mm:ss'): string {
    return dayjs(date).format(format)
  },
  
  // 相对时间
  fromNow(date: Date | string | number): string {
    return dayjs(date).fromNow()
  },
  
  // 是否是今天
  isToday(date: Date | string | number): boolean {
    return dayjs(date).isSame(dayjs(), 'day')
  },
  
  // 是否是本周
  isThisWeek(date: Date | string | number): boolean {
    return dayjs(date).isSame(dayjs(), 'week')
  },
  
  // 获取时间戳
  timestamp(date?: Date | string | number): number {
    return dayjs(date).valueOf()
  },
  
  // 日期范围
  dateRange(start: Date | string, end: Date | string): string[] {
    const startDate = dayjs(start)
    const endDate = dayjs(end)
    const dates: string[] = []
    
    let current = startDate
    while (current.isBefore(endDate) || current.isSame(endDate)) {
      dates.push(current.format('YYYY-MM-DD'))
      current = current.add(1, 'day')
    }
    
    return dates
  }
}
```

#### 4.1.3 验证工具
```typescript
// utils/validation.ts
export const validation = {
  // 手机号验证
  isPhone(phone: string): boolean {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phone)
  },
  
  // 邮箱验证
  isEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  },
  
  // 身份证验证
  isIdCard(idCard: string): boolean {
    const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
    return idCardRegex.test(idCard)
  },
  
  // 密码强度验证
  isStrongPassword(password: string): boolean {
    // 至少8位，包含大小写字母、数字和特殊字符
    const strongRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/
    return strongRegex.test(password)
  },
  
  // URL验证
  isUrl(url: string): boolean {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  },
  
  // 数字验证
  isNumber(value: any): boolean {
    return !isNaN(value) && !isNaN(parseFloat(value))
  },
  
  // 整数验证
  isInteger(value: any): boolean {
    return Number.isInteger(Number(value))
  }
}
```

### 4.2 存储服务模块

#### 4.2.1 本地存储服务
```typescript
// utils/storage.ts
class StorageService {
  private prefix: string
  
  constructor(prefix = 'jsherp_mobile_') {
    this.prefix = prefix
  }
  
  private getKey(key: string): string {
    return `${this.prefix}${key}`
  }
  
  // 设置数据
  set(key: string, value: any): void {
    try {
      const serializedValue = JSON.stringify(value)
      localStorage.setItem(this.getKey(key), serializedValue)
    } catch (error) {
      console.error('Storage set error:', error)
    }
  }
  
  // 获取数据
  get<T>(key: string, defaultValue?: T): T | null {
    try {
      const item = localStorage.getItem(this.getKey(key))
      if (item === null) return defaultValue || null
      return JSON.parse(item)
    } catch (error) {
      console.error('Storage get error:', error)
      return defaultValue || null
    }
  }
  
  // 删除数据
  remove(key: string): void {
    localStorage.removeItem(this.getKey(key))
  }
  
  // 清空所有数据
  clear(): void {
    const keys = Object.keys(localStorage)
    keys.forEach(key => {
      if (key.startsWith(this.prefix)) {
        localStorage.removeItem(key)
      }
    })
  }
  
  // 获取所有键
  keys(): string[] {
    const keys = Object.keys(localStorage)
    return keys
      .filter(key => key.startsWith(this.prefix))
      .map(key => key.replace(this.prefix, ''))
  }
}

export const storage = new StorageService()
```

#### 4.2.2 缓存服务
```typescript
// utils/cache.ts
interface CacheItem<T> {
  value: T
  expireTime: number
}

class CacheService {
  private cache = new Map<string, CacheItem<any>>()
  
  // 设置缓存
  set<T>(key: string, value: T, ttl = 300000): void { // 默认5分钟
    const expireTime = Date.now() + ttl
    this.cache.set(key, { value, expireTime })
  }
  
  // 获取缓存
  get<T>(key: string): T | null {
    const item = this.cache.get(key)
    if (!item) return null
    
    if (Date.now() > item.expireTime) {
      this.cache.delete(key)
      return null
    }
    
    return item.value
  }
  
  // 删除缓存
  delete(key: string): void {
    this.cache.delete(key)
  }
  
  // 清空缓存
  clear(): void {
    this.cache.clear()
  }
  
  // 清理过期缓存
  cleanup(): void {
    const now = Date.now()
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expireTime) {
        this.cache.delete(key)
      }
    }
  }
}

export const cache = new CacheService()

// 定时清理过期缓存
setInterval(() => {
  cache.cleanup()
}, 60000) // 每分钟清理一次
```

### 4.3 网络请求模块

#### 4.3.1 请求队列管理
```typescript
// utils/requestQueue.ts
interface QueueItem {
  id: string
  request: () => Promise<any>
  resolve: (value: any) => void
  reject: (error: any) => void
  retryCount: number
  maxRetries: number
}

class RequestQueue {
  private queue: QueueItem[] = []
  private processing = false
  private maxConcurrent = 3
  private currentRequests = 0
  
  // 添加请求到队列
  add<T>(
    request: () => Promise<T>,
    maxRetries = 3
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      const id = utils.generateUUID()
      this.queue.push({
        id,
        request,
        resolve,
        reject,
        retryCount: 0,
        maxRetries
      })
      this.process()
    })
  }
  
  // 处理队列
  private async process(): Promise<void> {
    if (this.processing || this.currentRequests >= this.maxConcurrent) {
      return
    }
    
    const item = this.queue.shift()
    if (!item) return
    
    this.processing = true
    this.currentRequests++
    
    try {
      const result = await item.request()
      item.resolve(result)
    } catch (error) {
      if (item.retryCount < item.maxRetries) {
        item.retryCount++
        this.queue.unshift(item) // 重新加入队列头部
      } else {
        item.reject(error)
      }
    } finally {
      this.currentRequests--
      this.processing = false
      
      // 继续处理队列
      if (this.queue.length > 0) {
        setTimeout(() => this.process(), 100)
      }
    }
  }
}

export const requestQueue = new RequestQueue()
```

### 4.4 错误处理模块

#### 4.4.1 错误类定义
```typescript
// utils/errors.ts
export class BaseError extends Error {
  public code: string
  public details?: any
  
  constructor(message: string, code = 'UNKNOWN_ERROR', details?: any) {
    super(message)
    this.name = this.constructor.name
    this.code = code
    this.details = details
  }
}

export class AuthError extends BaseError {
  constructor(message: string, details?: any) {
    super(message, 'AUTH_ERROR', details)
  }
}

export class NetworkError extends BaseError {
  constructor(message: string, details?: any) {
    super(message, 'NETWORK_ERROR', details)
  }
}

export class ValidationError extends BaseError {
  constructor(message: string, details?: any) {
    super(message, 'VALIDATION_ERROR', details)
  }
}

export class BusinessError extends BaseError {
  constructor(message: string, code: string, details?: any) {
    super(message, code, details)
  }
}
```