# 珐琅馆值班管理模块重建计划

## 项目概述

基于深度架构研究，采用**完全重写方案**重建珐琅馆值班管理模块，确保与jshERP系统架构100%一致。

### 技术要求
- **后端**：Spring Boot + MyBatis + MySQL（完全遵循jshERP架构）
- **前端**：Vue.js 2.7.16 + Ant Design Vue 1.5.2
- **架构模式**：严格对标用户管理模块（UserController + UserService + UserMapper模式）
- **数据库设计**：完全遵循jsh_user表结构模式
- **权限集成**：完全集成jshERP的RBAC权限模型

## 详细执行计划

### 第一阶段：清理现有模块 [进行中]
**目标**：删除所有与珐琅馆排班相关的现有实现

**清理范围**：
1. **后端文件清理**
   - `/src/main/java/com/jsh/erp/controller/CloisonneScheduleController.java`
   - `/src/main/java/com/jsh/erp/service/cloisonne/CloisonneScheduleService.java`
   - `/src/main/java/com/jsh/erp/datasource/entities/CloisonneSchedule.java`
   - `/src/main/java/com/jsh/erp/datasource/mappers/CloisonneScheduleMapperEx.java`
   - `/src/main/java/com/jsh/erp/datasource/vo/CloisonneScheduleVo4List.java`
   - `/src/main/resources/mapper_xml/cloisonne/CloisonneScheduleMapperEx.xml`

2. **前端文件清理**
   - `/src/views/operation/ScheduleCalendar.vue`
   - `/src/views/cloisonne/` 整个目录
   - `/src/api/cloisonne/schedule.js`
   - 相关路由配置

3. **数据库清理**
   - `DROP TABLE jsh_cloisonne_schedule`
   - `DELETE FROM jsh_function WHERE number LIKE '1002%'`

### 第二阶段：数据库设计与创建
**目标**：基于jsh_user表结构模式创建新的数据库表

**数据表设计**：
```sql
-- 珐琅馆值班管理表（对标jsh_user表结构）
CREATE TABLE `jsh_cloisonne_duty` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `duty_date` date NOT NULL COMMENT '值班日期',
  `employee_id` bigint(20) NOT NULL COMMENT '员工ID(关联jsh_user.id)',
  `employee_name` varchar(255) NOT NULL COMMENT '员工姓名',
  `shift_type` varchar(50) DEFAULT NULL COMMENT '班次类型(早班/晚班/全天)',
  `start_time` time DEFAULT NULL COMMENT '开始时间',
  `end_time` time DEFAULT NULL COMMENT '结束时间',
  `work_hours` decimal(4,2) DEFAULT NULL COMMENT '工作时长(小时)',
  `status` varchar(20) DEFAULT 'normal' COMMENT '状态(normal-正常/leave-请假/swap-调班)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户id',
  `delete_flag` varchar(1) DEFAULT '0' COMMENT '删除标记，0未删除，1删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_duty_unique` (`duty_date`, `employee_id`, `shift_type`, `tenant_id`, `delete_flag`),
  KEY `idx_duty_date` (`duty_date`, `tenant_id`, `delete_flag`),
  KEY `idx_employee` (`employee_id`, `tenant_id`, `delete_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='珐琅馆值班管理表';
```

**菜单配置**：
```sql
-- 菜单配置（对标用户管理模块）
INSERT INTO `jsh_function` VALUES 
(NULL, '1003', '珐琅馆值班', '10', '/cloisonne/duty', '/cloisonne/CloisonneDutyList', 
 '\0', '1003', '1', '电脑版', '1,2,3,4', 'calendar', '0');
```

### 第三阶段：后端实体层开发
**目标**：创建完全对标User实体的CloisonneDuty实体

**文件结构**：
1. `CloisonneDuty.java` - 基础实体类（对标User.java）
2. `CloisonneDutyEx.java` - 扩展实体类（对标UserEx.java）
3. `CloisonneDutyMapper.java` - 基础Mapper（MyBatis Generator生成）
4. `CloisonneDutyMapperEx.java` - 扩展Mapper（对标UserMapperEx.java）
5. `CloisonneDutyMapperEx.xml` - XML映射文件（对标UserMapperEx.xml）

### 第四阶段：后端服务层开发
**目标**：实现完全对标UserService的CloisonneDutyService

**核心功能**：
- CRUD操作（insertCloisonneDuty、updateCloisonneDuty、deleteCloisonneDuty、selectCloisonneDuty）
- 事务控制（@Transactional注解）
- 异常处理（JshException.writeFail()）
- 操作日志（logService.insertLog()）
- 多租户支持（自动tenant_id过滤）
- 软删除机制（delete_flag字段）

### 第五阶段：后端控制器开发
**目标**：实现完全对标UserController的CloisonneDutyController

**API接口设计**：
- `GET /cloisonne/duty/list` - 获取值班列表
- `POST /cloisonne/duty/add` - 新增值班记录
- `PUT /cloisonne/duty/update` - 更新值班记录
- `DELETE /cloisonne/duty/delete` - 删除值班记录
- `DELETE /cloisonne/duty/deleteBatch` - 批量删除
- `GET /cloisonne/duty/getUserList` - 获取员工下拉列表

### 第六阶段：前端API接口定义
**目标**：创建完全对标用户API的cloisonneDuty.js

**API方法**：
```javascript
const addCloisonneDuty = (params) => postAction("/cloisonne/duty/add", params);
const editCloisonneDuty = (params) => putAction("/cloisonne/duty/update", params);
const getCloisonneDutyList = (params) => getAction("/cloisonne/duty/list", params);
const deleteCloisonneDuty = (params) => deleteAction("/cloisonne/duty/delete", params);
```

### 第七阶段：前端主列表页面开发
**目标**：创建完全对标UserList.vue的CloisonneDutyList.vue

**核心特性**：
- 使用JeecgListMixin混入
- 标准的查询/操作/表格区域
- btnEnableList权限控制
- 统一的CRUD操作方法

### 第八阶段：前端弹窗组件开发
**目标**：创建完全对标UserModal.vue的CloisonneDutyModal.vue

**核心特性**：
- $form.createForm()表单创建
- validatorRules验证规则
- pick()字段选择
- 标准的提交处理流程

### 第九阶段：路由配置与权限集成
**目标**：配置前端路由和权限控制

**配置内容**：
- 动态路由配置
- 权限控制集成
- 菜单显示控制

### 第十阶段：功能测试与验证
**目标**：完整的功能测试

**测试范围**：
- CRUD操作测试
- 权限控制测试
- 多租户数据隔离测试
- 批量操作测试
- 异常处理测试

### 第十一阶段：集成测试与优化
**目标**：与现有jshERP系统集成测试

**测试内容**：
- 系统兼容性测试
- 性能测试
- 数据一致性测试
- 用户体验测试

### 第十二阶段：文档编写与部署
**目标**：完整的项目交付

**交付内容**：
- 技术开发文档
- 用户操作手册
- 部署指导文档
- API接口文档

## 质量保证

### 代码质量标准
- 100%遵循jshERP编码规范
- 完整的注释和文档
- 统一的异常处理
- 完整的日志记录

### 测试覆盖要求
- 单元测试覆盖率 > 80%
- 集成测试覆盖所有API
- 端到端测试覆盖主要业务流程
- 性能测试验证系统响应时间

### 兼容性保证
- 与现有jshERP模块无冲突
- 多租户数据完全隔离
- 权限控制完全集成
- 数据库事务完整性

## 风险控制

### 技术风险
- 架构不一致风险：通过深度研究用户模块确保100%对标
- 数据安全风险：严格遵循多租户和软删除机制
- 性能风险：通过索引优化和分页查询控制

### 项目风险
- 进度风险：分阶段实施，每阶段验证
- 质量风险：完整的测试覆盖和代码审查
- 集成风险：渐进式集成和回滚方案

## 预期成果

### 功能成果
- 完整的珐琅馆值班管理功能
- 与jshERP系统无缝集成
- 支持多租户和权限控制
- 完整的操作日志和审计

### 技术成果
- 100%符合jshERP架构标准
- 高质量的代码实现
- 完整的技术文档
- 可维护和可扩展的设计

---

**项目预计工期**：15-20个工作日
**开发团队**：Augment Code AI助手
**项目状态**：计划阶段完成，准备进入执行阶段
