# jshERP 排班管理模块数据库设计

## 概述

本文档描述了jshERP系统中排班管理模块的数据库设计，包括表结构、字段说明、索引设计和使用示例。

**创建日期**: 2025-06-21  
**版本**: v1.0  
**作者**: Augment Code  

## 表结构设计

### 1. 班次定义表 (jsh_schedule_shift)

#### 表说明
用于定义各种工作班次的基本信息，如全天班、上午班、下午班、晚班等。

#### 字段详情

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint | 20 | 是 | AUTO_INCREMENT | 主键ID |
| shift_name | varchar | 50 | 是 | - | 班次名称 |
| shift_type | varchar | 20 | 否 | FULL_DAY | 班次类型：FULL_DAY-全天班，MORNING-上午班，AFTERNOON-下午班，EVENING-晚班 |
| start_time | time | - | 否 | NULL | 开始时间 |
| end_time | time | - | 否 | NULL | 结束时间 |
| duration_hours | decimal | 4,2 | 否 | NULL | 班次时长（小时） |
| is_active | tinyint | 1 | 否 | 1 | 是否启用：0-禁用，1-启用 |
| sort_order | int | 11 | 否 | 0 | 排序 |
| description | text | - | 否 | NULL | 描述 |
| color | varchar | 20 | 否 | #1890ff | 显示颜色 |
| tenant_id | bigint | 20 | 是 | 0 | 租户ID |
| delete_flag | varchar | 1 | 否 | 0 | 删除标记：0-存在，1-删除 |
| create_time | datetime | - | 否 | CURRENT_TIMESTAMP | 创建时间 |
| update_time | datetime | - | 否 | CURRENT_TIMESTAMP | 更新时间 |
| create_by | bigint | 20 | 否 | NULL | 创建人ID |
| update_by | bigint | 20 | 否 | NULL | 更新人ID |

#### 索引设计

| 索引名 | 类型 | 字段 | 说明 |
|--------|------|------|------|
| PRIMARY | 主键 | id | 主键索引 |
| idx_tenant_id | 普通 | tenant_id | 租户查询优化 |
| idx_delete_flag | 普通 | delete_flag | 删除标记查询优化 |
| idx_shift_type | 普通 | shift_type | 班次类型查询优化 |
| idx_is_active | 普通 | is_active | 启用状态查询优化 |
| idx_sort_order | 普通 | sort_order | 排序查询优化 |

### 2. 排班记录表 (jsh_schedule_entry)

#### 表说明
用于记录具体的员工排班信息，包括排班日期、班次、状态等。

#### 字段详情

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint | 20 | 是 | AUTO_INCREMENT | 主键 |
| shift_id | bigint | 20 | 是 | - | 班次ID，关联jsh_schedule_shift表 |
| employee_id | bigint | 20 | 是 | - | 员工ID，关联jsh_user表 |
| schedule_date | date | - | 是 | - | 排班日期 |
| status | varchar | 20 | 否 | SCHEDULED | 状态：SCHEDULED-已排班，CONFIRMED-已确认，CANCELLED-已取消 |
| actual_start_time | datetime | - | 否 | NULL | 实际开始时间 |
| actual_end_time | datetime | - | 否 | NULL | 实际结束时间 |
| break_duration | int | 11 | 否 | 0 | 休息时长（分钟） |
| overtime_duration | int | 11 | 否 | 0 | 加班时长（分钟） |
| remark | varchar | 500 | 否 | NULL | 备注 |
| create_time | datetime | - | 否 | CURRENT_TIMESTAMP | 创建时间 |
| update_time | datetime | - | 否 | CURRENT_TIMESTAMP | 更新时间 |
| create_user | bigint | 20 | 否 | NULL | 创建用户 |
| update_user | bigint | 20 | 否 | NULL | 更新用户 |
| tenant_id | bigint | 20 | 否 | NULL | 租户id |
| delete_flag | varchar | 1 | 否 | 0 | 删除标记，0未删除，1删除 |

#### 索引设计

| 索引名 | 类型 | 字段 | 说明 |
|--------|------|------|------|
| PRIMARY | 主键 | id | 主键索引 |
| idx_tenant_delete | 复合 | tenant_id, delete_flag | 多租户数据隔离优化 |
| idx_schedule_employee | 复合 | schedule_date, employee_id | **核心复合索引**，优化按日期和员工查询 |
| idx_shift_id | 普通 | shift_id | 班次关联查询优化 |
| idx_employee_id | 普通 | employee_id | 员工查询优化 |
| idx_schedule_date | 普通 | schedule_date | 日期查询优化 |
| idx_status | 普通 | status | 状态查询优化 |

#### 外键约束

| 约束名 | 外键字段 | 引用表 | 引用字段 | 说明 |
|--------|----------|--------|----------|------|
| fk_schedule_entry_shift | shift_id | jsh_schedule_shift | id | 确保班次ID的引用完整性 |
| fk_schedule_entry_employee | employee_id | jsh_user | id | 确保员工ID的引用完整性 |

## 数据字典

### 班次类型 (shift_type)

| 值 | 说明 |
|----|------|
| FULL_DAY | 全天班 |
| MORNING | 上午班 |
| AFTERNOON | 下午班 |
| EVENING | 晚班 |

### 排班状态 (status)

| 值 | 说明 |
|----|------|
| SCHEDULED | 已排班 |
| CONFIRMED | 已确认 |
| CANCELLED | 已取消 |

## 使用示例

### 1. 查询员工某日排班信息

```sql
SELECT 
    se.id,
    se.schedule_date,
    ss.shift_name,
    ss.start_time,
    ss.end_time,
    u.username,
    se.status,
    se.remark
FROM jsh_schedule_entry se
JOIN jsh_schedule_shift ss ON se.shift_id = ss.id
JOIN jsh_user u ON se.employee_id = u.id
WHERE se.schedule_date = '2025-06-22' 
  AND se.employee_id = 120
  AND se.delete_flag = '0';
```

### 2. 查询某月所有排班记录

```sql
SELECT 
    se.schedule_date,
    u.username,
    ss.shift_name,
    ss.start_time,
    ss.end_time,
    se.status
FROM jsh_schedule_entry se
JOIN jsh_schedule_shift ss ON se.shift_id = ss.id
JOIN jsh_user u ON se.employee_id = u.id
WHERE se.schedule_date BETWEEN '2025-06-01' AND '2025-06-30'
  AND se.tenant_id = 0
  AND se.delete_flag = '0'
ORDER BY se.schedule_date, u.username;
```

### 3. 统计员工工作时长

```sql
SELECT 
    u.username,
    SUM(ss.duration_hours) as total_hours,
    COUNT(*) as shift_count
FROM jsh_schedule_entry se
JOIN jsh_schedule_shift ss ON se.shift_id = ss.id
JOIN jsh_user u ON se.employee_id = u.id
WHERE se.schedule_date BETWEEN '2025-06-01' AND '2025-06-30'
  AND se.status IN ('SCHEDULED', 'CONFIRMED')
  AND se.delete_flag = '0'
GROUP BY se.employee_id, u.username
ORDER BY total_hours DESC;
```

## 性能优化建议

### 1. 索引使用
- 主要查询场景使用复合索引 `idx_schedule_employee (schedule_date, employee_id)`
- 多租户查询使用 `idx_tenant_delete (tenant_id, delete_flag)`
- 避免在 `remark` 等大字段上建立索引

### 2. 查询优化
- 日期范围查询时使用 BETWEEN 而不是多个 OR 条件
- 始终包含 `delete_flag = '0'` 条件
- 多租户环境下始终包含 `tenant_id` 条件

### 3. 数据维护
- 定期清理历史数据（建议保留2年）
- 使用逻辑删除而不是物理删除
- 定期分析表统计信息

## 扩展建议

### 1. 可能的扩展字段
- `location_id`: 工作地点ID
- `department_id`: 部门ID  
- `cost_center`: 成本中心
- `approval_status`: 审批状态
- `approved_by`: 审批人
- `approved_time`: 审批时间

### 2. 相关表建议
- `jsh_schedule_template`: 排班模板表
- `jsh_schedule_rule`: 排班规则表
- `jsh_schedule_exception`: 排班异常表
- `jsh_attendance_record`: 考勤记录表

## 注意事项

1. **多租户隔离**: 所有查询必须包含 `tenant_id` 条件
2. **逻辑删除**: 使用 `delete_flag` 字段，不进行物理删除
3. **外键约束**: 确保数据完整性，但可能影响性能
4. **时区处理**: 时间字段需要考虑时区问题
5. **并发控制**: 排班操作需要考虑并发冲突
