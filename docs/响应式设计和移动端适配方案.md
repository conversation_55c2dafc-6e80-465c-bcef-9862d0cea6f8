# 掐丝珐琅馆综合管理模块响应式设计和移动端适配方案

## 📋 设计概述

本文档详细描述了掐丝珐琅馆综合管理模块在不同设备和屏幕尺寸下的响应式设计方案，确保在桌面端、平板端、移动端都能提供优秀的用户体验。

---

## 📱 设备分类和断点策略

### 设备分类
```javascript
// 设备类型定义
const DEVICE_TYPES = {
  MOBILE: 'mobile',      // 手机 (<768px)
  TABLET: 'tablet',      // 平板 (768px-1199px)
  DESKTOP: 'desktop'     // 桌面 (≥1200px)
}

// 断点定义 (基于Ant Design Vue)
const BREAKPOINTS = {
  xs: 480,   // 超小屏幕
  sm: 576,   // 小屏幕
  md: 768,   // 中等屏幕
  lg: 992,   // 大屏幕
  xl: 1200,  // 超大屏幕
  xxl: 1600  // 超超大屏幕
}
```

### Less变量定义
```less
// 响应式断点
@screen-xs: 480px;
@screen-sm: 576px;
@screen-md: 768px;
@screen-lg: 992px;
@screen-xl: 1200px;
@screen-xxl: 1600px;

// 容器最大宽度
@container-max-width-sm: 540px;
@container-max-width-md: 720px;
@container-max-width-lg: 960px;
@container-max-width-xl: 1140px;
@container-max-width-xxl: 1320px;

// 间距系统
@spacing-xs: 8px;
@spacing-sm: 12px;
@spacing-md: 16px;
@spacing-lg: 24px;
@spacing-xl: 32px;

// 移动端优化
@mobile-header-height: 56px;
@mobile-tab-height: 50px;
@mobile-button-height: 44px;
@mobile-touch-target: 44px;
```

---

## 🖥️ 桌面端设计 (≥1200px)

### 总览仪表板
```vue
<template>
  <div class="desktop-dashboard">
    <!-- 4列网格布局 -->
    <a-row :gutter="24" class="metrics-row">
      <a-col :span="6" v-for="metric in metrics" :key="metric.id">
        <metric-card :data="metric" size="large" />
      </a-col>
    </a-row>
    
    <!-- 主要内容区域 -->
    <a-row :gutter="24" class="content-row">
      <a-col :span="16">
        <tasks-panel :height="400" />
      </a-col>
      <a-col :span="8">
        <staff-panel :height="400" />
      </a-col>
    </a-row>
  </div>
</template>

<style lang="less" scoped>
.desktop-dashboard {
  .metrics-row {
    margin-bottom: 24px;
  }
  
  .content-row {
    .ant-col {
      min-height: 400px;
    }
  }
}
</style>
```

### 排班管理
```vue
<template>
  <div class="desktop-schedule">
    <!-- 控制面板 -->
    <a-card class="controls-panel">
      <a-row justify="space-between" align="middle">
        <a-col>
          <month-navigation />
        </a-col>
        <a-col>
          <view-switcher />
        </a-col>
      </a-row>
    </a-card>
    
    <!-- 日历视图 (7x6网格) -->
    <calendar-view 
      v-if="viewMode === 'calendar'"
      :cell-height="140"
      :show-details="true" />
  </div>
</template>
```

### POS销售
```vue
<template>
  <div class="desktop-pos">
    <a-row :gutter="24" style="height: 100vh;">
      <!-- 商品选择区 (2/3宽度) -->
      <a-col :span="16" class="products-section">
        <product-grid :columns="4" />
      </a-col>
      
      <!-- 购物车区 (1/3宽度) -->
      <a-col :span="8" class="cart-section">
        <shopping-cart :fixed="true" />
      </a-col>
    </a-row>
  </div>
</template>
```

---

## 📱 平板端设计 (768px-1199px)

### 总览仪表板
```vue
<template>
  <div class="tablet-dashboard">
    <!-- 2列网格布局 -->
    <a-row :gutter="16" class="metrics-row">
      <a-col :span="12" v-for="metric in metrics" :key="metric.id">
        <metric-card :data="metric" size="medium" />
      </a-col>
    </a-row>
    
    <!-- 堆叠布局 -->
    <a-row :gutter="16">
      <a-col :span="24">
        <tasks-panel :height="300" />
      </a-col>
    </a-row>
    
    <a-row :gutter="16">
      <a-col :span="24">
        <staff-panel :height="250" />
      </a-col>
    </a-row>
  </div>
</template>

<style lang="less" scoped>
.tablet-dashboard {
  .metrics-row {
    margin-bottom: 16px;
  }
  
  .ant-row {
    margin-bottom: 16px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
```

### 排班管理
```vue
<template>
  <div class="tablet-schedule">
    <!-- 简化控制面板 -->
    <a-card class="controls-panel" size="small">
      <a-row justify="space-between">
        <a-col>
          <month-navigation :compact="true" />
        </a-col>
        <a-col>
          <view-switcher :size="small" />
        </a-col>
      </a-row>
    </a-card>
    
    <!-- 日历视图 (较小格子) -->
    <calendar-view 
      v-if="viewMode === 'calendar'"
      :cell-height="100"
      :show-details="false" />
  </div>
</template>
```

### POS销售
```vue
<template>
  <div class="tablet-pos">
    <!-- Tab切换布局 -->
    <a-tabs v-model="activeTab" size="large">
      <a-tab-pane key="products" tab="商品选择">
        <product-grid :columns="3" />
      </a-tab-pane>
      
      <a-tab-pane key="cart" :tab="`购物车(${cartCount})`">
        <shopping-cart :compact="true" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>
```

---

## 📱 移动端设计 (<768px)

### 响应式混入
```javascript
// responsiveMixin.js
export const responsiveMixin = {
  data() {
    return {
      screenWidth: window.innerWidth,
      isMobile: false,
      isTablet: false,
      isDesktop: false
    }
  },
  
  computed: {
    deviceType() {
      if (this.screenWidth < 768) return 'mobile'
      if (this.screenWidth < 1200) return 'tablet'
      return 'desktop'
    },
    
    gridCols() {
      switch (this.deviceType) {
        case 'mobile': return { xs: 24, sm: 24 }
        case 'tablet': return { xs: 24, sm: 12, md: 12 }
        case 'desktop': return { xs: 24, sm: 12, md: 8, lg: 6 }
        default: return { xs: 24 }
      }
    }
  },
  
  mounted() {
    this.updateDeviceInfo()
    window.addEventListener('resize', this.handleResize)
  },
  
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  
  methods: {
    handleResize() {
      this.screenWidth = window.innerWidth
      this.updateDeviceInfo()
    },
    
    updateDeviceInfo() {
      this.isMobile = this.deviceType === 'mobile'
      this.isTablet = this.deviceType === 'tablet'
      this.isDesktop = this.deviceType === 'desktop'
    }
  }
}
```

### 移动端总览仪表板
```vue
<template>
  <div class="mobile-dashboard">
    <!-- 单列布局 -->
    <div class="metrics-section">
      <metric-card 
        v-for="metric in metrics" 
        :key="metric.id"
        :data="metric" 
        size="small"
        class="metric-item" />
    </div>
    
    <!-- 折叠面板 -->
    <a-collapse v-model="activePanel" accordion>
      <a-collapse-panel key="tasks" header="今日工作">
        <tasks-panel :compact="true" />
      </a-collapse-panel>
      
      <a-collapse-panel key="staff" header="值班信息">
        <staff-panel :compact="true" />
      </a-collapse-panel>
    </a-collapse>
  </div>
</template>

<style lang="less" scoped>
.mobile-dashboard {
  padding: 12px;
  
  .metrics-section {
    margin-bottom: 16px;
    
    .metric-item {
      margin-bottom: 12px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>
```

### 移动端排班管理
```vue
<template>
  <div class="mobile-schedule">
    <!-- 固定头部 -->
    <div class="mobile-header">
      <a-row justify="space-between" align="middle">
        <a-col>
          <h3>{{ currentMonthName }}</h3>
        </a-col>
        <a-col>
          <a-button-group size="small">
            <a-button @click="prevMonth">
              <a-icon type="left" />
            </a-button>
            <a-button @click="nextMonth">
              <a-icon type="right" />
            </a-button>
          </a-button-group>
        </a-col>
      </a-row>
    </div>
    
    <!-- 底部Tab导航 -->
    <a-tabs 
      v-model="viewMode" 
      tab-position="bottom"
      class="mobile-tabs">
      <a-tab-pane key="calendar" tab="日历">
        <mobile-calendar />
      </a-tab-pane>
      
      <a-tab-pane key="list" tab="列表">
        <mobile-schedule-list />
      </a-tab-pane>
      
      <a-tab-pane key="add" tab="添加">
        <mobile-add-schedule />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<style lang="less" scoped>
.mobile-schedule {
  height: 100vh;
  display: flex;
  flex-direction: column;
  
  .mobile-header {
    padding: 12px 16px;
    background: white;
    border-bottom: 1px solid #f0f0f0;
    flex-shrink: 0;
    
    h3 {
      margin: 0;
      font-size: 18px;
    }
  }
  
  .mobile-tabs {
    flex: 1;
    
    .ant-tabs-content {
      height: calc(100vh - @mobile-header-height - @mobile-tab-height);
      overflow-y: auto;
    }
    
    .ant-tabs-bar {
      margin: 0;
      border-top: 1px solid #f0f0f0;
    }
  }
}
</style>
```

### 移动端POS销售
```vue
<template>
  <div class="mobile-pos">
    <!-- 顶部状态栏 -->
    <div class="pos-header">
      <a-row justify="space-between" align="middle">
        <a-col>
          <h3>POS销售</h3>
        </a-col>
        <a-col>
          <a-badge :count="cartCount" :offset="[10, 0]">
            <a-button 
              type="primary" 
              shape="circle" 
              icon="shopping-cart"
              @click="showCart" />
          </a-badge>
        </a-col>
      </a-row>
    </div>
    
    <!-- 商品网格 (2列) -->
    <div class="products-grid">
      <a-row :gutter="8">
        <a-col 
          :span="12" 
          v-for="product in products" 
          :key="product.id">
          <mobile-product-card :product="product" />
        </a-col>
      </a-row>
    </div>
    
    <!-- 底部购物车抽屉 -->
    <a-drawer
      v-model="cartVisible"
      title="购物车"
      placement="bottom"
      :height="400">
      <mobile-shopping-cart />
    </a-drawer>
  </div>
</template>

<style lang="less" scoped>
.mobile-pos {
  .pos-header {
    padding: 12px 16px;
    background: white;
    border-bottom: 1px solid #f0f0f0;
    position: sticky;
    top: 0;
    z-index: 10;
  }
  
  .products-grid {
    padding: 12px;
    
    .ant-col {
      margin-bottom: 12px;
    }
  }
}
</style>
```

---

## 🎨 移动端组件优化

### 移动端商品卡片
```vue
<template>
  <a-card 
    class="mobile-product-card"
    :bordered="false"
    @click="handleAddToCart">
    
    <div class="product-image">
      <img :src="product.image" :alt="product.name" />
      <div class="quick-add" v-if="product.stock > 0">
        <a-icon type="plus" />
      </div>
    </div>
    
    <div class="product-info">
      <div class="product-name">{{ product.name }}</div>
      <div class="product-price">¥{{ product.price.toFixed(2) }}</div>
      <div class="product-stock" :class="getStockClass(product.stock)">
        库存: {{ product.stock }}
      </div>
    </div>
  </a-card>
</template>

<style lang="less" scoped>
.mobile-product-card {
  .product-image {
    position: relative;
    height: 120px;
    overflow: hidden;
    border-radius: 6px;
    margin-bottom: 8px;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .quick-add {
      position: absolute;
      bottom: 8px;
      right: 8px;
      width: 32px;
      height: 32px;
      background: #3B82F6;
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
    }
  }
  
  .product-info {
    .product-name {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 4px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .product-price {
      font-size: 16px;
      font-weight: bold;
      color: #ff4d4f;
      margin-bottom: 4px;
    }
    
    .product-stock {
      font-size: 12px;
      
      &.normal { color: #52c41a; }
      &.low { color: #fa8c16; }
      &.out { color: #ff4d4f; }
    }
  }
}
</style>
```

### 移动端购物车
```vue
<template>
  <div class="mobile-shopping-cart">
    <!-- 购物车商品列表 -->
    <div class="cart-items">
      <div 
        v-for="item in items" 
        :key="item.id"
        class="cart-item">
        
        <div class="item-image">
          <img :src="item.image" :alt="item.name" />
        </div>
        
        <div class="item-info">
          <div class="item-name">{{ item.name }}</div>
          <div class="item-price">¥{{ item.price.toFixed(2) }}</div>
        </div>
        
        <div class="item-controls">
          <a-button-group size="small">
            <a-button @click="decreaseQuantity(item)">-</a-button>
            <a-button disabled>{{ item.quantity }}</a-button>
            <a-button @click="increaseQuantity(item)">+</a-button>
          </a-button-group>
        </div>
      </div>
    </div>
    
    <!-- 底部结算 -->
    <div class="cart-footer">
      <div class="total-amount">
        总计: ¥{{ totalAmount.toFixed(2) }}
      </div>
      <a-button 
        type="primary" 
        size="large"
        block
        @click="handleCheckout">
        立即结算
      </a-button>
    </div>
  </div>
</template>

<style lang="less" scoped>
.mobile-shopping-cart {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .cart-items {
    flex: 1;
    overflow-y: auto;
    padding: 0 16px;
    
    .cart-item {
      display: flex;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;
      
      .item-image {
        width: 50px;
        height: 50px;
        margin-right: 12px;
        border-radius: 4px;
        overflow: hidden;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      
      .item-info {
        flex: 1;
        margin-right: 12px;
        
        .item-name {
          font-size: 14px;
          margin-bottom: 4px;
        }
        
        .item-price {
          font-size: 14px;
          color: #ff4d4f;
          font-weight: bold;
        }
      }
      
      .item-controls {
        .ant-btn-group {
          .ant-btn {
            width: 32px;
            height: 32px;
            padding: 0;
          }
        }
      }
    }
  }
  
  .cart-footer {
    padding: 16px;
    border-top: 1px solid #f0f0f0;
    background: white;
    
    .total-amount {
      font-size: 18px;
      font-weight: bold;
      text-align: center;
      margin-bottom: 12px;
      color: #333;
    }
    
    .ant-btn {
      height: @mobile-button-height;
      font-size: 16px;
      font-weight: bold;
    }
  }
}
</style>
```

---

## 👆 触摸交互优化

### 触摸目标尺寸
```less
// 触摸目标最小尺寸 (遵循Apple和Google设计规范)
@touch-target-min: 44px;
@touch-target-comfortable: 48px;

// 移动端按钮尺寸
.mobile-button {
  min-height: @touch-target-min;
  min-width: @touch-target-min;
  padding: 12px 16px;
  font-size: 16px;

  &.large {
    min-height: @touch-target-comfortable;
    padding: 14px 20px;
    font-size: 18px;
  }

  &.icon-only {
    width: @touch-target-min;
    height: @touch-target-min;
    padding: 0;
  }
}

// 移动端表单控件
.mobile-form-control {
  .ant-input,
  .ant-select-selector,
  .ant-picker {
    min-height: @touch-target-min;
    font-size: 16px; // 防止iOS缩放
  }

  .ant-checkbox,
  .ant-radio {
    transform: scale(1.2);
    margin-right: 12px;
  }
}
```

### 手势支持
```javascript
// gesturesMixin.js
export const gesturesMixin = {
  data() {
    return {
      touchStartX: 0,
      touchStartY: 0,
      touchEndX: 0,
      touchEndY: 0,
      swipeThreshold: 50,
      tapTimeout: null
    }
  },

  methods: {
    handleTouchStart(event) {
      const touch = event.touches[0]
      this.touchStartX = touch.clientX
      this.touchStartY = touch.clientY
    },

    handleTouchEnd(event) {
      const touch = event.changedTouches[0]
      this.touchEndX = touch.clientX
      this.touchEndY = touch.clientY

      this.detectSwipe()
    },

    detectSwipe() {
      const deltaX = this.touchEndX - this.touchStartX
      const deltaY = this.touchEndY - this.touchStartY

      if (Math.abs(deltaX) > Math.abs(deltaY)) {
        // 水平滑动
        if (Math.abs(deltaX) > this.swipeThreshold) {
          if (deltaX > 0) {
            this.onSwipeRight()
          } else {
            this.onSwipeLeft()
          }
        }
      } else {
        // 垂直滑动
        if (Math.abs(deltaY) > this.swipeThreshold) {
          if (deltaY > 0) {
            this.onSwipeDown()
          } else {
            this.onSwipeUp()
          }
        }
      }
    },

    onSwipeLeft() {
      // 左滑事件 - 可用于切换到下一页
      this.$emit('swipe-left')
    },

    onSwipeRight() {
      // 右滑事件 - 可用于切换到上一页
      this.$emit('swipe-right')
    },

    onSwipeUp() {
      // 上滑事件 - 可用于刷新
      this.$emit('swipe-up')
    },

    onSwipeDown() {
      // 下滑事件 - 可用于返回
      this.$emit('swipe-down')
    },

    handleDoubleTap(callback) {
      if (this.tapTimeout) {
        clearTimeout(this.tapTimeout)
        this.tapTimeout = null
        callback()
      } else {
        this.tapTimeout = setTimeout(() => {
          this.tapTimeout = null
        }, 300)
      }
    }
  }
}
```

### 长按交互
```vue
<template>
  <div
    class="long-press-item"
    @touchstart="handleTouchStart"
    @touchend="handleTouchEnd"
    @touchcancel="handleTouchCancel">

    <div class="item-content">
      {{ item.name }}
    </div>

    <!-- 长按菜单 -->
    <div v-if="showContextMenu" class="context-menu">
      <a-button @click="handleEdit">编辑</a-button>
      <a-button @click="handleDelete" danger>删除</a-button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      longPressTimer: null,
      showContextMenu: false,
      longPressDuration: 500 // 500ms
    }
  },

  methods: {
    handleTouchStart() {
      this.longPressTimer = setTimeout(() => {
        this.showContextMenu = true
        this.vibrate() // 触觉反馈
      }, this.longPressDuration)
    },

    handleTouchEnd() {
      if (this.longPressTimer) {
        clearTimeout(this.longPressTimer)
        this.longPressTimer = null
      }
    },

    handleTouchCancel() {
      this.handleTouchEnd()
    },

    vibrate() {
      if (navigator.vibrate) {
        navigator.vibrate(50) // 50ms震动
      }
    }
  }
}
</script>
```

---

## ⚡ 性能优化

### 虚拟滚动
```vue
<template>
  <div class="virtual-list" ref="container">
    <div
      class="virtual-list-phantom"
      :style="{ height: totalHeight + 'px' }">
    </div>

    <div
      class="virtual-list-content"
      :style="{ transform: `translateY(${offset}px)` }">
      <div
        v-for="item in visibleItems"
        :key="item.id"
        class="virtual-list-item"
        :style="{ height: itemHeight + 'px' }">
        <slot :item="item" :index="item.index"></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'VirtualList',

  props: {
    items: { type: Array, default: () => [] },
    itemHeight: { type: Number, default: 60 },
    containerHeight: { type: Number, default: 400 }
  },

  data() {
    return {
      scrollTop: 0,
      visibleCount: 0
    }
  },

  computed: {
    totalHeight() {
      return this.items.length * this.itemHeight
    },

    startIndex() {
      return Math.floor(this.scrollTop / this.itemHeight)
    },

    endIndex() {
      return Math.min(
        this.startIndex + this.visibleCount + 1,
        this.items.length
      )
    },

    visibleItems() {
      return this.items.slice(this.startIndex, this.endIndex).map((item, index) => ({
        ...item,
        index: this.startIndex + index
      }))
    },

    offset() {
      return this.startIndex * this.itemHeight
    }
  },

  mounted() {
    this.visibleCount = Math.ceil(this.containerHeight / this.itemHeight)
    this.$refs.container.addEventListener('scroll', this.handleScroll)
  },

  beforeDestroy() {
    this.$refs.container.removeEventListener('scroll', this.handleScroll)
  },

  methods: {
    handleScroll(event) {
      this.scrollTop = event.target.scrollTop
    }
  }
}
</script>

<style lang="less" scoped>
.virtual-list {
  height: 100%;
  overflow-y: auto;
  position: relative;

  .virtual-list-phantom {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: -1;
  }

  .virtual-list-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
  }

  .virtual-list-item {
    border-bottom: 1px solid #f0f0f0;
  }
}
</style>
```

### 图片懒加载
```vue
<template>
  <div class="lazy-image" ref="container">
    <img
      v-if="loaded"
      :src="src"
      :alt="alt"
      @load="handleLoad"
      @error="handleError" />

    <div v-else class="image-placeholder">
      <a-spin v-if="loading" />
      <a-icon v-else type="picture" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'LazyImage',

  props: {
    src: { type: String, required: true },
    alt: { type: String, default: '' },
    placeholder: { type: String, default: '' }
  },

  data() {
    return {
      loaded: false,
      loading: false,
      observer: null
    }
  },

  mounted() {
    this.initIntersectionObserver()
  },

  beforeDestroy() {
    if (this.observer) {
      this.observer.disconnect()
    }
  },

  methods: {
    initIntersectionObserver() {
      if ('IntersectionObserver' in window) {
        this.observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              this.loadImage()
              this.observer.unobserve(entry.target)
            }
          })
        }, {
          rootMargin: '50px'
        })

        this.observer.observe(this.$refs.container)
      } else {
        // 降级处理
        this.loadImage()
      }
    },

    loadImage() {
      this.loading = true
      const img = new Image()
      img.onload = () => {
        this.loaded = true
        this.loading = false
      }
      img.onerror = () => {
        this.loading = false
        this.handleError()
      }
      img.src = this.src
    },

    handleLoad() {
      this.$emit('load')
    },

    handleError() {
      this.$emit('error')
    }
  }
}
</script>

<style lang="less" scoped>
.lazy-image {
  position: relative;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .image-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;
    color: #ccc;
    font-size: 24px;
  }
}
</style>
```

### 防抖和节流
```javascript
// utils/performance.js
export function debounce(func, wait, immediate = false) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      timeout = null
      if (!immediate) func.apply(this, args)
    }
    const callNow = immediate && !timeout
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    if (callNow) func.apply(this, args)
  }
}

export function throttle(func, limit) {
  let inThrottle
  return function(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// 使用示例
export default {
  methods: {
    handleSearch: debounce(function(keyword) {
      this.searchProducts(keyword)
    }, 300),

    handleScroll: throttle(function(event) {
      this.updateScrollPosition(event.target.scrollTop)
    }, 100)
  }
}
```

---

## 📱 PWA支持

### Service Worker
```javascript
// public/sw.js
const CACHE_NAME = 'cloisonne-v1.0.0'
const urlsToCache = [
  '/',
  '/static/css/app.css',
  '/static/js/app.js',
  '/static/images/logo.png'
]

self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => cache.addAll(urlsToCache))
  )
})

self.addEventListener('fetch', event => {
  event.respondWith(
    caches.match(event.request)
      .then(response => {
        // 缓存命中，返回缓存资源
        if (response) {
          return response
        }

        // 网络请求
        return fetch(event.request).then(response => {
          // 检查是否是有效响应
          if (!response || response.status !== 200 || response.type !== 'basic') {
            return response
          }

          // 克隆响应
          const responseToCache = response.clone()

          caches.open(CACHE_NAME)
            .then(cache => {
              cache.put(event.request, responseToCache)
            })

          return response
        })
      })
  )
})
```

### Web App Manifest
```json
{
  "name": "掐丝珐琅馆综合管理系统",
  "short_name": "珐琅馆管理",
  "description": "掐丝珐琅馆的综合管理解决方案",
  "start_url": "/cloisonne",
  "display": "standalone",
  "background_color": "#ffffff",
  "theme_color": "#3B82F6",
  "orientation": "portrait-primary",
  "icons": [
    {
      "src": "/icons/icon-72x72.png",
      "sizes": "72x72",
      "type": "image/png"
    },
    {
      "src": "/icons/icon-96x96.png",
      "sizes": "96x96",
      "type": "image/png"
    },
    {
      "src": "/icons/icon-128x128.png",
      "sizes": "128x128",
      "type": "image/png"
    },
    {
      "src": "/icons/icon-144x144.png",
      "sizes": "144x144",
      "type": "image/png"
    },
    {
      "src": "/icons/icon-152x152.png",
      "sizes": "152x152",
      "type": "image/png"
    },
    {
      "src": "/icons/icon-192x192.png",
      "sizes": "192x192",
      "type": "image/png"
    },
    {
      "src": "/icons/icon-384x384.png",
      "sizes": "384x384",
      "type": "image/png"
    },
    {
      "src": "/icons/icon-512x512.png",
      "sizes": "512x512",
      "type": "image/png"
    }
  ]
}
```

---

## 🧪 测试策略

### 响应式测试
```javascript
// tests/responsive.spec.js
describe('响应式设计测试', () => {
  const viewports = [
    { name: 'Mobile', width: 375, height: 667 },
    { name: 'Tablet', width: 768, height: 1024 },
    { name: 'Desktop', width: 1920, height: 1080 }
  ]

  viewports.forEach(viewport => {
    describe(`${viewport.name} 视图`, () => {
      beforeEach(() => {
        cy.viewport(viewport.width, viewport.height)
        cy.visit('/cloisonne')
      })

      it('应该正确显示导航', () => {
        if (viewport.width < 768) {
          cy.get('.mobile-nav').should('be.visible')
          cy.get('.desktop-nav').should('not.exist')
        } else {
          cy.get('.desktop-nav').should('be.visible')
          cy.get('.mobile-nav').should('not.exist')
        }
      })

      it('应该正确显示网格布局', () => {
        cy.get('.metric-card').should('be.visible')

        if (viewport.width >= 1200) {
          cy.get('.ant-row .ant-col').should('have.length', 4)
        } else if (viewport.width >= 768) {
          cy.get('.ant-row .ant-col').should('have.length', 2)
        } else {
          cy.get('.ant-row .ant-col').should('have.length', 1)
        }
      })
    })
  })
})
```

### 触摸交互测试
```javascript
// tests/touch.spec.js
describe('触摸交互测试', () => {
  beforeEach(() => {
    cy.viewport('iphone-6')
    cy.visit('/cloisonne/pos')
  })

  it('应该支持滑动切换', () => {
    cy.get('.products-section')
      .trigger('touchstart', { touches: [{ clientX: 100, clientY: 100 }] })
      .trigger('touchmove', { touches: [{ clientX: 200, clientY: 100 }] })
      .trigger('touchend')

    cy.get('.cart-section').should('be.visible')
  })

  it('应该支持长按操作', () => {
    cy.get('.product-card').first()
      .trigger('touchstart')
      .wait(600)
      .trigger('touchend')

    cy.get('.context-menu').should('be.visible')
  })
})
```

---

*设计文档版本: v1.0*
*最后更新: 2025-01-22*
