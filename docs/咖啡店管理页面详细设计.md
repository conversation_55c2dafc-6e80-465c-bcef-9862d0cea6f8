# 掐丝珐琅馆咖啡店管理页面详细设计

## 📋 设计概述

咖啡店管理页面是掐丝珐琅馆综合管理模块的重要组成部分，主要用于日常销售数据录入、图片凭证上传和历史记录查询，为咖啡店的日常运营提供数字化管理支持。

---

## 🎨 整体布局设计

### 页面结构
```
┌─────────────────────────────────────────────────────────────┐
│ 页面标题: 咖啡店管理                                         │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 当前值班: 张三 | 早班 8:00-16:00 | 📞 联系             │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────┬─────────────────────────────────┐   │
│ │                     │                                 │   │
│ │   日销售数据录入     │        今日销售概览             │   │
│ │                     │                                 │   │
│ └─────────────────────┴─────────────────────────────────┘   │
├─────────────────────────────────────────────────────────────┤
│                    历史销售记录查询                          │
└─────────────────────────────────────────────────────────────┘
```

### Vue.js主组件实现
```vue
<template>
  <div class="coffee-shop-management">
    <!-- 页面头部 -->
    <coffee-header title="咖啡店管理" />
    
    <!-- 当前值班信息 -->
    <current-shift-info 
      :shift-info="currentShiftInfo"
      class="shift-info-section" />
    
    <!-- 主要内容区域 -->
    <a-row :gutter="24" class="main-content">
      <!-- 销售录入区域 -->
      <a-col :xs="24" :lg="14">
        <sales-entry-form 
          :loading="submitting"
          @submit="handleSalesSubmit" />
      </a-col>
      
      <!-- 今日概览区域 -->
      <a-col :xs="24" :lg="10">
        <a-space direction="vertical" size="large" style="width: 100%">
          <today-sales-overview 
            :today-data="todaySalesData" />
          
          <recent-uploads 
            :recent-images="recentImages" />
        </a-space>
      </a-col>
    </a-row>
    
    <!-- 历史记录区域 -->
    <sales-history-panel 
      :sales-history="salesHistory"
      @query="handleHistoryQuery"
      @view-detail="handleViewDetail" />
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import CoffeeHeader from './components/CoffeeHeader.vue'
import CurrentShiftInfo from './components/CurrentShiftInfo.vue'
import SalesEntryForm from './components/SalesEntryForm.vue'
import TodaySalesOverview from './components/TodaySalesOverview.vue'
import RecentUploads from './components/RecentUploads.vue'
import SalesHistoryPanel from './components/SalesHistoryPanel.vue'

export default {
  name: 'CoffeeManagement',
  
  components: {
    CoffeeHeader,
    CurrentShiftInfo,
    SalesEntryForm,
    TodaySalesOverview,
    RecentUploads,
    SalesHistoryPanel
  },
  
  data() {
    return {
      submitting: false
    }
  },
  
  computed: {
    ...mapState('cloisonne', [
      'currentShiftInfo',
      'todaySalesData',
      'recentImages',
      'salesHistory'
    ])
  },
  
  async created() {
    await this.loadCoffeeData()
  },
  
  methods: {
    ...mapActions('cloisonne', [
      'fetchCurrentShift',
      'fetchTodaySales',
      'saveSalesRecord',
      'fetchSalesHistory'
    ]),
    
    async loadCoffeeData() {
      try {
        await Promise.all([
          this.fetchCurrentShift(),
          this.fetchTodaySales(),
          this.fetchSalesHistory({ limit: 10 })
        ])
      } catch (error) {
        this.$message.error('加载咖啡店数据失败')
      }
    },
    
    async handleSalesSubmit(salesData) {
      this.submitting = true
      try {
        await this.saveSalesRecord(salesData)
        this.$message.success('销售记录保存成功')
        await this.loadCoffeeData() // 刷新数据
      } catch (error) {
        this.$message.error('保存销售记录失败')
      } finally {
        this.submitting = false
      }
    },
    
    async handleHistoryQuery(queryParams) {
      try {
        await this.fetchSalesHistory(queryParams)
      } catch (error) {
        this.$message.error('查询历史记录失败')
      }
    },
    
    handleViewDetail(record) {
      // 查看详细记录
      this.$router.push(`/cloisonne/coffee/detail/${record.id}`)
    }
  }
}
</script>

<style lang="less" scoped>
.coffee-shop-management {
  .shift-info-section {
    margin-bottom: 24px;
  }
  
  .main-content {
    margin-bottom: 32px;
  }
}
</style>
```

---

## 👥 当前值班信息组件

### CurrentShiftInfo 值班信息组件
```vue
<template>
  <a-card class="current-shift-info" :bordered="false">
    <div class="shift-content">
      <div class="shift-avatar">
        <a-avatar 
          :size="64"
          :src="shiftInfo.avatar || defaultAvatar" />
        <a-badge 
          :status="shiftInfo.status === 'online' ? 'success' : 'default'"
          :text="shiftInfo.status === 'online' ? '在岗' : '离岗'" />
      </div>
      
      <div class="shift-details">
        <h3 class="employee-name">{{ shiftInfo.employeeName || '暂无值班人员' }}</h3>
        <div class="shift-info">
          <a-tag :color="getShiftColor(shiftInfo.shift)">
            {{ shiftInfo.shift || '未排班' }}
          </a-tag>
          <span class="shift-time">{{ getShiftTime(shiftInfo.shift) }}</span>
        </div>
        <div class="contact-info" v-if="shiftInfo.phone">
          <a-button 
            type="link" 
            icon="phone"
            @click="handleContact">
            {{ shiftInfo.phone }}
          </a-button>
        </div>
      </div>
      
      <div class="shift-actions">
        <a-space direction="vertical">
          <a-button 
            type="primary" 
            size="small"
            @click="handleQuickEntry">
            快速录入
          </a-button>
          <a-button 
            size="small"
            @click="handleChangeShift">
            交接班
          </a-button>
        </a-space>
      </div>
    </div>
  </a-card>
</template>

<script>
export default {
  name: 'CurrentShiftInfo',
  
  props: {
    shiftInfo: {
      type: Object,
      default: () => ({})
    }
  },
  
  computed: {
    defaultAvatar() {
      const name = this.shiftInfo.employeeName || '未知'
      return `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=3B82F6&color=fff&size=128`
    }
  },
  
  methods: {
    getShiftColor(shift) {
      const colors = {
        '早班': 'blue',
        '中班': 'purple',
        '晚班': 'green',
        '夜班': 'orange',
        '全天': 'red'
      }
      return colors[shift] || 'default'
    },
    
    getShiftTime(shift) {
      const times = {
        '早班': '08:00-16:00',
        '中班': '12:00-20:00',
        '晚班': '16:00-24:00',
        '夜班': '00:00-08:00',
        '全天': '08:00-24:00'
      }
      return times[shift] || ''
    },
    
    handleContact() {
      if (this.shiftInfo.phone) {
        window.open(`tel:${this.shiftInfo.phone}`)
      }
    },
    
    handleQuickEntry() {
      this.$emit('quick-entry')
    },
    
    handleChangeShift() {
      this.$emit('change-shift')
    }
  }
}
</script>

<style lang="less" scoped>
.current-shift-info {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  
  .shift-content {
    display: flex;
    align-items: center;
    
    .shift-avatar {
      margin-right: 24px;
      text-align: center;
      
      .ant-badge {
        margin-top: 8px;
        
        .ant-badge-status-text {
          color: white;
        }
      }
    }
    
    .shift-details {
      flex: 1;
      
      .employee-name {
        margin: 0 0 8px 0;
        color: white;
        font-size: 20px;
      }
      
      .shift-info {
        margin-bottom: 8px;
        
        .shift-time {
          margin-left: 8px;
          opacity: 0.9;
        }
      }
      
      .contact-info {
        .ant-btn-link {
          color: white;
          padding: 0;
          
          &:hover {
            color: rgba(255, 255, 255, 0.8);
          }
        }
      }
    }
    
    .shift-actions {
      .ant-btn {
        border-color: rgba(255, 255, 255, 0.3);
        
        &.ant-btn-primary {
          background: rgba(255, 255, 255, 0.2);
          border-color: rgba(255, 255, 255, 0.3);
          
          &:hover {
            background: rgba(255, 255, 255, 0.3);
          }
        }
        
        &:not(.ant-btn-primary) {
          color: white;
          background: transparent;
          
          &:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.4);
          }
        }
      }
    }
  }
}
</style>
```

---

## 📝 销售录入表单组件

### SalesEntryForm 销售录入表单
```vue
<template>
  <a-card 
    title="日销售数据录入"
    class="sales-entry-form"
    :bordered="false">
    
    <template #extra>
      <a-button 
        type="primary" 
        :loading="loading"
        @click="handleSubmit">
        <a-icon type="save" />
        保存记录
      </a-button>
    </template>
    
    <a-form 
      :form="form"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
      @submit.prevent="handleSubmit">
      
      <!-- 基本信息 -->
      <a-form-item label="录入日期">
        <a-date-picker 
          v-decorator="['date', { 
            initialValue: moment(),
            rules: [{ required: true, message: '请选择日期' }]
          }]"
          style="width: 100%"
          :disabled-date="disabledDate" />
      </a-form-item>
      
      <a-form-item label="销售金额">
        <a-input-number
          v-decorator="['revenue', {
            rules: [
              { required: true, message: '请输入销售金额' },
              { type: 'number', min: 0, message: '金额不能为负数' }
            ]
          }]"
          :min="0"
          :precision="2"
          placeholder="请输入今日销售总额"
          style="width: 100%">
          <template #addonBefore>¥</template>
        </a-input-number>
      </a-form-item>
      
      <a-form-item label="订单数量">
        <a-input-number
          v-decorator="['orderCount', {
            rules: [{ type: 'number', min: 0, message: '订单数量不能为负数' }]
          }]"
          :min="0"
          placeholder="今日订单总数"
          style="width: 100%" />
      </a-form-item>
      
      <!-- 图片上传 -->
      <a-form-item label="销售凭证">
        <image-uploader 
          v-decorator="['images']"
          :max-count="8"
          :max-size="5"
          accept="image/*"
          @change="handleImageChange"
          @preview="handleImagePreview" />
        <div class="upload-tip">
          支持上传收银截图、小票照片等凭证，最多8张，单张不超过5MB
        </div>
      </a-form-item>
      
      <!-- 特殊情况说明 -->
      <a-form-item label="特殊情况">
        <a-checkbox-group v-decorator="['specialCases']">
          <a-checkbox value="promotion">促销活动</a-checkbox>
          <a-checkbox value="event">特殊活动</a-checkbox>
          <a-checkbox value="weather">恶劣天气</a-checkbox>
          <a-checkbox value="maintenance">设备维护</a-checkbox>
        </a-checkbox-group>
      </a-form-item>
      
      <!-- 备注信息 -->
      <a-form-item label="备注说明">
        <a-textarea 
          v-decorator="['notes']"
          :rows="4"
          placeholder="请描述今日销售情况、特殊事件或需要说明的问题..."
          :maxLength="500"
          show-count />
      </a-form-item>
      
      <!-- 录入人信息 -->
      <a-form-item label="录入人员">
        <a-input 
          v-decorator="['recorder', {
            initialValue: currentUser.name
          }]"
          disabled />
      </a-form-item>
    </a-form>
  </a-card>
</template>

<script>
import moment from 'moment'
import ImageUploader from '../common/ImageUploader.vue'

export default {
  name: 'SalesEntryForm',
  
  components: {
    ImageUploader
  },
  
  props: {
    loading: { type: Boolean, default: false }
  },
  
  data() {
    return {
      form: this.$form.createForm(this),
      uploadedImages: []
    }
  },
  
  computed: {
    currentUser() {
      return this.$store.getters['user/userInfo']
    }
  },
  
  methods: {
    moment,
    
    disabledDate(current) {
      // 不能选择未来日期
      return current && current > moment().endOf('day')
    },
    
    handleSubmit() {
      this.form.validateFields((err, values) => {
        if (!err) {
          const salesData = {
            ...values,
            date: values.date.format('YYYY-MM-DD'),
            images: this.uploadedImages,
            recorderId: this.currentUser.id,
            createTime: moment().format('YYYY-MM-DD HH:mm:ss')
          }
          
          this.$emit('submit', salesData)
        }
      })
    },
    
    handleImageChange(imageList) {
      this.uploadedImages = imageList
    },
    
    handleImagePreview(image) {
      // 图片预览功能
      this.$refs.imagePreviewModal.show(image)
    },
    
    resetForm() {
      this.form.resetFields()
      this.uploadedImages = []
    }
  }
}
</script>

<style lang="less" scoped>
.sales-entry-form {
  .upload-tip {
    color: #999;
    font-size: 12px;
    margin-top: 4px;
  }
}
</style>
```

---

## 📊 今日销售概览组件

### TodaySalesOverview 今日概览组件
```vue
<template>
  <a-card 
    title="今日销售概览"
    class="today-sales-overview"
    :bordered="false">
    
    <template #extra>
      <a-button 
        type="link" 
        size="small"
        @click="handleRefresh">
        <a-icon type="reload" />
        刷新
      </a-button>
    </template>
    
    <div class="overview-content">
      <!-- 销售指标 -->
      <div class="sales-metrics">
        <div class="metric-item">
          <div class="metric-icon">
            <a-icon type="dollar" style="color: #52c41a;" />
          </div>
          <div class="metric-data">
            <div class="metric-value">¥{{ formatCurrency(todayData.revenue) }}</div>
            <div class="metric-label">今日营业额</div>
          </div>
        </div>
        
        <div class="metric-item">
          <div class="metric-icon">
            <a-icon type="shopping-cart" style="color: #1890ff;" />
          </div>
          <div class="metric-data">
            <div class="metric-value">{{ todayData.orderCount || 0 }}</div>
            <div class="metric-label">订单数量</div>
          </div>
        </div>
        
        <div class="metric-item">
          <div class="metric-icon">
            <a-icon type="team" style="color: #722ed1;" />
          </div>
          <div class="metric-data">
            <div class="metric-value">{{ todayData.customerCount || 0 }}</div>
            <div class="metric-label">服务客户</div>
          </div>
        </div>
      </div>
      
      <!-- 对比数据 -->
      <div class="comparison-data">
        <a-row :gutter="16">
          <a-col :span="12">
            <div class="comparison-item">
              <span class="comparison-label">较昨日</span>
              <span :class="['comparison-value', getComparisonClass(todayData.yesterdayComparison)]">
                <a-icon :type="getComparisonIcon(todayData.yesterdayComparison)" />
                {{ Math.abs(todayData.yesterdayComparison || 0) }}%
              </span>
            </div>
          </a-col>
          <a-col :span="12">
            <div class="comparison-item">
              <span class="comparison-label">较上周同期</span>
              <span :class="['comparison-value', getComparisonClass(todayData.weekComparison)]">
                <a-icon :type="getComparisonIcon(todayData.weekComparison)" />
                {{ Math.abs(todayData.weekComparison || 0) }}%
              </span>
            </div>
          </a-col>
        </a-row>
      </div>
      
      <!-- 快速操作 -->
      <div class="quick-actions">
        <a-button-group>
          <a-button size="small" @click="handleViewDetail">
            <a-icon type="eye" />
            查看详情
          </a-button>
          <a-button size="small" @click="handleExport">
            <a-icon type="download" />
            导出数据
          </a-button>
        </a-button-group>
      </div>
    </div>
  </a-card>
</template>

<script>
export default {
  name: 'TodaySalesOverview',
  
  props: {
    todayData: {
      type: Object,
      default: () => ({
        revenue: 0,
        orderCount: 0,
        customerCount: 0,
        yesterdayComparison: 0,
        weekComparison: 0
      })
    }
  },
  
  methods: {
    formatCurrency(amount) {
      return (amount || 0).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    },
    
    getComparisonClass(value) {
      if (value > 0) return 'positive'
      if (value < 0) return 'negative'
      return 'neutral'
    },
    
    getComparisonIcon(value) {
      if (value > 0) return 'arrow-up'
      if (value < 0) return 'arrow-down'
      return 'minus'
    },
    
    handleRefresh() {
      this.$emit('refresh')
    },
    
    handleViewDetail() {
      this.$emit('view-detail')
    },
    
    handleExport() {
      this.$emit('export')
    }
  }
}
</script>

<style lang="less" scoped>
.today-sales-overview {
  .overview-content {
    .sales-metrics {
      margin-bottom: 20px;
      
      .metric-item {
        display: flex;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
        
        .metric-icon {
          margin-right: 16px;
          font-size: 24px;
        }
        
        .metric-data {
          flex: 1;
          
          .metric-value {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin-bottom: 2px;
          }
          
          .metric-label {
            font-size: 12px;
            color: #999;
          }
        }
      }
    }
    
    .comparison-data {
      margin-bottom: 20px;
      padding: 16px;
      background: #fafafa;
      border-radius: 6px;
      
      .comparison-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .comparison-label {
          font-size: 12px;
          color: #666;
        }
        
        .comparison-value {
          font-size: 14px;
          font-weight: 500;
          
          &.positive {
            color: #52c41a;
          }
          
          &.negative {
            color: #ff4d4f;
          }
          
          &.neutral {
            color: #999;
          }
          
          .anticon {
            margin-right: 4px;
          }
        }
      }
    }
    
    .quick-actions {
      text-align: center;
    }
  }
}
</style>
```

---

## 📷 图片上传组件

### ImageUploader 图片上传组件
```vue
<template>
  <div class="image-uploader">
    <a-upload
      :file-list="fileList"
      :before-upload="beforeUpload"
      :custom-request="customUpload"
      @change="handleChange"
      @preview="handlePreview"
      @remove="handleRemove"
      list-type="picture-card"
      :accept="accept"
      :multiple="true">

      <div v-if="fileList.length < maxCount" class="upload-button">
        <a-icon :type="uploading ? 'loading' : 'plus'" />
        <div class="upload-text">
          {{ uploading ? '上传中...' : '上传图片' }}
        </div>
      </div>
    </a-upload>

    <!-- 图片预览模态框 -->
    <a-modal
      :visible="previewVisible"
      :footer="null"
      @cancel="handlePreviewCancel"
      width="800px"
      centered>

      <template #title>
        <span>图片预览 - {{ previewTitle }}</span>
      </template>

      <div class="preview-container">
        <img
          :src="previewImage"
          :alt="previewTitle"
          style="width: 100%; max-height: 500px; object-fit: contain;" />

        <div class="preview-actions">
          <a-space>
            <a-button @click="handleDownload">
              <a-icon type="download" />
              下载
            </a-button>
            <a-button @click="handleRotate">
              <a-icon type="redo" />
              旋转
            </a-button>
            <a-button danger @click="handleDeletePreview">
              <a-icon type="delete" />
              删除
            </a-button>
          </a-space>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
export default {
  name: 'ImageUploader',

  props: {
    value: { type: Array, default: () => [] },
    maxCount: { type: Number, default: 8 },
    maxSize: { type: Number, default: 5 }, // MB
    accept: { type: String, default: 'image/*' }
  },

  data() {
    return {
      fileList: [],
      uploading: false,
      previewVisible: false,
      previewImage: '',
      previewTitle: '',
      currentPreviewIndex: -1
    }
  },

  watch: {
    value: {
      handler(newVal) {
        this.fileList = newVal.map((item, index) => ({
          uid: item.uid || `${index}`,
          name: item.name || `image-${index}.jpg`,
          status: 'done',
          url: item.url,
          response: item
        }))
      },
      immediate: true
    }
  },

  methods: {
    beforeUpload(file) {
      // 文件类型检查
      const isImage = file.type.startsWith('image/')
      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }

      // 文件大小检查
      const isLtMaxSize = file.size / 1024 / 1024 < this.maxSize
      if (!isLtMaxSize) {
        this.$message.error(`图片大小不能超过 ${this.maxSize}MB!`)
        return false
      }

      // 数量检查
      if (this.fileList.length >= this.maxCount) {
        this.$message.error(`最多只能上传 ${this.maxCount} 张图片!`)
        return false
      }

      return true
    },

    async customUpload({ file, onProgress, onSuccess, onError }) {
      this.uploading = true

      try {
        // 创建FormData
        const formData = new FormData()
        formData.append('file', file)
        formData.append('type', 'coffee-sales')

        // 上传文件
        const response = await this.$http.post('/cloisonne/coffee/upload', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: (progressEvent) => {
            const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total)
            onProgress({ percent })
          }
        })

        onSuccess(response.data, file)
        this.$message.success('图片上传成功')

      } catch (error) {
        onError(error)
        this.$message.error('图片上传失败')
      } finally {
        this.uploading = false
      }
    },

    handleChange({ fileList }) {
      this.fileList = fileList

      // 过滤出成功上传的文件
      const successFiles = fileList
        .filter(file => file.status === 'done' && file.response)
        .map(file => ({
          uid: file.uid,
          name: file.name,
          url: file.response.url || file.url,
          size: file.size,
          type: file.type
        }))

      this.$emit('input', successFiles)
      this.$emit('change', successFiles)
    },

    handlePreview(file) {
      this.previewImage = file.url || file.response?.url
      this.previewTitle = file.name
      this.previewVisible = true
      this.currentPreviewIndex = this.fileList.findIndex(item => item.uid === file.uid)
    },

    handlePreviewCancel() {
      this.previewVisible = false
      this.previewImage = ''
      this.previewTitle = ''
      this.currentPreviewIndex = -1
    },

    handleRemove(file) {
      const index = this.fileList.findIndex(item => item.uid === file.uid)
      if (index > -1) {
        this.fileList.splice(index, 1)
        this.handleChange({ fileList: this.fileList })
      }
    },

    handleDownload() {
      if (this.previewImage) {
        const link = document.createElement('a')
        link.href = this.previewImage
        link.download = this.previewTitle
        link.click()
      }
    },

    handleRotate() {
      // 图片旋转功能（可选实现）
      this.$message.info('旋转功能开发中...')
    },

    handleDeletePreview() {
      if (this.currentPreviewIndex > -1) {
        const file = this.fileList[this.currentPreviewIndex]
        this.handleRemove(file)
        this.handlePreviewCancel()
      }
    }
  }
}
</script>

<style lang="less" scoped>
.image-uploader {
  .upload-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #999;

    .anticon {
      font-size: 32px;
      margin-bottom: 8px;
    }

    .upload-text {
      font-size: 14px;
    }
  }

  .preview-container {
    text-align: center;

    .preview-actions {
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid #f0f0f0;
    }
  }
}
</style>
```

---

## 📋 历史记录查询组件

### SalesHistoryPanel 历史记录面板
```vue
<template>
  <a-card
    title="历史销售记录"
    class="sales-history-panel"
    :bordered="false">

    <!-- 查询条件 -->
    <div class="query-section">
      <a-form layout="inline" @submit.prevent="handleQuery">
        <a-form-item label="日期范围">
          <a-range-picker
            v-model="queryForm.dateRange"
            :ranges="dateRangePresets"
            format="YYYY-MM-DD"
            style="width: 240px" />
        </a-form-item>

        <a-form-item label="金额范围">
          <a-input-group compact>
            <a-input-number
              v-model="queryForm.minAmount"
              placeholder="最小金额"
              :min="0"
              style="width: 100px" />
            <a-input
              style="width: 30px; text-align: center; pointer-events: none"
              placeholder="~"
              disabled />
            <a-input-number
              v-model="queryForm.maxAmount"
              placeholder="最大金额"
              :min="0"
              style="width: 100px" />
          </a-input-group>
        </a-form-item>

        <a-form-item label="录入人员">
          <a-select
            v-model="queryForm.recorder"
            placeholder="选择录入人员"
            style="width: 120px"
            allow-clear>
            <a-select-option
              v-for="user in recorderList"
              :key="user.id"
              :value="user.id">
              {{ user.name }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item>
          <a-space>
            <a-button type="primary" html-type="submit" :loading="querying">
              <a-icon type="search" />
              查询
            </a-button>
            <a-button @click="handleReset">
              <a-icon type="reload" />
              重置
            </a-button>
            <a-button @click="handleExport">
              <a-icon type="download" />
              导出
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </div>

    <!-- 记录列表 -->
    <div class="history-list">
      <a-list
        :data-source="salesHistory"
        :loading="querying"
        :pagination="pagination"
        item-layout="vertical"
        size="large">

        <template #renderItem="{ item }">
          <a-list-item>
            <template #actions>
              <a-space>
                <a @click="handleViewDetail(item)">
                  <a-icon type="eye" />
                  查看详情
                </a>
                <a @click="handleEdit(item)">
                  <a-icon type="edit" />
                  编辑
                </a>
                <a @click="handleDelete(item.id)" style="color: #ff4d4f;">
                  <a-icon type="delete" />
                  删除
                </a>
              </a-space>
            </template>

            <template #extra>
              <div class="record-images" v-if="item.images && item.images.length > 0">
                <a-avatar-group :max-count="3">
                  <a-avatar
                    v-for="(image, index) in item.images.slice(0, 3)"
                    :key="index"
                    :src="image.url"
                    shape="square"
                    size="large"
                    @click="handleImagePreview(image, item.images)" />
                </a-avatar-group>
                <span v-if="item.images.length > 3" class="more-images">
                  +{{ item.images.length - 3 }}
                </span>
              </div>
            </template>

            <a-list-item-meta>
              <template #title>
                <div class="record-title">
                  <span class="record-date">{{ formatDate(item.date) }}</span>
                  <a-tag :color="getAmountColor(item.revenue)" class="amount-tag">
                    ¥{{ formatCurrency(item.revenue) }}
                  </a-tag>
                </div>
              </template>

              <template #description>
                <div class="record-meta">
                  <a-space>
                    <span>
                      <a-icon type="shopping-cart" />
                      {{ item.orderCount || 0 }} 单
                    </span>
                    <span>
                      <a-icon type="user" />
                      {{ item.recorderName }}
                    </span>
                    <span>
                      <a-icon type="clock-circle" />
                      {{ formatDateTime(item.createTime) }}
                    </span>
                  </a-space>
                </div>
              </template>
            </a-list-item-meta>

            <div class="record-content">
              <div class="special-cases" v-if="item.specialCases && item.specialCases.length > 0">
                <a-tag
                  v-for="caseType in item.specialCases"
                  :key="caseType"
                  size="small"
                  :color="getSpecialCaseColor(caseType)">
                  {{ getSpecialCaseText(caseType) }}
                </a-tag>
              </div>

              <div class="record-notes" v-if="item.notes">
                <p>{{ item.notes }}</p>
              </div>
            </div>
          </a-list-item>
        </template>
      </a-list>
    </div>
  </a-card>
</template>

<script>
import moment from 'moment'

export default {
  name: 'SalesHistoryPanel',

  props: {
    salesHistory: { type: Array, default: () => [] }
  },

  data() {
    return {
      querying: false,
      queryForm: {
        dateRange: [moment().subtract(7, 'days'), moment()],
        minAmount: null,
        maxAmount: null,
        recorder: null
      },

      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
      },

      dateRangePresets: {
        '今天': [moment(), moment()],
        '昨天': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
        '最近7天': [moment().subtract(7, 'days'), moment()],
        '最近30天': [moment().subtract(30, 'days'), moment()],
        '本月': [moment().startOf('month'), moment().endOf('month')],
        '上月': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
      },

      recorderList: [] // 从store获取
    }
  },

  created() {
    this.loadRecorderList()
    this.handleQuery()
  },

  methods: {
    async loadRecorderList() {
      // 获取录入人员列表
      try {
        const response = await this.$http.get('/cloisonne/coffee/recorders')
        this.recorderList = response.data
      } catch (error) {
        console.error('获取录入人员列表失败:', error)
      }
    },

    handleQuery() {
      const queryParams = {
        startDate: this.queryForm.dateRange?.[0]?.format('YYYY-MM-DD'),
        endDate: this.queryForm.dateRange?.[1]?.format('YYYY-MM-DD'),
        minAmount: this.queryForm.minAmount,
        maxAmount: this.queryForm.maxAmount,
        recorder: this.queryForm.recorder,
        page: this.pagination.current,
        pageSize: this.pagination.pageSize
      }

      this.$emit('query', queryParams)
    },

    handleReset() {
      this.queryForm = {
        dateRange: [moment().subtract(7, 'days'), moment()],
        minAmount: null,
        maxAmount: null,
        recorder: null
      }
      this.pagination.current = 1
      this.handleQuery()
    },

    handleExport() {
      this.$emit('export', this.queryForm)
    },

    handleViewDetail(record) {
      this.$emit('view-detail', record)
    },

    handleEdit(record) {
      this.$emit('edit', record)
    },

    handleDelete(recordId) {
      this.$confirm({
        title: '确认删除',
        content: '确定要删除这条销售记录吗？删除后无法恢复。',
        onOk: () => {
          this.$emit('delete', recordId)
        }
      })
    },

    handleImagePreview(image, allImages) {
      this.$emit('image-preview', { image, allImages })
    },

    formatDate(date) {
      return moment(date).format('YYYY年MM月DD日')
    },

    formatDateTime(datetime) {
      return moment(datetime).format('MM-DD HH:mm')
    },

    formatCurrency(amount) {
      return (amount || 0).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    },

    getAmountColor(amount) {
      if (amount >= 1000) return 'red'
      if (amount >= 500) return 'orange'
      if (amount >= 200) return 'blue'
      return 'default'
    },

    getSpecialCaseColor(caseType) {
      const colors = {
        promotion: 'red',
        event: 'purple',
        weather: 'orange',
        maintenance: 'blue'
      }
      return colors[caseType] || 'default'
    },

    getSpecialCaseText(caseType) {
      const texts = {
        promotion: '促销活动',
        event: '特殊活动',
        weather: '恶劣天气',
        maintenance: '设备维护'
      }
      return texts[caseType] || caseType
    }
  }
}
</script>

<style lang="less" scoped>
.sales-history-panel {
  .query-section {
    margin-bottom: 24px;
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
  }

  .history-list {
    .record-title {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .record-date {
        font-size: 16px;
        font-weight: 600;
      }

      .amount-tag {
        font-weight: bold;
      }
    }

    .record-meta {
      color: #666;
      font-size: 12px;

      .anticon {
        margin-right: 4px;
      }
    }

    .record-content {
      .special-cases {
        margin-bottom: 8px;
      }

      .record-notes {
        color: #666;
        font-size: 14px;
        line-height: 1.5;
      }
    }

    .record-images {
      display: flex;
      align-items: center;

      .more-images {
        margin-left: 8px;
        color: #999;
        font-size: 12px;
      }
    }
  }
}
</style>
```

---

*设计文档版本: v1.0*
*最后更新: 2025-01-22*
