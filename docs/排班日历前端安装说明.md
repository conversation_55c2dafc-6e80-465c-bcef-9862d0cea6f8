# 排班日历前端安装说明

## 概述

本文档说明如何在jshERP项目中安装和配置排班日历功能所需的前端依赖。

**创建日期**: 2025-06-21  
**作者**: Augment Code  

## 前端依赖安装

### 1. 安装FullCalendar依赖

在jshERP-web目录下执行以下命令安装FullCalendar相关依赖：

```bash
cd jshERP-web

# 安装FullCalendar核心包
npm install @fullcalendar/vue3 @fullcalendar/core

# 安装FullCalendar插件
npm install @fullcalendar/daygrid @fullcalendar/timegrid @fullcalendar/interaction

# 安装中文语言包（如果需要）
# 注意：@fullcalendar/core 已包含中文语言包
```

### 2. 验证安装

安装完成后，检查package.json文件中是否包含以下依赖：

```json
{
  "dependencies": {
    "@fullcalendar/core": "^6.x.x",
    "@fullcalendar/vue3": "^6.x.x",
    "@fullcalendar/daygrid": "^6.x.x",
    "@fullcalendar/timegrid": "^6.x.x",
    "@fullcalendar/interaction": "^6.x.x"
  }
}
```

## 文件结构

### 已创建的文件

1. **Vue组件文件**
   - `jshERP-web/src/views/operation/ScheduleCalendar.vue` - 主要的排班日历组件

2. **数据库配置**
   - 已更新 `jsh_function` 表中的菜单配置
   - 菜单编号：100102
   - 菜单名称：掐丝珐琅馆排班
   - 访问路径：/cloisonne/schedule/calendar
   - 组件路径：/operation/ScheduleCalendar

## 路由配置

需要在前端路由配置中添加新的路由项。在路由配置文件中添加：

```javascript
{
  path: '/cloisonne/schedule/calendar',
  name: 'ScheduleCalendar',
  component: () => import('@/views/operation/ScheduleCalendar.vue'),
  meta: {
    title: '掐丝珐琅馆排班',
    requireAuth: true
  }
}
```

## API接口配置

### 后端API接口

排班日历组件使用以下API接口：

1. **获取班次列表**
   - `GET /schedule/shifts`
   - 返回所有可用的班次信息

2. **获取排班记录**
   - `GET /schedule/entries`
   - 参数：start_date, end_date, employee_id (可选), status (可选)

3. **创建排班记录**
   - `POST /schedule/entry`
   - 请求体：scheduleDate, employeeId, shiftId, status, remark

4. **删除排班记录**
   - `DELETE /schedule/entry/{id}`

5. **导出排班表**
   - `GET /schedule/export`
   - 参数：start_date, end_date, format (excel/pdf/csv)

6. **获取员工列表**
   - `GET /user/getUserList`
   - 返回可排班的员工列表

### API基础配置

确保在axios配置中正确设置了baseURL：

```javascript
// 在api/manage.js或相关配置文件中
const baseURL = process.env.VUE_APP_API_BASE_URL || 'http://localhost:9999/jshERP-boot'
```

## 功能特性

### 1. 日历视图
- **月视图**: 显示整月的排班情况
- **周视图**: 显示一周的详细排班
- **日视图**: 显示单日的排班详情
- **中文界面**: 完整的中文本地化支持

### 2. 排班操作
- **新增排班**: 点击日期或使用新增按钮创建排班
- **查看详情**: 点击排班事件查看详细信息
- **编辑排班**: 修改现有排班记录（开发中）
- **删除排班**: 删除不需要的排班记录

### 3. 数据导出
- **Excel导出**: 导出月度排班表为Excel文件
- **PDF导出**: 生成PDF格式的排班报表（开发中）
- **CSV导出**: 导出为CSV格式（开发中）

### 4. 响应式设计
- **桌面端**: 完整功能的日历界面
- **移动端**: 适配移动设备的响应式布局
- **平板端**: 优化的中等屏幕显示

## 样式自定义

### 主题颜色

组件使用了与jshERP一致的设计风格：

```less
// 主要颜色
$primary-color: #1890ff;
$success-color: #52c41a;
$warning-color: #faad14;
$error-color: #f5222d;

// 背景颜色
$background-color: #f0f2f5;
$component-background: #fff;
```

### 自定义样式

可以通过修改组件中的样式来自定义外观：

```less
.schedule-full-calendar {
  :deep(.fc-event) {
    // 自定义事件样式
    border-radius: 4px;
    font-size: 12px;
  }
  
  :deep(.fc-day-today) {
    // 自定义今天的背景色
    background-color: #e6f7ff !important;
  }
}
```

## 故障排查

### 常见问题

1. **FullCalendar不显示**
   - 检查是否正确安装了所有依赖
   - 确认Vue版本兼容性（需要Vue 3.x）

2. **API请求失败**
   - 检查后端服务是否正常运行
   - 确认API路径和参数是否正确

3. **样式显示异常**
   - 检查CSS样式是否正确加载
   - 确认less-loader配置是否正确

### 调试方法

1. **开启开发者工具**
   ```javascript
   // 在组件中添加调试信息
   console.log('Calendar options:', this.calendarOptions)
   console.log('Schedule data:', this.scheduleList)
   ```

2. **检查网络请求**
   - 使用浏览器开发者工具查看API请求
   - 确认请求和响应数据格式

## 后续开发计划

### 待实现功能

1. **编辑排班功能**: 完善排班记录的编辑功能
2. **批量操作**: 支持批量创建、修改、删除排班
3. **排班模板**: 创建和应用排班模板
4. **统计报表**: 员工工作时长统计和分析
5. **移动端优化**: 进一步优化移动端用户体验

### 性能优化

1. **数据缓存**: 实现前端数据缓存机制
2. **懒加载**: 按需加载排班数据
3. **虚拟滚动**: 处理大量排班数据的性能优化

## 技术支持

如遇到问题，请参考：
1. FullCalendar官方文档：https://fullcalendar.io/docs
2. Vue 3官方文档：https://v3.vuejs.org/
3. Ant Design Vue文档：https://antdv.com/
4. jshERP项目文档和Issue跟踪
