# jshERP 版本迭代更新操作手册

**版本**: v2.0
**创建日期**: 2025-06-25
**适用版本**: jshERP 3.5+
**维护者**: Augment Code

---

## 📋 目录

- [1. 概述和说明](#1-概述和说明)
- [2. 环境和工具准备](#2-环境和工具准备)
- [3. 更新前准备工作](#3-更新前准备工作)
- [4. 本地开发和测试](#4-本地开发和测试)
- [5. 构建部署包](#5-构建部署包)
- [6. 服务器更新部署](#6-服务器更新部署)
- [7. 更新后验证](#7-更新后验证)
- [8. 不同更新场景](#8-不同更新场景)
- [9. 回滚和应急处理](#9-回滚和应急处理)
- [10. 最佳实践和注意事项](#10-最佳实践和注意事项)
- [11. 常见问题和解决方案](#11-常见问题和解决方案)
- [12. 附录](#12-附录)

---

## 1. 概述和说明

### 1.1 文档目的

本操作手册旨在为jshERP系统的版本迭代更新提供标准化、安全可靠的操作流程。通过本手册，开发者可以：

- 安全地进行系统版本更新
- 最小化更新过程中的风险
- 确保更新后系统的稳定性
- 快速处理更新过程中的问题

### 1.2 适用范围

**适用人员**：
- jshERP系统开发者
- 系统运维人员
- 具备基础Linux知识的技术人员

**适用环境**：
- 开发环境：macOS + Docker
- 生产环境：CentOS 7.5+ + 宝塔面板
- 测试环境：标准Linux环境

### 1.3 技术栈说明

**前端技术栈**：
- Vue.js 2.7.16
- Ant Design Vue 1.5.2
- Vue Router 3.0.1
- Vuex 3.1.0
- Axios 0.18.0

**后端技术栈**：
- Spring Boot 2.x
- MyBatis + MyBatis Plus
- MySQL 5.7.33
- Redis 6.2.1
- JWT Token认证

**部署环境**：
- 操作系统：CentOS 7.5+
- Web服务器：Nginx 1.16+
- Java环境：JDK 1.8
- 容器化：Docker + Docker Compose（开发环境）

### 1.4 更新类型分类

| 更新类型 | 风险级别 | 停机时间 | 回滚难度 | 测试要求 |
|---------|---------|---------|---------|---------|
| 热修复更新 | 🔴 高 | < 5分钟 | 简单 | 重点测试 |
| 功能更新 | 🟡 中 | 10-30分钟 | 中等 | 全面测试 |
| 安全更新 | 🔴 高 | < 10分钟 | 简单 | 安全测试 |
| 性能优化 | 🟢 低 | 5-15分钟 | 简单 | 性能测试 |
| 大版本更新 | 🔴 高 | 30-60分钟 | 复杂 | 完整测试 |

---

## 2. 环境和工具准备

### 2.1 本地开发环境

**必需软件**：
```bash
# 检查Node.js版本（要求16.16.0+）
node --version

# 检查Java版本（要求JDK 1.8）
java -version

# 检查Maven版本（要求3.6+）
mvn --version

# 检查Docker版本
docker --version
docker-compose --version
```

**项目目录结构**：
```
/Users/<USER>/Desktop/jshERP-0612-Cursor/
├── jshERP-web/                 # 前端项目
├── jshERP-boot/                # 后端项目
├── scripts/                    # 构建脚本
│   ├── build-frontend.sh       # 前端构建脚本
│   ├── build-backend.sh        # 后端构建脚本
│   ├── build-official-package.sh # 官方部署包构建脚本
│   ├── database/               # 数据库脚本
│   ├── deploy/                 # 部署脚本
│   └── templates/              # 模板文件
├── dist/                       # 构建输出目录
└── docs/                       # 文档目录
```

### 2.2 服务器环境

**生产服务器要求**：
- 操作系统：CentOS 7.5+
- 内存：4GB+
- 磁盘：50GB+
- 网络：稳定的互联网连接

**必需服务**：
```bash
# 检查系统版本
cat /etc/redhat-release

# 检查Java环境
java -version

# 检查MySQL服务
systemctl status mysql
# 或宝塔面板环境
/www/server/mysql/bin/mysql --version

# 检查Redis服务
systemctl status redis
redis-cli ping

# 检查Nginx服务
systemctl status nginx
nginx -v
```

### 2.3 权限和访问

**服务器访问**：
```bash
# SSH密钥配置
ssh-copy-id root@your-server-ip

# 测试连接
ssh root@your-server-ip "echo 'Connection successful'"
```

**文件权限设置**：
```bash
# 设置项目目录权限
sudo chown -R root:root /home/<USER>/
sudo chmod -R 755 /home/<USER>/

# 设置日志目录权限
sudo chown -R root:root /opt/jshERP/
sudo chmod -R 755 /opt/jshERP/
```

## 3. 更新前准备工作

### 3.1 风险评估和计划

**更新影响评估**：
```bash
# 创建更新计划文档
cat > /tmp/update_plan_$(date +%Y%m%d).md << EOF
# jshERP更新计划

## 更新信息
- 更新日期: $(date)
- 更新版本: [填写目标版本]
- 更新类型: [热修复/功能更新/安全更新/性能优化]
- 预计停机时间: [填写预估时间]

## 更新内容
- [详细描述更新内容]

## 风险评估
- 数据丢失风险: [高/中/低]
- 功能影响范围: [描述影响的功能模块]
- 回滚复杂度: [简单/中等/复杂]

## 测试计划
- [列出需要测试的功能点]

## 回滚计划
- [描述回滚步骤和时间]
EOF
```

### 3.2 系统备份

**3.2.1 数据库备份**

```bash
# 创建备份目录
sudo mkdir -p /home/<USER>/backup/$(date +%Y%m%d)
cd /home/<USER>/backup/$(date +%Y%m%d)

# 完整数据库备份
mysqldump -u jsh_user -p123456 \
  --single-transaction \
  --routines \
  --triggers \
  --events \
  --hex-blob \
  --default-character-set=utf8mb4 \
  jsh_erp > jsh_erp_full_backup_$(date +%Y%m%d_%H%M%S).sql

# 验证备份文件
if [ -f "jsh_erp_full_backup_$(date +%Y%m%d_%H%M%S).sql" ]; then
    echo "✅ 数据库备份成功"
    ls -lh jsh_erp_full_backup_*.sql
else
    echo "❌ 数据库备份失败"
    exit 1
fi

# 压缩备份文件
gzip jsh_erp_full_backup_*.sql
echo "📦 备份文件已压缩"
```

**3.2.2 应用文件备份**

```bash
# 备份应用目录
cd /home/<USER>/backup/$(date +%Y%m%d)

# 备份后端应用
tar -czf jshERP-boot_backup_$(date +%Y%m%d_%H%M%S).tar.gz \
  -C /home/<USER>

# 备份前端应用
tar -czf jshERP-web_backup_$(date +%Y%m%d_%H%M%S).tar.gz \
  -C /home/<USER>

# 备份上传文件
tar -czf jshERP-upload_backup_$(date +%Y%m%d_%H%M%S).tar.gz \
  -C /opt/jshERP upload

# 备份配置文件
tar -czf jshERP-config_backup_$(date +%Y%m%d_%H%M%S).tar.gz \
  -C /home/<USER>/jshERP-boot config

echo "✅ 应用文件备份完成"
ls -lh *_backup_*.tar.gz
```

**3.2.3 系统配置备份**

```bash
# 备份Nginx配置
sudo cp /etc/nginx/nginx.conf \
  /home/<USER>/backup/$(date +%Y%m%d)/nginx_backup_$(date +%Y%m%d_%H%M%S).conf

# 备份系统服务配置（如果有自定义服务）
sudo systemctl list-unit-files | grep jsh > \
  /home/<USER>/backup/$(date +%Y%m%d)/services_backup_$(date +%Y%m%d_%H%M%S).txt

# 备份环境变量
env | grep -i jsh > \
  /home/<USER>/backup/$(date +%Y%m%d)/env_backup_$(date +%Y%m%d_%H%M%S).txt

echo "✅ 系统配置备份完成"
```

### 3.3 环境检查

**3.3.1 系统资源检查**

```bash
# 检查磁盘空间（至少保留2GB空闲空间）
df -h | grep -E "(/$|/home|/opt)"

# 检查内存使用情况
free -h

# 检查CPU负载
uptime

# 检查网络连接
ping -c 3 8.8.8.8

echo "📊 系统资源检查完成"
```

**3.3.2 服务状态检查**

```bash
# 检查jshERP服务状态
cd /home/<USER>/jshERP-boot
./start.sh status

# 检查数据库连接
mysql -u jsh_user -p123456 -e "SELECT 'Database connection OK' as status;"

# 检查Redis连接
redis-cli ping

# 检查Nginx状态
sudo systemctl status nginx

# 检查端口监听
netstat -tlnp | grep -E "(3000|9999|3306|6379)"

echo "✅ 服务状态检查完成"
```

**3.3.3 应用功能检查**

```bash
# 检查前端访问
curl -I http://localhost:3000

# 检查后端API
curl -s http://localhost:9999/jshERP-boot/user/getCurrentUser | head -5

# 检查数据库表数量
mysql -u jsh_user -p123456 -e "USE jsh_erp; SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema='jsh_erp';"

echo "✅ 应用功能检查完成"
```

### 3.4 更新通知

**3.4.1 用户通知**

```bash
# 创建维护通知
cat > /home/<USER>/jshERP-web/maintenance.html << EOF
<!DOCTYPE html>
<html>
<head>
    <title>系统维护中</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
        .notice { background: #f0f8ff; padding: 20px; border-radius: 10px; }
    </style>
</head>
<body>
    <div class="notice">
        <h1>🔧 系统维护中</h1>
        <p>系统正在进行版本更新，预计 <strong>[X] 分钟</strong> 后恢复正常</p>
        <p>维护时间：$(date)</p>
        <p>如有紧急问题，请联系技术支持</p>
    </div>
</body>
</html>
EOF

echo "📢 维护通知页面已创建"
```

---