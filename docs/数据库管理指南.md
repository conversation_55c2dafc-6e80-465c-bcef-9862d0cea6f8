# jshERP 数据库管理指南

## 概述

本指南提供了jshERP项目的数据库管理方法，包括初始化、备份、恢复等操作。

## 数据库文件位置

### Git仓库中的SQL文件
- **最新数据库**: `jshERP-boot/docs/jsh_erp_latest.sql` (117K, 2025-06-19)
  - 包含7个用户，50个物料
  - 完整的30张数据表
  - 最新的业务数据

- **历史版本**: `jshERP-boot/docs/jsh_erp_2025-06-11_06-43-53_mysql_data_pbMor.sql` (92K)
  - 包含3个用户，31个物料
  - 基础数据版本

## 管理工具

### 1. 数据库管理脚本
```bash
./scripts/database-manager.sh [选项]
```

#### 可用选项：
- `init` - 从Git仓库初始化数据库
- `backup` - 备份当前数据库
- `restore` - 从备份文件恢复
- `status` - 检查数据库状态
- `reset` - 重置到初始状态
- `help` - 显示帮助信息

#### 使用示例：
```bash
# 检查数据库状态
./scripts/database-manager.sh status

# 初始化数据库（使用最新SQL文件）
./scripts/database-manager.sh init

# 备份当前数据库
./scripts/database-manager.sh backup

# 重置数据库到初始状态
./scripts/database-manager.sh reset
```

### 2. 快速恢复脚本
```bash
./scripts/restore-database-from-git.sh
```
- 快速从Git仓库恢复数据库
- 自动备份当前数据
- 验证恢复结果

## 数据库配置

### MySQL连接信息
- **容器名称**: `jsherp-mysql-dev`
- **数据库名**: `jsh_erp`
- **用户名**: `jsh_user`
- **密码**: `123456`
- **端口**: `3306`

### 访问方式
- **phpMyAdmin**: http://localhost:8081
- **直接连接**: `mysql -h localhost -P 3306 -u jsh_user -p jsh_erp`

## 数据库结构

### 主要数据表 (30张)
- `jsh_user` - 用户表
- `jsh_material` - 物料表
- `jsh_depot` - 仓库表
- `jsh_account` - 账户表
- `jsh_function` - 功能菜单表
- `jsh_tenant` - 租户表
- `jsh_role` - 角色表
- `jsh_depot_head` - 出入库主表
- `jsh_depot_item` - 出入库明细表
- `jsh_account_head` - 财务主表
- `jsh_account_item` - 财务明细表
- 等等...

### 当前数据统计
- **用户数量**: 7个
- **物料数量**: 50个
- **数据表数量**: 30张

## 常见操作

### 数据库初始化
当系统首次部署或数据库损坏时：
```bash
./scripts/database-manager.sh init
```

### 定期备份
建议定期备份数据库：
```bash
./scripts/database-manager.sh backup
```
备份文件保存在 `database_backups/` 目录中。

### 数据恢复
从备份恢复数据：
```bash
./scripts/database-manager.sh restore
```
脚本会列出可用的备份文件供选择。

### 状态检查
检查数据库健康状态：
```bash
./scripts/database-manager.sh status
```

## MySQL MCP配置

### 配置文件
项目根目录下的 `mysql-mcp-config.json`:
```json
{
  "mcpServers": {
    "mysql": {
      "command": "uv",
      "args": ["tool", "run", "mysql_mcp_server"],
      "env": {
        "MYSQL_HOST": "localhost",
        "MYSQL_PORT": "3306",
        "MYSQL_USER": "jsh_user",
        "MYSQL_PASSWORD": "123456",
        "MYSQL_DATABASE": "jsh_erp"
      }
    }
  }
}
```

### 使用方法
1. 确保已安装MySQL MCP服务器：
   ```bash
   uv tool install mysql-mcp-server
   ```

2. 在MCP客户端中使用配置文件连接数据库

## 故障排除

### 常见问题

1. **MySQL容器未运行**
   ```bash
   docker start jsherp-mysql-dev
   ```

2. **数据库连接失败**
   - 检查容器状态
   - 验证用户名密码
   - 确认端口映射

3. **数据表为空**
   ```bash
   ./scripts/database-manager.sh init
   ```

4. **phpMyAdmin无法访问**
   - 检查容器状态：`docker ps | grep phpmyadmin`
   - 重启容器：`docker restart jsherp-phpmyadmin`

### 日志查看
```bash
# 查看MySQL容器日志
docker logs jsherp-mysql-dev

# 查看phpMyAdmin容器日志
docker logs jsherp-phpmyadmin
```

## 注意事项

1. **数据安全**: 重要操作前请先备份数据库
2. **权限管理**: 确保Docker容器有足够的权限访问数据文件
3. **版本兼容**: 使用MySQL 5.7.33版本以确保兼容性
4. **字符集**: 数据库使用UTF-8字符集，支持中文
5. **多租户**: 系统支持多租户架构，注意tenant_id字段

## 更新记录

- **2025-06-21**: 创建数据库管理工具和文档
- **2025-06-19**: 更新到最新数据库文件 (117K)
- **2025-06-11**: 基础数据库版本 (92K)
