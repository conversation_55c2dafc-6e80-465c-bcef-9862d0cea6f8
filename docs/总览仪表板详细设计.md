# 掐丝珐琅馆总览仪表板详细设计

## 📋 设计概述

总览仪表板是掐丝珐琅馆综合管理模块的核心页面，采用4x2网格布局，展示关键业务指标和实时数据，为管理者提供全面的业务概览。

---

## 🎨 整体布局设计

### 页面结构
```
┌─────────────────────────────────────────────────────────────┐
│ 页面标题: 掐丝珐琅馆总览                    [添加概要] 按钮    │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────┬─────────┬─────────┬─────────┐                   │
│ │咖啡店   │珐琅馆   │总任务   │今日值班 │  数据指标卡片区    │
│ │今日销售 │今日销售 │完成率   │人数     │                   │
│ └─────────┴─────────┴─────────┴─────────┘                   │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────┬─────────────────────────────┐   │
│ │                         │                             │   │
│ │      今日工作任务        │        今日值班信息          │   │
│ │                         │                             │   │
│ └─────────────────────────┴─────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

### Vue.js实现结构
```vue
<template>
  <div class="cloisonne-dashboard">
    <!-- 页面头部 -->
    <dashboard-header 
      title="掐丝珐琅馆总览"
      @add-summary="handleAddSummary" />
    
    <!-- 数据指标卡片区 -->
    <a-row :gutter="[24, 16]" class="metrics-section">
      <a-col :xs="24" :sm="12" :lg="6">
        <sales-metric-card
          title="咖啡店今日销售"
          :value="dashboardData.coffeeSales"
          icon="coffee"
          color="cyan"
          :trend="dashboardData.coffeeTrend" />
      </a-col>
      
      <a-col :xs="24" :sm="12" :lg="6">
        <sales-metric-card
          title="珐琅馆今日销售"
          :value="dashboardData.museumSales"
          icon="shopping-cart"
          color="purple"
          :trend="dashboardData.museumTrend" />
      </a-col>
      
      <a-col :xs="24" :sm="12" :lg="6">
        <completion-metric-card
          title="总任务完成率"
          :completed="dashboardData.completedTasks"
          :total="dashboardData.totalTasks"
          color="green" />
      </a-col>
      
      <a-col :xs="24" :sm="12" :lg="6">
        <staff-metric-card
          title="今日值班人数"
          :count="dashboardData.onDutyCount"
          color="amber" />
      </a-col>
    </a-row>
    
    <!-- 主要内容区域 -->
    <a-row :gutter="24" class="content-section">
      <a-col :xs="24" :lg="16">
        <today-tasks-panel 
          :tasks="dashboardData.todayTasks"
          @task-toggle="handleTaskToggle"
          @add-task="handleAddTask" />
      </a-col>
      
      <a-col :xs="24" :lg="8">
        <on-duty-staff-panel 
          :staff="dashboardData.onDutyStaff" />
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import DashboardHeader from './components/DashboardHeader.vue'
import SalesMetricCard from './components/SalesMetricCard.vue'
import CompletionMetricCard from './components/CompletionMetricCard.vue'
import StaffMetricCard from './components/StaffMetricCard.vue'
import TodayTasksPanel from './components/TodayTasksPanel.vue'
import OnDutyStaffPanel from './components/OnDutyStaffPanel.vue'

export default {
  name: 'DashboardOverview',
  
  components: {
    DashboardHeader,
    SalesMetricCard,
    CompletionMetricCard,
    StaffMetricCard,
    TodayTasksPanel,
    OnDutyStaffPanel
  },
  
  data() {
    return {
      loading: false
    }
  },
  
  computed: {
    ...mapState('cloisonne', ['dashboardData'])
  },
  
  async created() {
    await this.loadDashboardData()
  },
  
  methods: {
    ...mapActions('cloisonne', ['fetchDashboardData', 'updateTask']),
    
    async loadDashboardData() {
      this.loading = true
      try {
        await this.fetchDashboardData()
      } catch (error) {
        this.$message.error('加载仪表板数据失败')
      } finally {
        this.loading = false
      }
    },
    
    handleTaskToggle(taskId) {
      this.updateTask({ id: taskId, completed: !this.getTask(taskId).completed })
    },
    
    handleAddTask() {
      // 打开新增任务弹窗
      this.$refs.taskModal.show()
    },
    
    handleAddSummary() {
      this.$message.info('添加概要功能待实现')
    }
  }
}
</script>
```

---

## 📊 数据指标卡片设计

### SalesMetricCard 销售指标卡片
```vue
<template>
  <a-card 
    :class="['sales-metric-card', `gradient-${color}`]"
    :bordered="false">
    
    <div class="metric-content">
      <div class="metric-icon">
        <a-icon :type="icon" class="icon-large" />
      </div>
      
      <div class="metric-data">
        <h4 class="metric-title">{{ title }}</h4>
        <div class="metric-value">¥{{ formattedValue }}</div>
        
        <div class="metric-trend" v-if="trend !== null">
          <a-icon :type="trendIcon" />
          <span class="trend-text">{{ Math.abs(trend) }}%</span>
          <span class="trend-label">较昨日</span>
        </div>
      </div>
    </div>
  </a-card>
</template>

<script>
export default {
  name: 'SalesMetricCard',
  
  props: {
    title: { type: String, required: true },
    value: { type: Number, required: true },
    icon: { type: String, required: true },
    color: { type: String, required: true },
    trend: { type: Number, default: null }
  },
  
  computed: {
    formattedValue() {
      return this.value.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    },
    
    trendIcon() {
      if (this.trend === null) return ''
      return this.trend >= 0 ? 'arrow-up' : 'arrow-down'
    }
  }
}
</script>

<style lang="less" scoped>
.sales-metric-card {
  position: relative;
  overflow: hidden;
  color: white;
  min-height: 120px;
  
  // 渐变背景
  &.gradient-cyan {
    background: linear-gradient(135deg, #06B6D4 0%, #3B82F6 100%);
  }
  
  &.gradient-purple {
    background: linear-gradient(135deg, #8B5CF6 0%, #3B82F6 100%);
  }
  
  &.gradient-green {
    background: linear-gradient(135deg, #10B981 0%, #059669 100%);
  }
  
  &.gradient-amber {
    background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
  }
  
  // 装饰性圆形
  &::before {
    content: '';
    position: absolute;
    top: -30px;
    right: -30px;
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
  }
  
  .metric-content {
    display: flex;
    align-items: center;
    position: relative;
    z-index: 1;
    
    .metric-icon {
      margin-right: 16px;
      
      .icon-large {
        font-size: 32px;
        opacity: 0.9;
      }
    }
    
    .metric-data {
      flex: 1;
      
      .metric-title {
        margin: 0 0 8px 0;
        font-size: 14px;
        opacity: 0.9;
        font-weight: normal;
      }
      
      .metric-value {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 4px;
        line-height: 1;
      }
      
      .metric-trend {
        font-size: 12px;
        opacity: 0.8;
        
        .anticon {
          margin-right: 4px;
        }
        
        .trend-text {
          margin-right: 4px;
          font-weight: 500;
        }
      }
    }
  }
}
</style>
```

### CompletionMetricCard 完成率指标卡片
```vue
<template>
  <a-card 
    :class="['completion-metric-card', `gradient-${color}`]"
    :bordered="false">
    
    <div class="metric-content">
      <div class="completion-circle">
        <a-progress 
          type="circle" 
          :percent="completionRate"
          :width="60"
          :stroke-width="8"
          stroke-color="rgba(255, 255, 255, 0.9)"
          trail-color="rgba(255, 255, 255, 0.2)"
          :show-info="false" />
        
        <div class="circle-text">
          <div class="percentage">{{ completionRate }}%</div>
        </div>
      </div>
      
      <div class="metric-data">
        <h4 class="metric-title">{{ title }}</h4>
        <div class="completion-stats">
          <span class="completed">{{ completed }}</span>
          <span class="separator">/</span>
          <span class="total">{{ total }}</span>
          <span class="unit">项完成</span>
        </div>
      </div>
    </div>
  </a-card>
</template>

<script>
export default {
  name: 'CompletionMetricCard',
  
  props: {
    title: { type: String, required: true },
    completed: { type: Number, required: true },
    total: { type: Number, required: true },
    color: { type: String, required: true }
  },
  
  computed: {
    completionRate() {
      return this.total > 0 ? Math.round((this.completed / this.total) * 100) : 0
    }
  }
}
</script>

<style lang="less" scoped>
.completion-metric-card {
  .metric-content {
    display: flex;
    align-items: center;
    
    .completion-circle {
      position: relative;
      margin-right: 16px;
      
      .circle-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        
        .percentage {
          font-size: 16px;
          font-weight: bold;
          color: white;
        }
      }
    }
    
    .metric-data {
      flex: 1;
      
      .metric-title {
        margin: 0 0 8px 0;
        font-size: 14px;
        opacity: 0.9;
        font-weight: normal;
      }
      
      .completion-stats {
        font-size: 18px;
        
        .completed {
          font-weight: bold;
          font-size: 20px;
        }
        
        .separator {
          margin: 0 4px;
          opacity: 0.7;
        }
        
        .total {
          opacity: 0.8;
        }
        
        .unit {
          margin-left: 4px;
          font-size: 14px;
          opacity: 0.8;
        }
      }
    }
  }
}
</style>
```

---

## 📋 今日工作任务面板

### TodayTasksPanel 任务面板组件
```vue
<template>
  <a-card 
    title="今日工作"
    class="today-tasks-panel"
    :bordered="false">
    
    <template #extra>
      <a-button 
        type="link" 
        icon="plus"
        @click="handleAddTask">
        新任务
      </a-button>
    </template>
    
    <div class="tasks-container">
      <a-empty 
        v-if="tasks.length === 0"
        description="今日无特定任务"
        :image="emptyImage" />
      
      <div v-else class="tasks-list">
        <div 
          v-for="task in tasks"
          :key="task.id"
          :class="['task-item', { 'completed': task.completed }]"
          @click="handleTaskClick(task)">
          
          <a-checkbox 
            :checked="task.completed"
            @change="handleTaskToggle(task.id)"
            @click.stop />
          
          <div class="task-content">
            <span class="task-text">{{ task.description }}</span>
            
            <div class="task-meta">
              <a-tag 
                v-if="!task.completed && task.dueDate"
                color="orange"
                size="small">
                <a-icon type="clock-circle" />
                截止
              </a-tag>
              
              <span 
                v-if="task.priority"
                :class="['task-priority', `priority-${task.priority}`]">
                {{ priorityText(task.priority) }}
              </span>
            </div>
          </div>
          
          <div class="task-actions">
            <a-button 
              type="link" 
              size="small"
              icon="edit"
              @click.stop="handleEditTask(task)" />
            
            <a-button 
              type="link" 
              size="small"
              icon="delete"
              @click.stop="handleDeleteTask(task.id)" />
          </div>
        </div>
      </div>
    </div>
  </a-card>
</template>

<script>
export default {
  name: 'TodayTasksPanel',
  
  props: {
    tasks: { type: Array, default: () => [] }
  },
  
  data() {
    return {
      emptyImage: require('@/assets/images/empty-tasks.svg')
    }
  },
  
  methods: {
    handleTaskToggle(taskId) {
      this.$emit('task-toggle', taskId)
    },
    
    handleAddTask() {
      this.$emit('add-task')
    },
    
    handleTaskClick(task) {
      if (!task.completed) {
        this.handleTaskToggle(task.id)
      }
    },
    
    handleEditTask(task) {
      this.$emit('edit-task', task)
    },
    
    handleDeleteTask(taskId) {
      this.$confirm({
        title: '确认删除',
        content: '确定要删除这个任务吗？',
        onOk: () => {
          this.$emit('delete-task', taskId)
        }
      })
    },
    
    priorityText(priority) {
      const map = {
        high: '高优先级',
        medium: '中优先级',
        low: '低优先级'
      }
      return map[priority] || ''
    }
  }
}
</script>

<style lang="less" scoped>
.today-tasks-panel {
  .tasks-container {
    max-height: 400px;
    overflow-y: auto;
    
    .tasks-list {
      .task-item {
        display: flex;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
        cursor: pointer;
        transition: all 0.2s;
        
        &:hover {
          background: #fafafa;
          border-radius: 6px;
          padding-left: 8px;
          padding-right: 8px;
        }
        
        &.completed {
          opacity: 0.6;
          
          .task-text {
            text-decoration: line-through;
            color: #999;
          }
        }
        
        .ant-checkbox-wrapper {
          margin-right: 12px;
        }
        
        .task-content {
          flex: 1;
          
          .task-text {
            display: block;
            margin-bottom: 4px;
            font-size: 14px;
            line-height: 1.4;
          }
          
          .task-meta {
            display: flex;
            align-items: center;
            gap: 8px;
            
            .task-priority {
              font-size: 12px;
              padding: 2px 6px;
              border-radius: 4px;
              
              &.priority-high {
                background: #fff2f0;
                color: #ff4d4f;
              }
              
              &.priority-medium {
                background: #fff7e6;
                color: #fa8c16;
              }
              
              &.priority-low {
                background: #f6ffed;
                color: #52c41a;
              }
            }
          }
        }
        
        .task-actions {
          opacity: 0;
          transition: opacity 0.2s;
          
          .ant-btn {
            padding: 0;
            width: 24px;
            height: 24px;
          }
        }
        
        &:hover .task-actions {
          opacity: 1;
        }
      }
    }
  }
}
</style>
```

---

## 👥 今日值班信息面板

### OnDutyStaffPanel 值班人员面板
```vue
<template>
  <a-card 
    title="今日值班"
    class="on-duty-staff-panel"
    :bordered="false">
    
    <template #title>
      <span>
        <a-icon type="team" style="margin-right: 8px; color: #3B82F6;" />
        今日值班
      </span>
    </template>
    
    <div class="staff-container">
      <a-empty 
        v-if="staff.length === 0"
        description="今日无值班人员"
        :image="emptyImage" />
      
      <div v-else class="staff-list">
        <div 
          v-for="member in staff"
          :key="member.id"
          class="staff-item">
          
          <a-avatar 
            :size="48"
            :src="member.avatar || defaultAvatar(member.name)"
            class="staff-avatar" />
          
          <div class="staff-info">
            <div class="staff-name">{{ member.name }}</div>
            <div class="staff-role">{{ member.role }}</div>
            <div class="staff-shift">
              <a-tag :color="shiftColor(member.shift)">
                {{ member.shift }}
              </a-tag>
              <span class="shift-time">{{ getShiftTime(member.shift) }}</span>
            </div>
          </div>
          
          <div class="staff-actions">
            <a-button 
              type="link" 
              size="small"
              icon="phone"
              @click="handleContact(member)" />
          </div>
        </div>
      </div>
    </div>
  </a-card>
</template>

<script>
export default {
  name: 'OnDutyStaffPanel',
  
  props: {
    staff: { type: Array, default: () => [] }
  },
  
  data() {
    return {
      emptyImage: require('@/assets/images/empty-staff.svg')
    }
  },
  
  methods: {
    defaultAvatar(name) {
      return `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=3B82F6&color=fff`
    },
    
    shiftColor(shift) {
      const colors = {
        '早班': 'blue',
        '晚班': 'green',
        '全天': 'orange'
      }
      return colors[shift] || 'default'
    },
    
    getShiftTime(shift) {
      const times = {
        '早班': '08:00-16:00',
        '晚班': '16:00-24:00',
        '全天': '08:00-24:00'
      }
      return times[shift] || ''
    },
    
    handleContact(member) {
      if (member.phone) {
        window.open(`tel:${member.phone}`)
      } else {
        this.$message.info('暂无联系方式')
      }
    }
  }
}
</script>

<style lang="less" scoped>
.on-duty-staff-panel {
  .staff-container {
    .staff-list {
      .staff-item {
        display: flex;
        align-items: center;
        padding: 16px 0;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
        
        .staff-avatar {
          margin-right: 16px;
          border: 2px solid #f0f0f0;
        }
        
        .staff-info {
          flex: 1;
          
          .staff-name {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 4px;
            color: #333;
          }
          
          .staff-role {
            font-size: 14px;
            color: #666;
            margin-bottom: 6px;
          }
          
          .staff-shift {
            display: flex;
            align-items: center;
            gap: 8px;
            
            .shift-time {
              font-size: 12px;
              color: #999;
            }
          }
        }
        
        .staff-actions {
          .ant-btn {
            color: #3B82F6;
          }
        }
      }
    }
  }
}
</style>
```

---

*设计文档版本: v1.0*
*最后更新: 2025-01-22*
