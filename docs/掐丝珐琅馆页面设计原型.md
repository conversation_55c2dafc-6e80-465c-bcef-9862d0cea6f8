# 掐丝珐琅馆综合管理模块页面设计原型

## 📋 设计概述

本文档详细描述了"掐丝珐琅馆综合管理模块"四个核心页面的设计原型，包括布局结构、交互流程、组件层次和用户体验标准。

---

## 🎨 设计系统规范

### 色彩系统
```less
// 主色调
@primary-color: #3B82F6;           // 主蓝色
@primary-color-hover: #2563EB;     // 悬停蓝色
@primary-color-active: #1D4ED8;    // 激活蓝色

// 功能色彩
@success-color: #10B981;           // 成功绿色
@warning-color: #F59E0B;           // 警告橙色
@error-color: #EF4444;             // 错误红色
@info-color: #6B7280;              // 信息灰色

// 背景色彩
@body-background: #F7F8FA;         // 页面背景
@component-background: #FFFFFF;     // 组件背景
@border-color-base: #E5E7EB;       // 边框颜色
```

### 间距系统
```less
// 标准间距
@spacing-xs: 4px;                  // 超小间距
@spacing-sm: 8px;                  // 小间距
@spacing-md: 16px;                 // 中等间距
@spacing-lg: 24px;                 // 大间距
@spacing-xl: 32px;                 // 超大间距

// 页面间距
@page-padding: 24px;               // 页面内边距
@card-padding: 24px;               // 卡片内边距
@section-margin: 16px;             // 区块间距
```

### 字体系统
```less
// 字体大小
@font-size-sm: 12px;               // 小字体
@font-size-base: 14px;             // 基础字体
@font-size-lg: 16px;               // 大字体
@font-size-xl: 18px;               // 超大字体

// 标题字体
@heading-1-size: 24px;             // 一级标题
@heading-2-size: 20px;             // 二级标题
@heading-3-size: 16px;             // 三级标题
```

### 圆角系统
```less
@border-radius-sm: 4px;            // 小圆角
@border-radius-base: 6px;          // 基础圆角
@border-radius-lg: 8px;            // 大圆角
```

---

## 📱 响应式断点设计

### 断点定义
```javascript
const breakpoints = {
  mobile: '0-767px',      // 移动端
  tablet: '768-1199px',   // 平板端
  desktop: '1200px+'      // 桌面端
}
```

### 布局适配策略
- **桌面端 (≥1200px)**: 4列网格布局，完整功能展示
- **平板端 (768-1199px)**: 2列网格布局，功能适度简化
- **移动端 (<768px)**: 单列布局，核心功能优先

---

## 🏠 1. 总览仪表板页面设计

### 页面结构
```
┌─────────────────────────────────────────────────────────┐
│ 页面标题: 掐丝珐琅馆总览                                  │
├─────────────────────────────────────────────────────────┤
│ ┌─────────┬─────────┬─────────┬─────────┐                │
│ │今日工作 │值班信息 │咖啡店   │珐琅馆   │  数据卡片区     │
│ │概况     │         │销售数据 │销售数据 │                │
│ └─────────┴─────────┴─────────┴─────────┘                │
├─────────────────────────────────────────────────────────┤
│ ┌─────────────────┬─────────────────────┐                │
│ │   销售趋势图表   │   工作完成度图表     │  图表展示区     │
│ │                │                     │                │
│ └─────────────────┴─────────────────────┘                │
└─────────────────────────────────────────────────────────┘
```

### 数据卡片设计
```vue
<!-- 今日工作概况卡片 -->
<a-card :bordered="false" class="data-card">
  <div class="card-header">
    <h3>今日工作概况</h3>
    <a-icon type="calendar" class="card-icon" />
  </div>
  <div class="card-content">
    <div class="metric-number">{{ todayTasks }}</div>
    <div class="metric-label">待办任务</div>
    <a-progress :percent="completionRate" size="small" />
  </div>
  <div class="card-footer">
    <a-button type="link">查看详情</a-button>
  </div>
</a-card>
```

### 响应式布局
```vue
<a-row :gutter="[24, 16]">
  <!-- 桌面端: 4列 -->
  <a-col :xs="24" :sm="12" :lg="6" v-for="card in dataCards" :key="card.id">
    <data-card :data="card" />
  </a-col>
</a-row>
```

---

## 📅 2. 排班管理页面设计

### 页面结构
```
┌─────────────────────────────────────────────────────────┐
│ 页面标题: 排班管理  [日历视图] [列表视图] [统计视图]        │
├─────────────────────────────────────────────────────────┤
│ 月份选择器 | 快速筛选 | 批量操作                          │
├─────────────────────────────────────────────────────────┤
│                                                         │
│                主要内容区域                              │
│            (根据视图类型动态切换)                         │
│                                                         │
├─────────────────────────────────────────────────────────┤
│ 状态栏: 本月统计信息 | 操作提示                          │
└─────────────────────────────────────────────────────────┘
```

### 日历视图设计
```vue
<a-calendar 
  :fullscreen="true"
  :value="currentMonth"
  @select="handleDateSelect">
  
  <!-- 自定义日期单元格 -->
  <template #dateCellRender="{ current }">
    <div class="schedule-cell">
      <div class="date-number">{{ current.date() }}</div>
      <div class="schedule-list">
        <div 
          v-for="schedule in getSchedulesByDate(current)"
          :key="schedule.id"
          :class="['schedule-item', `shift-${schedule.shiftType}`]">
          <a-avatar :size="24" :src="schedule.employeeAvatar" />
          <span class="employee-name">{{ schedule.employeeName }}</span>
        </div>
      </div>
    </div>
  </template>
</a-calendar>
```

### 拖拽排班功能
```vue
<draggable
  v-model="scheduleList"
  group="schedule"
  @change="handleScheduleChange">
  
  <div 
    v-for="schedule in scheduleList"
    :key="schedule.id"
    class="draggable-schedule-item">
    {{ schedule.employeeName }} - {{ schedule.shiftName }}
  </div>
</draggable>
```

---

## ☕ 3. 咖啡店管理页面设计

### 页面结构
```
┌─────────────────────────────────────────────────────────┐
│ 页面标题: 咖啡店管理                                      │
├─────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────┐ │
│ │ 当前值班: 张三 | 早班 8:00-16:00 | 📞 联系           │ │
│ └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│ ┌─────────┬─────────┬─────────────────────────────────┐ │
│ │ 营业额  │ 订单数  │        图片上传区域              │ │
│ │ [____] │ [____] │                                 │ │
│ │         │         │ [📷 拍照] [📁 选择文件]         │ │
│ └─────────┴─────────┴─────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│                历史记录查询区域                          │
│ 日期选择器 + 数据列表 + 图片预览                         │
└─────────────────────────────────────────────────────────┘
```

### 销售录入表单
```vue
<a-form :form="salesForm" layout="inline">
  <a-form-item label="今日营业额" name="revenue">
    <a-input-number
      v-model:value="salesForm.revenue"
      :min="0"
      :precision="2"
      placeholder="请输入营业额"
      style="width: 200px">
      <template #addonBefore>¥</template>
    </a-input-number>
  </a-form-item>
  
  <a-form-item label="订单数量" name="orderCount">
    <a-input-number
      v-model:value="salesForm.orderCount"
      :min="0"
      placeholder="请输入订单数"
      style="width: 150px" />
  </a-form-item>
  
  <a-form-item>
    <a-button type="primary" @click="handleSubmit">保存</a-button>
  </a-form-item>
</a-form>
```

### 图片上传组件
```vue
<a-upload
  :file-list="fileList"
  :before-upload="beforeUpload"
  @change="handleUploadChange"
  list-type="picture-card"
  accept="image/*">
  
  <div v-if="fileList.length < 8">
    <a-icon type="plus" />
    <div class="ant-upload-text">上传图片</div>
  </div>
</a-upload>
```

---

## 🛒 4. POS销售页面设计

### 页面结构 (桌面端双栏布局)
```
┌─────────────────┬─────────────────────────────────────┐
│   商品选择区     │            购物车区域                │
│                │                                     │
│ ┌─────┬─────┐   │ ┌─────────────────────────────────┐ │
│ │分类1│分类2│   │ │ 商品1  x2        ¥50.00        │ │
│ └─────┴─────┘   │ ├─────────────────────────────────┤ │
│                │ │ 商品2  x1        ¥30.00        │ │
│ ┌─────────────┐ │ ├─────────────────────────────────┤ │
│ │   商品网格   │ │ │ 商品3  x3        ¥90.00        │ │
│ │             │ │ ├─────────────────────────────────┤ │
│ │ [商品卡片]   │ │ │ 小计:           ¥170.00        │ │
│ │ [商品卡片]   │ │ │ 优惠:            ¥0.00         │ │
│ │ [商品卡片]   │ │ │ 总计:           ¥170.00        │ │
│ └─────────────┘ │ ├─────────────────────────────────┤ │
│                │ │ [结算] [清空] [暂存]             │ │
│                │ └─────────────────────────────────┘ │
└─────────────────┴─────────────────────────────────────┘
```

### 商品卡片设计
```vue
<a-card 
  :hoverable="true"
  class="product-card"
  @click="addToCart(product)">
  
  <template #cover>
    <img :src="product.image" :alt="product.name" />
  </template>
  
  <a-card-meta 
    :title="product.name"
    :description="`¥${product.price}`" />
    
  <template #actions>
    <a-icon type="plus" @click.stop="addToCart(product)" />
  </template>
</a-card>
```

### 购物车组件
```vue
<div class="shopping-cart">
  <div class="cart-header">
    <h3>购物车 ({{ cartItems.length }})</h3>
    <a-button type="link" @click="clearCart">清空</a-button>
  </div>
  
  <div class="cart-items">
    <div 
      v-for="item in cartItems"
      :key="item.id"
      class="cart-item">
      
      <div class="item-info">
        <span class="item-name">{{ item.name }}</span>
        <span class="item-price">¥{{ item.price }}</span>
      </div>
      
      <div class="item-controls">
        <a-input-number
          v-model:value="item.quantity"
          :min="1"
          size="small"
          @change="updateQuantity(item)" />
        <a-button 
          type="link" 
          danger 
          @click="removeItem(item)">删除</a-button>
      </div>
    </div>
  </div>
  
  <div class="cart-footer">
    <div class="total-amount">
      总计: ¥{{ totalAmount }}
    </div>
    <a-button 
      type="primary" 
      size="large" 
      block
      @click="handleCheckout">结算</a-button>
  </div>
</div>
```

---

## 📱 移动端适配设计

### 移动端布局调整
1. **仪表板**: 卡片垂直堆叠，图表简化显示
2. **排班管理**: 默认列表视图，日历简化为周视图
3. **咖啡店管理**: 表单垂直布局，大按钮设计
4. **POS销售**: 单栏布局，底部Tab切换

### 移动端交互优化
- 增大点击区域 (最小44px)
- 支持手势操作 (滑动、长按)
- 优化输入体验 (数字键盘、自动完成)
- 简化操作流程 (减少步骤)

---

## 🎯 用户体验标准

### 加载性能
- 首屏加载时间 < 2秒
- 页面切换动画 < 300ms
- 数据更新响应 < 500ms

### 交互反馈
- 按钮点击有视觉反馈
- 表单验证实时提示
- 操作成功/失败明确提示
- 加载状态清晰显示

### 可访问性
- 支持键盘导航
- 色彩对比度符合WCAG标准
- 屏幕阅读器友好
- 多语言支持预留

---

*设计文档版本: v1.0*
*最后更新: 2025-01-22*
