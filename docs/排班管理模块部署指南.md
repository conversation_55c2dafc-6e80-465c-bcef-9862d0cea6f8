# 排班管理模块部署指南

## 概述

本指南介绍如何在jshERP系统中部署和启用排班管理模块（Week 16开发）。

## 前置条件

- jshERP系统已正常运行
- 数据库访问权限
- 管理员权限

## 部署步骤

### 1. 数据库初始化

执行数据库安装脚本：

```sql
-- 执行完整安装脚本
mysql -u [username] -p [database_name] < jshERP-boot/docs/schedule_module_complete_setup.sql
```

### 2. 重启后端服务

```bash
# 停止服务
pkill -f jshERP-boot

# 重新启动
cd jshERP-boot
mvn spring-boot:run
```

### 3. 前端访问验证

1. 登录系统
2. 在主菜单中查找"排班管理"和"咖啡店管理"
3. 点击进入模块页面

## 功能验证

### 排班管理功能

- [ ] 新增排班
- [ ] 编辑排班
- [ ] 删除排班
- [ ] 时间冲突检查
- [ ] 工资计算
- [ ] 批量操作
- [ ] 日历视图

### 咖啡店管理功能

- [ ] 销售记录
- [ ] 提成计算
- [ ] 采购管理
- [ ] 销售统计

## 权限配置

模块默认分配给超级管理员角色，如需为其他角色分配权限：

1. 进入"系统管理" → "角色管理"
2. 选择目标角色
3. 勾选排班管理相关权限
4. 保存配置

## 故障排除

### 1. 菜单不显示

- 检查功能权限是否正确分配
- 确认用户角色包含相应功能权限
- 清除浏览器缓存重新登录

### 2. API调用失败

- 检查后端服务是否正常启动
- 查看后端日志确认错误信息
- 验证数据库连接是否正常

### 3. 数据库错误

- 确认所有表已正确创建
- 检查外键约束是否正确
- 验证初始数据是否插入成功

## 联系支持

如遇到部署问题，请检查：

1. jshERP技术文档
2. 系统日志文件
3. 数据库连接状态

---

**注意事项**：
- 部署前请备份现有数据
- 建议在测试环境先行验证
- 确保数据库权限配置正确