# jshERP薪酬管理模块 TODO 清单

## 总体进度概览

- ✅ **第一阶段：前端UI重构** (已完成 100%)
- 🔄 **第二阶段：数据库设计和后端基础架构** (进行中 0%)
- ⏳ **第三阶段：核心业务逻辑开发** (待开始)
- ⏳ **第四阶段：计算引擎开发** (待开始)
- ⏳ **第五阶段：模块集成和测试** (待开始)
- ⏳ **第六阶段：部署和优化** (待开始)

---

## 第一阶段：前端UI重构 ✅ 已完成

### 薪酬档案管理
- [x] SalaryProfileList.vue 重构为jshERP标准模式
- [x] 添加JeecgListMixin混入
- [x] 优化查询区域布局（展开/收起功能）
- [x] 添加列设置和批量操作功能
- [x] SalaryProfileModal.vue 优化
- [x] 添加与用户模块的关联
- [x] 优化表单布局和字段结构
- [x] 添加响应式设计

### 薪酬配置管理
- [x] SalaryConfigModal.vue 完全重构
- [x] 固定薪酬配置（日薪标准）
- [x] 销售提成配置（多种提成类型）
- [x] 项目收入配置（讲师费、助理费）
- [x] 生产提成配置（制作费标准）
- [x] 报销配置（按次填报）

### 薪酬计算管理
- [x] SalaryCalculation.vue 标准化改造
- [x] 计算参数设置区域优化
- [x] 计算进度显示优化
- [x] 查询和操作界面标准化
- [x] 批量审批功能界面

---

## 第二阶段：数据库设计和后端基础架构 🔄 进行中

### 数据库表结构设计
- [ ] **高优先级** 创建薪酬档案表 (jsh_salary_profile)
  - [ ] 设计表结构和字段
  - [ ] 添加索引和约束
  - [ ] 创建SQL脚本
  - [ ] 执行数据库创建

- [ ] **高优先级** 创建薪酬配置表 (jsh_salary_config)
  - [ ] 设计配置表结构
  - [ ] 支持多种配置类型
  - [ ] 创建初始配置数据

- [ ] **高优先级** 创建薪酬计算记录表 (jsh_salary_calculation)
  - [ ] 设计计算记录表结构
  - [ ] 支持计算明细JSON存储
  - [ ] 添加状态管理字段

- [ ] **中优先级** 创建薪酬发放表 (jsh_salary_payment)
  - [ ] 设计发放记录表结构
  - [ ] 支持发放状态跟踪

- [ ] **中优先级** 创建薪酬查询视图
  - [ ] 设计查询优化视图
  - [ ] 支持复杂统计查询

### 后端基础架构
- [ ] **高优先级** 创建Entity实体类
  - [ ] SalaryProfile.java
  - [ ] SalaryConfig.java
  - [ ] SalaryCalculation.java
  - [ ] SalaryPayment.java

- [ ] **高优先级** 创建Mapper接口
  - [ ] SalaryProfileMapper.java + XML
  - [ ] SalaryConfigMapper.java + XML
  - [ ] SalaryCalculationMapper.java + XML
  - [ ] SalaryPaymentMapper.java + XML

- [ ] **高优先级** 创建Service服务层
  - [ ] ISalaryProfileService.java
  - [ ] SalaryProfileServiceImpl.java
  - [ ] ISalaryConfigService.java
  - [ ] SalaryConfigServiceImpl.java
  - [ ] ISalaryCalculationService.java
  - [ ] SalaryCalculationServiceImpl.java

- [ ] **高优先级** 创建Controller控制层
  - [ ] SalaryProfileController.java
  - [ ] SalaryConfigController.java
  - [ ] SalaryCalculationController.java
  - [ ] SalaryPaymentController.java

### 权限和多租户配置
- [ ] **高优先级** 配置多租户数据隔离
  - [ ] 添加tenant_id自动注入
  - [ ] 配置数据权限过滤
  - [ ] 测试数据隔离效果

- [ ] **中优先级** 配置菜单权限
  - [ ] 在jsh_function表中添加薪酬模块菜单
  - [ ] 配置角色权限分配
  - [ ] 测试权限控制

---

## 第三阶段：核心业务逻辑开发 ⏳ 待开始

### 薪酬档案管理功能
- [ ] **高优先级** 薪酬档案CRUD功能
  - [ ] 新增薪酬档案API
  - [ ] 更新薪酬档案API
  - [ ] 删除薪酬档案API
  - [ ] 查询薪酬档案列表API
  - [ ] 薪酬档案详情API

- [ ] **高优先级** 与用户模块集成
  - [ ] 获取用户列表API
  - [ ] 用户信息自动填充
  - [ ] 用户权限验证

- [ ] **中优先级** 数据验证和处理
  - [ ] 表单数据验证
  - [ ] 业务规则验证
  - [ ] 异常处理机制

### 薪酬配置管理功能
- [ ] **高优先级** 薪酬配置CRUD功能
  - [ ] 获取薪酬配置API
  - [ ] 保存薪酬配置API
  - [ ] 配置历史记录
  - [ ] 配置版本管理

- [ ] **中优先级** 配置验证和默认值
  - [ ] 配置数据验证
  - [ ] 默认配置初始化
  - [ ] 配置备份和恢复

### 基础数据管理
- [ ] **中优先级** 部门数据管理
  - [ ] 部门列表API
  - [ ] 部门层级管理

- [ ] **中优先级** 职位数据管理
  - [ ] 职位列表API
  - [ ] 职位权限配置

---

## 第四阶段：计算引擎开发 ⏳ 待开始

### 薪酬计算引擎核心
- [ ] **高优先级** 计算引擎架构设计
  - [ ] 计算引擎接口设计
  - [ ] 计算规则抽象
  - [ ] 计算结果数据结构

- [ ] **高优先级** 固定薪酬计算
  - [ ] 日薪计算逻辑
  - [ ] 考勤数据集成
  - [ ] 工作日计算

- [ ] **高优先级** 销售提成计算
  - [ ] 咖啡店销售提成计算
  - [ ] 馆内销售提成计算
  - [ ] 艺术作品销售提成计算
  - [ ] 渠道开发提成计算

- [ ] **高优先级** 项目收入计算
  - [ ] 讲师费计算（外出/在馆）
  - [ ] 助理费计算（外出/在馆）
  - [ ] 项目数据源集成

- [ ] **高优先级** 生产提成计算
  - [ ] 掐丝点蓝制作费计算
  - [ ] 配饰制作费计算
  - [ ] 生产数据源集成

- [ ] **中优先级** 报销计算
  - [ ] 报销数据录入
  - [ ] 报销金额计算
  - [ ] 报销审批流程

### 数据源集成
- [ ] **高优先级** 销售模块数据集成
  - [ ] 咖啡店销售数据获取
  - [ ] 馆内销售数据获取
  - [ ] 销售数据格式转换

- [ ] **高优先级** 生产模块数据集成
  - [ ] 生产记录数据获取
  - [ ] 生产质量数据获取
  - [ ] 生产数据格式转换

- [ ] **高优先级** 珐琅馆模块数据集成
  - [ ] 培训记录数据获取
  - [ ] 项目记录数据获取
  - [ ] 珐琅馆数据格式转换

### 计算流程管理
- [ ] **高优先级** 批量计算功能
  - [ ] 按月份批量计算
  - [ ] 按部门批量计算
  - [ ] 按员工批量计算

- [ ] **中优先级** 计算进度跟踪
  - [ ] 计算进度实时更新
  - [ ] 计算错误处理
  - [ ] 计算结果验证

- [ ] **中优先级** 计算结果审批
  - [ ] 审批流程设计
  - [ ] 审批权限控制
  - [ ] 审批记录管理

---

## 第五阶段：模块集成和测试 ⏳ 待开始

### 前后端集成
- [ ] **高优先级** API接口联调
  - [ ] 薪酬档案接口测试
  - [ ] 薪酬配置接口测试
  - [ ] 薪酬计算接口测试
  - [ ] 薪酬发放接口测试

- [ ] **高优先级** 数据流测试
  - [ ] 完整业务流程测试
  - [ ] 数据一致性测试
  - [ ] 异常情况测试

### 系统集成测试
- [ ] **高优先级** 与现有模块集成测试
  - [ ] 用户模块集成测试
  - [ ] 销售模块集成测试
  - [ ] 生产模块集成测试
  - [ ] 珐琅馆模块集成测试

- [ ] **中优先级** 权限系统测试
  - [ ] 多租户数据隔离测试
  - [ ] 用户权限控制测试
  - [ ] 菜单权限测试

### 性能优化
- [ ] **中优先级** 计算性能优化
  - [ ] 大数据量计算优化
  - [ ] 数据库查询优化
  - [ ] 缓存机制优化

- [ ] **低优先级** 界面性能优化
  - [ ] 前端渲染优化
  - [ ] 数据加载优化
  - [ ] 用户体验优化

---

## 第六阶段：部署和优化 ⏳ 待开始

### 生产环境部署
- [ ] **高优先级** 数据库部署
  - [ ] 生产环境数据库创建
  - [ ] 数据迁移脚本
  - [ ] 数据备份策略

- [ ] **高优先级** 应用部署
  - [ ] 后端应用部署
  - [ ] 前端应用部署
  - [ ] 配置文件管理

### 用户培训和文档
- [ ] **中优先级** 用户手册编写
  - [ ] 操作手册
  - [ ] 常见问题解答
  - [ ] 视频教程

- [ ] **中优先级** 技术文档编写
  - [ ] API文档
  - [ ] 部署文档
  - [ ] 维护文档

### 持续优化
- [ ] **低优先级** 功能优化
  - [ ] 用户反馈收集
  - [ ] 功能改进
  - [ ] 新需求开发

- [ ] **低优先级** 系统监控
  - [ ] 性能监控
  - [ ] 错误监控
  - [ ] 使用情况统计

---

## 当前重点任务 (本周)

### 立即开始
1. **创建数据库表结构** (今天)
2. **建立后端基础架构** (明天)
3. **实现薪酬档案CRUD功能** (本周内)

### 本周目标
- 完成第二阶段的数据库设计
- 完成后端基础架构搭建
- 开始第三阶段的核心功能开发

---

## 备注

- **优先级说明**：
  - 高优先级：核心功能，必须完成
  - 中优先级：重要功能，尽量完成
  - 低优先级：辅助功能，时间允许时完成

- **预估工时**：
  - 第二阶段：3-5天
  - 第三阶段：5-7天
  - 第四阶段：7-10天
  - 第五阶段：3-5天
  - 第六阶段：2-3天

- **风险提醒**：
  - 数据集成复杂性较高，需要充分测试
  - 计算引擎逻辑复杂，需要仔细设计
  - 多租户数据隔离需要严格验证

---

*最后更新：2025-01-23*
