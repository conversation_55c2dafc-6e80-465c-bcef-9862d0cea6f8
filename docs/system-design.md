# jshERP移动端系统设计文档

## 文档信息

| 项目 | 信息 |
|------|------|
| 文档标题 | jshERP移动端系统设计文档 |
| 文档版本 | v1.0.0 |
| 创建日期 | 2024-12-25 |
| 作者 | Augment Agent |
| 审核者 | 待定 |
| 批准者 | 待定 |

## 变更历史

| 版本 | 日期 | 变更内容 | 作者 |
|------|------|----------|------|
| v1.0.0 | 2024-12-25 | 初始版本创建 | Augment Agent |

## 目录

1. [项目概述](#1-项目概述)
2. [架构设计](#2-架构设计)
3. [技术选型](#3-技术选型)
4. [接口设计](#4-接口设计)
5. [部署架构](#5-部署架构)
6. [质量保证](#6-质量保证)
7. [安全设计](#7-安全设计)
8. [性能设计](#8-性能设计)

## 1. 项目概述

### 1.1 项目背景

jshERP是一个成熟的企业资源规划系统，目前主要服务于桌面端用户。随着移动办公需求的增长，需要开发一个专门的移动端应用，为用户提供随时随地的业务管理能力。

### 1.2 业务价值

- **提升工作效率**：移动端随时随地处理业务
- **改善用户体验**：专为移动设备优化的界面和交互
- **扩大用户覆盖**：满足移动办公场景需求
- **增强竞争力**：完善的多端产品矩阵

### 1.3 技术目标

- **高性能**：快速响应和流畅体验
- **高可用**：99.9%以上的系统可用性
- **高安全**：企业级安全保障
- **高兼容**：与现有系统完全兼容
- **高扩展**：支持未来功能扩展

### 1.4 约束条件

- **技术约束**：必须与现有jshERP后端API兼容
- **部署约束**：在现有服务器环境中部署
- **时间约束**：4-6周完成核心功能开发
- **资源约束**：复用现有后端服务和数据库

## 2. 架构设计

### 2.1 整体架构

```mermaid
graph TB
    subgraph "客户端层"
        A[移动端浏览器]
        B[PWA应用]
    end
    
    subgraph "前端应用层"
        C[Vue 3 + Vite]
        D[Vant UI组件]
        E[Pinia状态管理]
        F[Vue Router路由]
    end
    
    subgraph "API适配层"
        G[API适配器]
        H[数据转换器]
        I[错误处理器]
    end
    
    subgraph "后端服务层"
        J[Spring Boot API]
        K[业务逻辑层]
        L[数据访问层]
    end
    
    subgraph "数据存储层"
        M[MySQL数据库]
        N[Redis缓存]
    end
    
    A --> C
    B --> C
    C --> D
    C --> E
    C --> F
    C --> G
    G --> H
    G --> I
    G --> J
    J --> K
    K --> L
    L --> M
    L --> N
```

### 2.2 分层架构设计

#### 2.2.1 表现层（Presentation Layer）
- **职责**：用户界面展示和交互处理
- **组件**：Vue组件、页面路由、用户交互
- **技术**：Vue 3、Vant UI、CSS/Less

#### 2.2.2 业务逻辑层（Business Logic Layer）
- **职责**：业务规则处理和状态管理
- **组件**：Pinia Store、Composables、业务服务
- **技术**：Pinia、Composition API、TypeScript

#### 2.2.3 数据访问层（Data Access Layer）
- **职责**：数据获取和API通信
- **组件**：API适配器、HTTP客户端、数据转换
- **技术**：Axios、适配器模式、数据映射

#### 2.2.4 基础设施层（Infrastructure Layer）
- **职责**：通用工具和基础服务
- **组件**：工具函数、常量定义、配置管理
- **技术**：TypeScript、环境配置、工具库

### 2.3 组件关系图

```mermaid
graph LR
    subgraph "Views"
        A[登录页面]
        B[仪表板]
        C[业务页面]
    end
    
    subgraph "Components"
        D[基础组件]
        E[业务组件]
        F[布局组件]
    end
    
    subgraph "Stores"
        G[用户状态]
        H[应用状态]
        I[业务状态]
    end
    
    subgraph "API"
        J[认证API]
        K[业务API]
        L[系统API]
    end
    
    A --> D
    A --> G
    A --> J
    B --> E
    B --> H
    B --> K
    C --> F
    C --> I
    C --> L
```

### 2.4 数据流设计

```mermaid
sequenceDiagram
    participant U as 用户
    participant V as Vue组件
    participant S as Pinia Store
    participant A as API适配器
    participant B as 后端API
    
    U->>V: 用户操作
    V->>S: 调用Action
    S->>A: 请求数据
    A->>B: HTTP请求
    B-->>A: 响应数据
    A-->>S: 转换后数据
    S-->>V: 更新状态
    V-->>U: 界面更新
```

## 3. 技术选型

### 3.1 前端技术栈

#### 3.1.1 Vue 3
- **选择理由**：
  - Composition API提供更好的逻辑复用
  - 更好的TypeScript支持
  - 更优的性能表现
  - 更小的包体积
- **版本**：^3.4.0
- **特性**：Composition API、Teleport、Fragments

#### 3.1.2 Vite
- **选择理由**：
  - 极快的开发服务器启动
  - 热更新性能优异
  - 现代化的构建流程
  - 优秀的插件生态
- **版本**：^5.0.0
- **配置**：开发服务器、构建优化、插件配置

#### 3.1.3 TypeScript
- **选择理由**：
  - 类型安全保障
  - 更好的IDE支持
  - 代码质量提升
  - 重构安全性
- **版本**：^5.3.0
- **配置**：严格模式、路径映射、类型检查

#### 3.1.4 Vant 4
- **选择理由**：
  - 专为移动端设计
  - Vue 3原生支持
  - 组件丰富完整
  - 定制化能力强
- **版本**：^4.8.0
- **特性**：按需引入、主题定制、TypeScript支持

### 3.2 状态管理

#### 3.2.1 Pinia
- **选择理由**：
  - Vue 3官方推荐
  - 更简洁的API
  - 更好的TypeScript支持
  - 更好的开发体验
- **版本**：^2.1.0
- **特性**：模块化、持久化、开发工具

### 3.3 构建和开发工具

#### 3.3.1 开发工具链
- **ESLint**：代码质量检查
- **Prettier**：代码格式化
- **Husky**：Git钩子管理
- **lint-staged**：暂存区代码检查

#### 3.3.2 测试工具
- **Vitest**：单元测试框架
- **Vue Test Utils**：Vue组件测试
- **Playwright**：端到端测试

## 4. 接口设计

### 4.1 API设计规范

#### 4.1.1 RESTful API规范
- **HTTP方法**：GET（查询）、POST（创建）、PUT（更新）、DELETE（删除）
- **状态码**：200（成功）、400（参数错误）、401（未授权）、403（权限不足）、404（不存在）、500（服务器错误）
- **响应格式**：统一JSON格式

#### 4.1.2 请求格式
```typescript
// 请求头
{
  "Content-Type": "application/json",
  "Authorization": "Bearer <token>",
  "X-Tenant-ID": "<tenant_id>"
}

// 请求体
{
  "data": {
    // 业务数据
  },
  "meta": {
    "timestamp": "2024-12-25T10:00:00Z",
    "requestId": "uuid"
  }
}
```

#### 4.1.3 响应格式
```typescript
// 成功响应
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    // 业务数据
  },
  "timestamp": "2024-12-25T10:00:00Z"
}

// 错误响应
{
  "success": false,
  "code": 400,
  "message": "参数错误",
  "error": {
    "type": "VALIDATION_ERROR",
    "details": [
      {
        "field": "username",
        "message": "用户名不能为空"
      }
    ]
  },
  "timestamp": "2024-12-25T10:00:00Z"
}
```

### 4.2 API适配器设计

#### 4.2.1 适配器模式
```typescript
// API适配器接口
interface ApiAdapter<T, R> {
  request(params: T): Promise<R>;
  transform(rawData: any): R;
  handleError(error: any): never;
}

// 认证API适配器
class AuthAdapter implements ApiAdapter<LoginRequest, LoginResponse> {
  async request(params: LoginRequest): Promise<LoginResponse> {
    const response = await http.post('/sys/login', params);
    return this.transform(response.data);
  }
  
  transform(rawData: any): LoginResponse {
    return {
      token: rawData.result.token,
      userInfo: {
        id: rawData.result.userInfo.id,
        username: rawData.result.userInfo.username,
        realname: rawData.result.userInfo.realname,
        avatar: rawData.result.userInfo.avatar || '/default-avatar.png'
      },
      permissions: rawData.result.permissions || []
    };
  }
  
  handleError(error: any): never {
    throw new AuthError(error.message, error.code);
  }
}
```

### 4.3 数据类型定义

#### 4.3.1 用户相关类型
```typescript
// 用户信息
interface UserInfo {
  id: string;
  username: string;
  realname: string;
  avatar: string;
  email?: string;
  phone?: string;
}

// 登录请求
interface LoginRequest {
  username: string;
  password: string;
  captcha?: string;
  rememberMe?: boolean;
}

// 登录响应
interface LoginResponse {
  token: string;
  userInfo: UserInfo;
  permissions: string[];
  expiresIn: number;
}
```

#### 4.3.2 业务相关类型
```typescript
// 分页请求
interface PageRequest {
  current: number;
  size: number;
  sort?: string;
  order?: 'asc' | 'desc';
}

// 分页响应
interface PageResponse<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

// API响应包装
interface ApiResponse<T> {
  success: boolean;
  code: number;
  message: string;
  data: T;
  timestamp: string;
}
```

## 5. 部署架构

### 5.1 部署环境设计

#### 5.1.1 服务器架构
```mermaid
graph TB
    subgraph "负载均衡层"
        A[Nginx反向代理]
    end
    
    subgraph "应用服务层"
        B[桌面端应用<br/>:8080]
        C[移动端应用<br/>:8081]
        D[后端API服务<br/>:9999]
    end
    
    subgraph "数据服务层"
        E[MySQL数据库<br/>:3306]
        F[Redis缓存<br/>:6379]
    end
    
    A --> B
    A --> C
    A --> D
    D --> E
    D --> F
```

#### 5.1.2 网络配置
```nginx
# Nginx配置示例
server {
    listen 443 ssl http2;
    server_name cms.linghuaart.com;
    
    # SSL配置
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    # 桌面端（默认）
    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # 移动端
    location /mobile/ {
        proxy_pass http://localhost:8081/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:9999/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 5.2 环境配置

#### 5.2.1 开发环境
- **服务器**：本地开发机器
- **端口**：8081（移动端）、9999（后端API）
- **数据库**：Docker容器中的MySQL
- **缓存**：Docker容器中的Redis

#### 5.2.2 生产环境
- **服务器**：CentOS 7.5 + 宝塔面板
- **端口**：443（HTTPS）、80（HTTP重定向）
- **数据库**：生产MySQL服务器
- **缓存**：生产Redis服务器

### 5.3 部署流程

#### 5.3.1 自动化部署脚本
```bash
#!/bin/bash
# deploy-mobile.sh

echo "开始部署jshERP移动端..."

# 1. 代码构建
cd /path/to/jshERP-mobile
npm run build

# 2. 备份现有版本
if [ -d "/www/wwwroot/mobile" ]; then
    mv /www/wwwroot/mobile /www/wwwroot/mobile.backup.$(date +%Y%m%d_%H%M%S)
fi

# 3. 部署新版本
mkdir -p /www/wwwroot/mobile
cp -r dist/* /www/wwwroot/mobile/

# 4. 设置权限
chown -R www:www /www/wwwroot/mobile
chmod -R 755 /www/wwwroot/mobile

# 5. 重启服务
nginx -t && nginx -s reload

echo "移动端部署完成！"
```

## 6. 质量保证

### 6.1 代码质量标准

#### 6.1.1 代码规范
- **ESLint规则**：Vue官方推荐规则 + TypeScript规则
- **Prettier配置**：统一代码格式
- **命名规范**：驼峰命名、语义化命名
- **注释规范**：JSDoc注释、关键逻辑注释

#### 6.1.2 代码审查
- **Pull Request**：所有代码变更必须经过PR
- **代码审查**：至少一人审查通过
- **自动检查**：CI/CD自动运行代码检查
- **质量门禁**：不通过检查的代码不能合并

### 6.2 测试策略

#### 6.2.1 测试金字塔
```mermaid
graph TB
    A[E2E测试<br/>10%] --> B[集成测试<br/>20%]
    B --> C[单元测试<br/>70%]
```

#### 6.2.2 测试类型
- **单元测试**：组件测试、函数测试、工具测试
- **集成测试**：API集成测试、模块集成测试
- **E2E测试**：关键业务流程测试
- **性能测试**：加载性能、响应性能

#### 6.2.3 测试覆盖率要求
- **代码覆盖率**：≥80%
- **分支覆盖率**：≥70%
- **函数覆盖率**：≥90%
- **行覆盖率**：≥85%

### 6.3 持续集成

#### 6.3.1 CI/CD流程
```mermaid
graph LR
    A[代码提交] --> B[代码检查]
    B --> C[单元测试]
    C --> D[构建应用]
    D --> E[集成测试]
    E --> F[部署测试环境]
    F --> G[E2E测试]
    G --> H[部署生产环境]
```

#### 6.3.2 质量门禁
- **代码检查通过**：ESLint、Prettier检查
- **测试通过**：所有测试用例通过
- **覆盖率达标**：测试覆盖率达到要求
- **构建成功**：应用构建无错误
- **安全扫描通过**：依赖安全扫描

## 7. 安全设计

### 7.1 认证和授权

#### 7.1.1 JWT Token认证
- **Token格式**：JWT标准格式
- **Token存储**：localStorage（开发）、HttpOnly Cookie（生产）
- **Token刷新**：自动刷新机制
- **Token过期**：自动跳转登录页

#### 7.1.2 权限控制
- **路由权限**：基于用户权限的路由访问控制
- **按钮权限**：基于权限的按钮显示控制
- **数据权限**：基于租户的数据隔离
- **API权限**：后端API权限验证

### 7.2 数据安全

#### 7.2.1 数据传输安全
- **HTTPS**：所有数据传输使用HTTPS
- **数据加密**：敏感数据加密传输
- **请求签名**：关键请求数字签名
- **防重放**：时间戳和随机数防重放

#### 7.2.2 数据存储安全
- **敏感数据加密**：密码、Token等敏感数据加密存储
- **数据脱敏**：日志中敏感数据脱敏
- **数据备份**：定期数据备份
- **数据清理**：过期数据自动清理

### 7.3 安全防护

#### 7.3.1 前端安全
- **XSS防护**：输入验证、输出编码
- **CSRF防护**：CSRF Token验证
- **点击劫持防护**：X-Frame-Options头
- **内容安全策略**：CSP头配置

#### 7.3.2 网络安全
- **防火墙**：服务器防火墙配置
- **DDoS防护**：流量清洗和限流
- **IP白名单**：管理接口IP白名单
- **访问日志**：详细的访问日志记录

## 8. 性能设计

### 8.1 前端性能优化

#### 8.1.1 加载性能
- **代码分割**：路由级别和组件级别的代码分割
- **懒加载**：图片懒加载、组件懒加载
- **资源压缩**：JavaScript、CSS、图片压缩
- **缓存策略**：浏览器缓存、CDN缓存

#### 8.1.2 运行时性能
- **虚拟滚动**：长列表虚拟滚动
- **防抖节流**：用户输入防抖节流
- **内存管理**：组件销毁时清理资源
- **渲染优化**：合理使用v-memo、computed

### 8.2 网络性能优化

#### 8.2.1 请求优化
- **请求合并**：多个请求合并为一个
- **请求缓存**：相同请求结果缓存
- **并发控制**：限制并发请求数量
- **超时控制**：请求超时时间控制

#### 8.2.2 数据优化
- **数据压缩**：响应数据Gzip压缩
- **分页加载**：大数据集分页加载
- **增量更新**：数据增量更新
- **离线缓存**：关键数据离线缓存

### 8.3 性能监控

#### 8.3.1 性能指标
- **首屏加载时间**：≤3秒
- **页面切换时间**：≤1秒
- **API响应时间**：≤2秒
- **内存使用**：≤100MB

#### 8.3.2 监控方案
- **性能监控**：Real User Monitoring（RUM）
- **错误监控**：JavaScript错误��控
- **API监控**：API调用成功率和响应时间
- **用户行为**：用户操作路径分析

---

## 附录

### A. 技术栈版本清单

| 技术 | 版本 | 说明 |
|------|------|------|
| Vue | ^3.4.0 | 前端框架 |
| Vite | ^5.0.0 | 构建工具 |
| TypeScript | ^5.3.0 | 类型系统 |
| Vant | ^4.8.0 | UI组件库 |
| Pinia | ^2.1.0 | 状态管理 |
| Vue Router | ^4.2.0 | 路由管理 |
| Axios | ^1.6.0 | HTTP客户端 |

### B. 开发环境要求

| 环境 | 版本要求 | 说明 |
|------|----------|------|
| Node.js | ≥18.0.0 | JavaScript运行时 |
| npm | ≥9.0.0 | 包管理器 |
| Git | ≥2.30.0 | 版本控制 |
| VS Code | 最新版 | 推荐IDE |

### C. 浏览器兼容性

| 浏览器 | 版本要求 | 说明 |
|--------|----------|------|
| Chrome | ≥90 | 主要支持 |
| Safari | ≥14 | iOS设备 |
| Firefox | ≥88 | 备选支持 |
| Edge | ≥90 | Windows设备 |

---

*本文档将随着项目进展持续更新和完善。*