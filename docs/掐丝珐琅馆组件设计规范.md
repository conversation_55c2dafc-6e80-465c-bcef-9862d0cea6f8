# 掐丝珐琅馆综合管理模块组件设计规范

## 📋 组件架构概述

本文档定义了掐丝珐琅馆综合管理模块的组件架构、设计规范和开发标准，确保代码的一致性和可维护性。

---

## 🏗️ 组件架构设计

### 组件层次结构
```
CloisonneManagement/
├── layouts/                    # 布局组件
│   ├── CloisonneLayout.vue    # 主布局组件
│   └── MobileLayout.vue       # 移动端布局
├── components/                 # 通用组件
│   ├── common/                # 公共组件
│   │   ├── DataCard.vue       # 数据卡片
│   │   ├── QuickActions.vue   # 快速操作
│   │   ├── StatusBadge.vue    # 状态徽章
│   │   └── ImageUploader.vue  # 图片上传
│   ├── charts/                # 图表组件
│   │   ├── TrendChart.vue     # 趋势图表
│   │   ├── PieChart.vue       # 饼图
│   │   └── BarChart.vue       # 柱状图
│   └── business/              # 业务组件
│       ├── EmployeeAvatar.vue # 员工头像
│       ├── ShiftBadge.vue     # 班次徽章
│       └── ProductCard.vue    # 商品卡片
├── views/                      # 页面组件
│   ├── Dashboard/             # 仪表板
│   │   ├── DashboardOverview.vue
│   │   ├── WorkSummaryCard.vue
│   │   ├── ShiftInfoCard.vue
│   │   └── SalesDataCard.vue
│   ├── Schedule/              # 排班管理
│   │   ├── ScheduleManagement.vue
│   │   ├── CalendarView.vue
│   │   ├── ListView.vue
│   │   └── StatisticsView.vue
│   ├── Coffee/                # 咖啡店管理
│   │   ├── CoffeeManagement.vue
│   │   ├── SalesEntry.vue
│   │   └── HistoryQuery.vue
│   └── POS/                   # POS销售
│       ├── POSSales.vue
│       ├── ProductSelector.vue
│       ├── ShoppingCart.vue
│       └── PaymentModal.vue
└── mixins/                     # 混入
    ├── responsiveMixin.js     # 响应式混入
    ├── permissionMixin.js     # 权限混入
    └── cloisonneMixin.js      # 业务混入
```

---

## 🎨 组件设计规范

### 1. 命名规范

#### 组件命名
- 使用PascalCase命名: `DataCard.vue`
- 组件名应具有描述性: `EmployeeScheduleCalendar.vue`
- 避免缩写，使用完整单词: `Employee` 而不是 `Emp`

#### 文件命名
- Vue组件: PascalCase (如: `DataCard.vue`)
- 工具函数: camelCase (如: `formatDate.js`)
- 样式文件: kebab-case (如: `data-card.less`)

#### 变量命名
```javascript
// 组件内部状态
data() {
  return {
    isLoading: false,        // 布尔值使用is/has前缀
    userList: [],            // 数组使用List后缀
    currentUser: null,       // 当前项使用current前缀
    selectedItems: [],       // 选中项使用selected前缀
    formData: {}            // 表单数据使用Data后缀
  }
}
```

### 2. Props设计规范

```javascript
// 标准Props定义
props: {
  // 基础类型
  title: {
    type: String,
    required: true,
    default: ''
  },
  
  // 数字类型
  size: {
    type: Number,
    default: 24,
    validator: (value) => value > 0
  },
  
  // 布尔类型
  loading: {
    type: Boolean,
    default: false
  },
  
  // 对象类型
  data: {
    type: Object,
    default: () => ({})
  },
  
  // 数组类型
  items: {
    type: Array,
    default: () => []
  },
  
  // 枚举类型
  type: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'primary', 'success', 'warning', 'error'].includes(value)
  }
}
```

### 3. 事件设计规范

```javascript
// 事件命名规范
methods: {
  // 处理函数使用handle前缀
  handleClick() {
    this.$emit('click', event)
  },
  
  // 内部方法使用动词开头
  updateData() {
    // 内部逻辑
  },
  
  // 验证函数使用validate前缀
  validateForm() {
    // 验证逻辑
  }
}

// 事件发射规范
this.$emit('update:value', newValue)    // 双向绑定
this.$emit('change', { oldValue, newValue })  // 值变化
this.$emit('submit', formData)          // 表单提交
this.$emit('error', errorMessage)       // 错误处理
```

---

## 🧩 核心组件设计

### 1. DataCard 数据卡片组件

```vue
<template>
  <a-card 
    :class="['data-card', `data-card--${type}`]"
    :bordered="false"
    :loading="loading">
    
    <div class="data-card__header">
      <h3 class="data-card__title">{{ title }}</h3>
      <div class="data-card__icon">
        <a-icon :type="icon" />
      </div>
    </div>
    
    <div class="data-card__content">
      <div class="data-card__value">{{ formattedValue }}</div>
      <div class="data-card__label">{{ label }}</div>
      <div class="data-card__trend" v-if="trend">
        <a-icon :type="trendIcon" :style="{ color: trendColor }" />
        <span>{{ trend }}%</span>
      </div>
    </div>
    
    <div class="data-card__footer" v-if="$slots.footer">
      <slot name="footer"></slot>
    </div>
  </a-card>
</template>

<script>
export default {
  name: 'DataCard',
  props: {
    title: { type: String, required: true },
    value: { type: [Number, String], required: true },
    label: { type: String, default: '' },
    icon: { type: String, default: 'bar-chart' },
    type: { 
      type: String, 
      default: 'default',
      validator: v => ['default', 'primary', 'success', 'warning', 'error'].includes(v)
    },
    trend: { type: Number, default: null },
    loading: { type: Boolean, default: false },
    formatter: { type: Function, default: null }
  },
  
  computed: {
    formattedValue() {
      return this.formatter ? this.formatter(this.value) : this.value
    },
    
    trendIcon() {
      return this.trend > 0 ? 'arrow-up' : 'arrow-down'
    },
    
    trendColor() {
      return this.trend > 0 ? '#52c41a' : '#ff4d4f'
    }
  }
}
</script>

<style lang="less" scoped>
.data-card {
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }
  
  &__title {
    margin: 0;
    font-size: 14px;
    color: #666;
  }
  
  &__icon {
    color: @primary-color;
    font-size: 16px;
  }
  
  &__content {
    text-align: center;
  }
  
  &__value {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
  }
  
  &__label {
    font-size: 12px;
    color: #999;
    margin-bottom: 8px;
  }
  
  &__trend {
    font-size: 12px;
    
    .anticon {
      margin-right: 4px;
    }
  }
  
  // 类型样式
  &--primary {
    border-left: 4px solid @primary-color;
  }
  
  &--success {
    border-left: 4px solid @success-color;
  }
  
  &--warning {
    border-left: 4px solid @warning-color;
  }
  
  &--error {
    border-left: 4px solid @error-color;
  }
}
</style>
```

### 2. QuickActions 快速操作组件

```vue
<template>
  <div class="quick-actions">
    <a-button-group>
      <a-button 
        v-for="action in actions"
        :key="action.key"
        :type="action.type || 'default'"
        :icon="action.icon"
        :loading="action.loading"
        :disabled="action.disabled"
        @click="handleAction(action)">
        {{ action.label }}
      </a-button>
    </a-button-group>
  </div>
</template>

<script>
export default {
  name: 'QuickActions',
  props: {
    actions: {
      type: Array,
      required: true,
      validator: (actions) => {
        return actions.every(action => 
          action.key && action.label && typeof action.handler === 'function'
        )
      }
    }
  },
  
  methods: {
    handleAction(action) {
      if (action.disabled || action.loading) return
      
      this.$emit('action', action.key)
      
      if (action.handler) {
        action.handler()
      }
    }
  }
}
</script>
```

### 3. StatusBadge 状态徽章组件

```vue
<template>
  <a-badge 
    :status="badgeStatus"
    :text="text"
    :class="['status-badge', `status-badge--${status}`]" />
</template>

<script>
export default {
  name: 'StatusBadge',
  props: {
    status: {
      type: String,
      required: true,
      validator: v => ['active', 'inactive', 'pending', 'error', 'success'].includes(v)
    },
    text: {
      type: String,
      required: true
    }
  },
  
  computed: {
    badgeStatus() {
      const statusMap = {
        active: 'processing',
        inactive: 'default',
        pending: 'warning',
        error: 'error',
        success: 'success'
      }
      return statusMap[this.status] || 'default'
    }
  }
}
</script>
```

---

## 📱 响应式组件设计

### 响应式Mixin

```javascript
// mixins/responsiveMixin.js
export default {
  data() {
    return {
      screenWidth: window.innerWidth
    }
  },
  
  computed: {
    isMobile() {
      return this.screenWidth < 768
    },
    
    isTablet() {
      return this.screenWidth >= 768 && this.screenWidth < 1200
    },
    
    isDesktop() {
      return this.screenWidth >= 1200
    },
    
    deviceType() {
      if (this.isMobile) return 'mobile'
      if (this.isTablet) return 'tablet'
      return 'desktop'
    }
  },
  
  mounted() {
    window.addEventListener('resize', this.handleResize)
  },
  
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  
  methods: {
    handleResize() {
      this.screenWidth = window.innerWidth
    }
  }
}
```

### 响应式组件示例

```vue
<template>
  <div class="responsive-grid">
    <a-row :gutter="gutter">
      <a-col 
        v-for="item in items"
        :key="item.id"
        :span="colSpan">
        <slot :item="item"></slot>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import responsiveMixin from '@/mixins/responsiveMixin'

export default {
  name: 'ResponsiveGrid',
  mixins: [responsiveMixin],
  
  props: {
    items: { type: Array, required: true },
    columns: {
      type: Object,
      default: () => ({
        mobile: 1,
        tablet: 2,
        desktop: 4
      })
    }
  },
  
  computed: {
    colSpan() {
      const cols = this.columns[this.deviceType] || 1
      return 24 / cols
    },
    
    gutter() {
      return this.isMobile ? [16, 16] : [24, 24]
    }
  }
}
</script>
```

---

## 🎯 性能优化规范

### 1. 组件懒加载
```javascript
// 路由级别懒加载
const Dashboard = () => import('@/views/cloisonne/Dashboard')

// 组件级别懒加载
components: {
  TrendChart: () => import('@/components/charts/TrendChart')
}
```

### 2. 计算属性缓存
```javascript
computed: {
  // 使用计算属性缓存复杂计算
  filteredItems() {
    return this.items.filter(item => item.status === 'active')
  },
  
  // 避免在模板中进行复杂计算
  formattedTotal() {
    return this.items.reduce((sum, item) => sum + item.amount, 0).toFixed(2)
  }
}
```

### 3. 事件防抖
```javascript
import { debounce } from 'lodash-es'

methods: {
  // 搜索防抖
  handleSearch: debounce(function(keyword) {
    this.searchKeyword = keyword
    this.loadData()
  }, 300),
  
  // 窗口大小调整防抖
  handleResize: debounce(function() {
    this.updateLayout()
  }, 100)
}
```

---

## 📝 代码质量规范

### 1. 组件文档注释
```javascript
/**
 * 数据卡片组件
 * @description 用于展示关键数据指标的卡片组件
 * <AUTHOR>
 * @date 2025-01-22
 * @example
 * <DataCard 
 *   title="今日销售额"
 *   :value="12345"
 *   label="元"
 *   icon="dollar"
 *   type="success"
 *   :trend="12.5" />
 */
export default {
  name: 'DataCard',
  // ...
}
```

### 2. 错误处理
```javascript
methods: {
  async loadData() {
    try {
      this.loading = true
      const response = await this.$http.get('/api/data')
      this.data = response.data
    } catch (error) {
      this.$message.error('数据加载失败: ' + error.message)
      console.error('Data loading error:', error)
    } finally {
      this.loading = false
    }
  }
}
```

### 3. 类型检查
```javascript
// 使用PropTypes进行运行时类型检查
import PropTypes from 'vue-types'

props: {
  data: PropTypes.object.isRequired,
  loading: PropTypes.bool.def(false),
  size: PropTypes.oneOf(['small', 'default', 'large']).def('default')
}
```

---

*组件设计规范版本: v1.0*
*最后更新: 2025-01-22*
