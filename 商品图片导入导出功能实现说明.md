# jshERP商品图片导入导出功能实现说明

## 功能概述

在jshERP商品管理模块的Excel导入导出功能基础上，增加了商品图片的批量处理能力。用户可以通过Excel表格中的"图片地址"列来管理商品图片。

## 实现方案

### 方案选择
采用**Excel中增加图片地址列**的简单直接方案，而非复杂的ZIP压缩包方案。

### 技术实现

#### 1. 导出功能增强

**修改位置**: `MaterialService.exportExcel()`方法

**主要变更**:
- 在Excel列定义中增加"图片地址"列
- 在数据填充时调用`generateImageUrl()`方法生成完整的图片访问URL

**代码变更**:
```java
// 列定义增加图片地址
String nameStr = "名称*,规格,型号,颜色,品牌,类别,基础重量(kg),保质期(天),基本单位*,副单位,基本条码*,副条码,比例,多属性," +
        "采购价,零售价,销售价,最低售价,状态*,序列号,批号,仓位货架,制造商," + otherField + ",备注,图片地址";

// 数据填充时生成图片URL
objs[27] = generateImageUrl(m.getImgName()); // 图片地址
```

#### 2. 导入功能增强

**修改位置**: `MaterialService.importExcel()`方法

**主要变更**:
- 读取Excel中第28列（图片地址列）的数据
- 调用`extractImagePath()`方法将完整URL转换为相对路径
- 将相对路径存储到Material实体的imgName字段

**代码变更**:
```java
// 读取图片地址列
String imageUrl = ExcelUtils.getContent(src, i, 27); //图片地址

// 处理图片地址，将URL转换为相对路径
String imgName = extractImagePath(imageUrl);
m.setImgName(imgName);
```

#### 3. 工具方法实现

**新增方法1**: `generateImageUrl(String imgName)`
- 功能：将数据库中的相对路径转换为完整的访问URL
- 支持多图片（逗号分隔）
- URL格式：`/systemConfig/static/{相对路径}`

**新增方法2**: `extractImagePath(String imageUrl)`
- 功能：从完整URL中提取相对路径部分
- 支持多种URL格式的解析
- 错误处理：格式不符合时记录警告但不中断处理

#### 4. 列位置调整

由于增加了图片地址列，仓库库存列的位置需要相应调整：
```java
// 原来：int col = 26 + j;
// 修改为：int col = 27 + j; // 因为增加了图片地址列，所以仓库列从28开始
```

## 使用说明

### 导出操作
1. 在商品管理页面点击"导出"按钮
2. 生成的Excel文件将包含"图片地址"列
3. 该列显示商品图片的完整访问URL，可直接在浏览器中打开查看

### 导入操作
1. 在Excel模板的"图片地址"列中填入图片的完整URL
2. 支持多个图片URL，用逗号分隔
3. 上传Excel文件进行导入
4. 系统会自动将URL转换为相对路径存储

### 图片URL格式
- **标准格式**: `/systemConfig/static/material/{tenantId}/{filename}`
- **完整URL**: `http://domain/systemConfig/static/material/{tenantId}/{filename}`
- **多图片**: 用逗号分隔多个URL

## 兼容性说明

### 向后兼容
- 现有的Excel导入导出功能完全保持兼容
- 图片地址列为新增列，不影响现有数据处理
- 空的图片地址不会影响商品数据的正常导入

### 数据库兼容
- 复用现有的`jsh_material.img_name`字段
- 字段类型：`varchar(1000)`，足够存储多个图片路径
- 存储格式：相对路径，用逗号分隔多个路径

## 错误处理

### 导出错误处理
- 如果imgName字段为空，图片地址列显示为空
- 图片路径格式错误时记录日志但不中断导出

### 导入错误处理
- 图片URL格式不符合预期时记录警告，继续处理其他数据
- 图片地址列为空时，imgName字段设置为null
- URL解析失败不会影响商品基础数据的导入

## 测试建议

### 功能测试
1. 测试导出包含图片的商品数据
2. 测试导入包含图片URL的Excel文件
3. 测试多图片的导入导出
4. 测试空图片地址的处理

### 边界测试
1. 测试超长URL的处理
2. 测试特殊字符在URL中的处理
3. 测试错误URL格式的容错处理
4. 测试大批量数据的导入导出性能

## 后续优化建议

1. **URL验证**: 可以增加URL格式的严格验证
2. **图片预览**: 在Excel中可以考虑增加图片预览功能
3. **批量上传**: 可以考虑增加图片批量上传到服务器的功能
4. **压缩优化**: 对于大图片可以考虑自动压缩处理
