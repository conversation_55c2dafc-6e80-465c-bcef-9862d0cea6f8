# 智能生产管理系统部署指南

## 📋 系统概述

智能生产管理系统是为jshERP开发的生产管理模块，提供完整的生产流程管理能力，包括：
- 掐丝点蓝制作管理
- 配饰制作管理  
- 后工任务管理
- 物流追踪管理
- 质检管理
- 工人信息管理

## 🗄️ 数据库表结构

### 1. 掐丝点蓝制作表
```sql
CREATE TABLE `jsh_cloisonne_production` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_number` varchar(50) NOT NULL COMMENT '任务编号',
  `product_id` bigint(20) DEFAULT NULL COMMENT '产品ID',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `quantity` decimal(10,2) NOT NULL COMMENT '制作数量',
  `unit_name` varchar(20) DEFAULT NULL COMMENT '单位名称',
  `worker_id` bigint(20) DEFAULT NULL COMMENT '工人ID',
  `worker_name` varchar(50) DEFAULT NULL COMMENT '工人姓名',
  `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '状态：PENDING-待开始,IN_PROGRESS-进行中,COMPLETED-已完成,CANCELLED-已取消',
  `priority` varchar(20) DEFAULT 'MEDIUM' COMMENT '优先级：URGENT-紧急,HIGH-高,MEDIUM-中,LOW-低',
  `plan_start_time` datetime DEFAULT NULL COMMENT '计划开始时间',
  `plan_end_time` datetime DEFAULT NULL COMMENT '计划完成时间',
  `actual_start_time` datetime DEFAULT NULL COMMENT '实际开始时间',
  `actual_end_time` datetime DEFAULT NULL COMMENT '实际完成时间',
  `design_requirements` text COMMENT '设计要求',
  `color_scheme` varchar(200) DEFAULT NULL COMMENT '色彩方案',
  `pattern_complexity` varchar(20) DEFAULT NULL COMMENT '图案复杂度',
  `material_requirements` text COMMENT '材料要求',
  `quality_standards` text COMMENT '质量标准',
  `labor_cost_amount` decimal(10,2) DEFAULT NULL COMMENT '人工成本金额',
  `salary_amount` decimal(10,2) DEFAULT NULL COMMENT '薪酬金额',
  `remark` text COMMENT '备注',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `delete_flag` varchar(1) NOT NULL DEFAULT '0' COMMENT '删除标记：0-正常,1-删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` bigint(20) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_number` (`task_number`),
  KEY `idx_tenant_delete` (`tenant_id`, `delete_flag`),
  KEY `idx_status` (`status`),
  KEY `idx_worker` (`worker_id`),
  KEY `idx_priority` (`priority`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='掐丝点蓝制作表';
```

### 2. 配饰制作表
```sql
CREATE TABLE `jsh_accessory_production` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_number` varchar(50) NOT NULL COMMENT '任务编号',
  `product_id` bigint(20) DEFAULT NULL COMMENT '产品ID',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `quantity` decimal(10,2) NOT NULL COMMENT '制作数量',
  `unit_name` varchar(20) DEFAULT NULL COMMENT '单位名称',
  `worker_id` bigint(20) DEFAULT NULL COMMENT '工人ID',
  `worker_name` varchar(50) DEFAULT NULL COMMENT '工人姓名',
  `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '状态：PENDING-待开始,IN_PROGRESS-进行中,COMPLETED-已完成,CANCELLED-已取消',
  `priority` varchar(20) DEFAULT 'MEDIUM' COMMENT '优先级：URGENT-紧急,HIGH-高,MEDIUM-中,LOW-低',
  `plan_start_time` datetime DEFAULT NULL COMMENT '计划开始时间',
  `plan_end_time` datetime DEFAULT NULL COMMENT '计划完成时间',
  `actual_start_time` datetime DEFAULT NULL COMMENT '实际开始时间',
  `actual_end_time` datetime DEFAULT NULL COMMENT '实际完成时间',
  `accessory_type` varchar(50) DEFAULT NULL COMMENT '配饰类型',
  `material_type` varchar(50) DEFAULT NULL COMMENT '材料类型',
  `size_specifications` varchar(100) DEFAULT NULL COMMENT '尺寸规格',
  `color_requirements` varchar(200) DEFAULT NULL COMMENT '颜色要求',
  `craftsmanship_requirements` text COMMENT '工艺要求',
  `quality_standards` text COMMENT '质量标准',
  `labor_cost_amount` decimal(10,2) DEFAULT NULL COMMENT '人工成本金额',
  `salary_amount` decimal(10,2) DEFAULT NULL COMMENT '薪酬金额',
  `remark` text COMMENT '备注',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `delete_flag` varchar(1) NOT NULL DEFAULT '0' COMMENT '删除标记：0-正常,1-删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` bigint(20) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_number` (`task_number`),
  KEY `idx_tenant_delete` (`tenant_id`, `delete_flag`),
  KEY `idx_status` (`status`),
  KEY `idx_worker` (`worker_id`),
  KEY `idx_priority` (`priority`),
  KEY `idx_accessory_type` (`accessory_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配饰制作表';
```

### 3. 生产工人表
```sql
CREATE TABLE `jsh_production_worker` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `worker_number` varchar(50) NOT NULL COMMENT '工人编号',
  `worker_name` varchar(50) NOT NULL COMMENT '工人姓名',
  `department` varchar(50) NOT NULL COMMENT '部门',
  `position` varchar(50) DEFAULT NULL COMMENT '职位',
  `skill_level` varchar(20) DEFAULT NULL COMMENT '技能等级：JUNIOR-初级,INTERMEDIATE-中级,SENIOR-高级,EXPERT-专家',
  `specialties` varchar(200) DEFAULT NULL COMMENT '专长技能',
  `experience_years` int(11) DEFAULT NULL COMMENT '工作经验年限',
  `status` varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-在职,INACTIVE-离职,SUSPENDED-停职',
  `phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `hire_date` date DEFAULT NULL COMMENT '入职日期',
  `hourly_rate` decimal(10,2) DEFAULT NULL COMMENT '时薪',
  `monthly_target` decimal(10,2) DEFAULT NULL COMMENT '月度目标',
  `current_workload` decimal(10,2) DEFAULT '0.00' COMMENT '当前工作负荷',
  `max_workload` decimal(10,2) DEFAULT NULL COMMENT '最大工作负荷',
  `quality_rating` decimal(3,2) DEFAULT '5.00' COMMENT '质量评分',
  `efficiency_rating` decimal(3,2) DEFAULT '5.00' COMMENT '效率评分',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `delete_flag` varchar(1) NOT NULL DEFAULT '0' COMMENT '删除标记：0-正常,1-删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` bigint(20) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_worker_number` (`worker_number`),
  KEY `idx_tenant_delete` (`tenant_id`, `delete_flag`),
  KEY `idx_department` (`department`),
  KEY `idx_skill_level` (`skill_level`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生产工人表';
```

### 4. 后工任务表
```sql
CREATE TABLE `jsh_post_processing_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_number` varchar(50) NOT NULL COMMENT '任务编号',
  `product_id` bigint(20) DEFAULT NULL COMMENT '产品ID',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `task_type` varchar(50) NOT NULL COMMENT '任务类型：POLISHING-抛光,PACKAGING-包装,QUALITY_CHECK-质检,LABELING-贴标,WAREHOUSING-入库',
  `task_description` text COMMENT '任务描述',
  `quantity` decimal(10,2) NOT NULL COMMENT '处理数量',
  `unit_name` varchar(20) DEFAULT NULL COMMENT '单位名称',
  `worker_id` bigint(20) DEFAULT NULL COMMENT '工人ID',
  `worker_name` varchar(50) DEFAULT NULL COMMENT '工人姓名',
  `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '状态：PENDING-待处理,IN_PROGRESS-处理中,COMPLETED-已完成,CANCELLED-已取消',
  `priority` varchar(20) DEFAULT 'MEDIUM' COMMENT '优先级：URGENT-紧急,HIGH-高,MEDIUM-中,LOW-低',
  `plan_start_time` datetime DEFAULT NULL COMMENT '计划开始时间',
  `plan_end_time` datetime DEFAULT NULL COMMENT '计划完成时间',
  `actual_start_time` datetime DEFAULT NULL COMMENT '实际开始时间',
  `actual_end_time` datetime DEFAULT NULL COMMENT '实际完成时间',
  `processing_standard` text COMMENT '处理标准',
  `quality_requirement` text COMMENT '质量要求',
  `equipment` varchar(200) DEFAULT NULL COMMENT '使用设备',
  `labor_cost_amount` decimal(10,2) DEFAULT NULL COMMENT '人工成本金额',
  `salary_amount` decimal(10,2) DEFAULT NULL COMMENT '薪酬金额',
  `remark` text COMMENT '备注',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `delete_flag` varchar(1) NOT NULL DEFAULT '0' COMMENT '删除标记：0-正常,1-删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` bigint(20) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_number` (`task_number`),
  KEY `idx_tenant_delete` (`tenant_id`, `delete_flag`),
  KEY `idx_task_type` (`task_type`),
  KEY `idx_status` (`status`),
  KEY `idx_worker` (`worker_id`),
  KEY `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='后工任务表';
```

### 5. 物流追踪表
```sql
CREATE TABLE `jsh_logistics_tracking` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tracking_number` varchar(50) NOT NULL COMMENT '物流单号',
  `work_order_id` bigint(20) DEFAULT NULL COMMENT '工单ID',
  `work_order_number` varchar(50) DEFAULT NULL COMMENT '工单号',
  `task_id` bigint(20) DEFAULT NULL COMMENT '任务ID',
  `task_number` varchar(50) DEFAULT NULL COMMENT '任务号',
  `logistics_type` varchar(50) DEFAULT NULL COMMENT '物流类型',
  `product_id` bigint(20) DEFAULT NULL COMMENT '产品ID',
  `product_name` varchar(100) DEFAULT NULL COMMENT '产品名称',
  `quantity` decimal(10,2) DEFAULT NULL COMMENT '发货数量',
  `unit_name` varchar(20) DEFAULT NULL COMMENT '单位名称',
  `from_location` varchar(200) DEFAULT NULL COMMENT '起始位置',
  `to_location` varchar(200) DEFAULT NULL COMMENT '目标位置',
  `carrier_name` varchar(100) DEFAULT NULL COMMENT '承运商名称',
  `carrier_contact` varchar(100) DEFAULT NULL COMMENT '承运商联系方式',
  `tracking_code` varchar(100) DEFAULT NULL COMMENT '追踪码',
  `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '状态：PENDING-待发货,SHIPPED-已发货,IN_TRANSIT-运输中,DELIVERED-已送达,EXCEPTION-异常,CANCELLED-已取消',
  `ship_time` datetime DEFAULT NULL COMMENT '发货时间',
  `estimated_arrival_time` datetime DEFAULT NULL COMMENT '预计到达时间',
  `actual_arrival_time` datetime DEFAULT NULL COMMENT '实际到达时间',
  `receive_time` datetime DEFAULT NULL COMMENT '收货时间',
  `receiver_name` varchar(50) DEFAULT NULL COMMENT '收货人姓名',
  `receiver_contact` varchar(100) DEFAULT NULL COMMENT '收货人联系方式',
  `logistics_cost` decimal(10,2) DEFAULT NULL COMMENT '物流费用',
  `tracking_info` text COMMENT '追踪信息',
  `exception_description` text COMMENT '异常描述',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `delete_flag` varchar(1) NOT NULL DEFAULT '0' COMMENT '删除标记：0-正常,1-删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` bigint(20) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tracking_number` (`tracking_number`),
  KEY `idx_tenant_delete` (`tenant_id`, `delete_flag`),
  KEY `idx_work_order` (`work_order_id`),
  KEY `idx_task` (`task_id`),
  KEY `idx_status` (`status`),
  KEY `idx_carrier` (`carrier_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='物流追踪表';
```

### 6. 质检记录表
```sql
CREATE TABLE `jsh_quality_inspection` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `inspection_number` varchar(50) NOT NULL COMMENT '质检单号',
  `task_id` bigint(20) DEFAULT NULL COMMENT '任务ID',
  `task_number` varchar(50) DEFAULT NULL COMMENT '任务号',
  `work_order_id` bigint(20) DEFAULT NULL COMMENT '工单ID',
  `work_order_number` varchar(50) DEFAULT NULL COMMENT '工单号',
  `product_id` bigint(20) DEFAULT NULL COMMENT '产品ID',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `inspector_id` bigint(20) DEFAULT NULL COMMENT '质检员ID',
  `inspector_name` varchar(50) DEFAULT NULL COMMENT '质检员姓名',
  `inspection_time` datetime DEFAULT NULL COMMENT '质检时间',
  `inspection_type` varchar(50) DEFAULT NULL COMMENT '质检类型',
  `inspection_quantity` decimal(10,2) DEFAULT NULL COMMENT '质检数量',
  `qualified_quantity` decimal(10,2) DEFAULT NULL COMMENT '合格数量',
  `defective_quantity` decimal(10,2) DEFAULT NULL COMMENT '不合格数量',
  `unit_name` varchar(20) DEFAULT NULL COMMENT '单位名称',
  `qualification_rate` decimal(5,2) DEFAULT NULL COMMENT '合格率',
  `overall_result` varchar(20) DEFAULT NULL COMMENT '质检结果：PASS-合格,FAIL-不合格,CONDITIONAL-有条件通过',
  `quality_grade` varchar(10) DEFAULT NULL COMMENT '质量等级：A,B,C,D',
  `overall_score` decimal(3,2) DEFAULT NULL COMMENT '总体评分',
  `appearance_score` decimal(3,2) DEFAULT NULL COMMENT '外观评分',
  `size_score` decimal(3,2) DEFAULT NULL COMMENT '尺寸评分',
  `color_score` decimal(3,2) DEFAULT NULL COMMENT '颜色评分',
  `texture_score` decimal(3,2) DEFAULT NULL COMMENT '质感评分',
  `detail_score` decimal(3,2) DEFAULT NULL COMMENT '细节评分',
  `overall_effect_score` decimal(3,2) DEFAULT NULL COMMENT '整体效果评分',
  `problem_description` text COMMENT '问题描述',
  `improvement_suggestion` text COMMENT '改进建议',
  `quality_photos` text COMMENT '质检照片',
  `inspection_standard` text COMMENT '质检标准',
  `remark` text COMMENT '备注',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `delete_flag` varchar(1) NOT NULL DEFAULT '0' COMMENT '删除标记：0-正常,1-删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` bigint(20) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_inspection_number` (`inspection_number`),
  KEY `idx_tenant_delete` (`tenant_id`, `delete_flag`),
  KEY `idx_task` (`task_id`),
  KEY `idx_work_order` (`work_order_id`),
  KEY `idx_inspector` (`inspector_id`),
  KEY `idx_overall_result` (`overall_result`),
  KEY `idx_quality_grade` (`quality_grade`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='质检记录表';
```

## 🚀 部署步骤

### 1. 数据库初始化
执行上述SQL脚本创建所有必要的数据库表。

### 2. 代码部署
确保所有Java文件已正确放置在对应目录：
- 实体类：`src/main/java/com/jsh/erp/datasource/entities/`
- Mapper接口：`src/main/java/com/jsh/erp/datasource/mappers/`
- XML映射：`src/main/resources/mapper_xml/`
- Service类：`src/main/java/com/jsh/erp/service/`

### 3. 配置验证
- 验证MyBatis Plus配置
- 验证数据源配置
- 验证事务管理配置

### 4. 功能测试
- 测试基础CRUD操作
- 测试业务流程
- 测试统计功能
- 测试权限控制

## 📊 系统功能

### 核心模块
1. **掐丝点蓝制作管理** - 完整的制作流程管理
2. **配饰制作管理** - 工人分配和制作管理
3. **后工任务管理** - 优先级和时间管理
4. **物流追踪管理** - 完整的物流状态追踪
5. **质检管理** - 多维度质量评分和管理
6. **工人信息管理** - 技能、绩效、工作负荷的全面管理

### 智能化功能
- 智能工人推荐
- 优先级管理
- 工作负荷平衡
- 质量预警
- 绩效评估

### 统计分析
- 多维度统计
- 趋势分析
- 绩效排行
- 成本分析

## 🔧 维护说明

### 性能优化
- 定期检查数据库索引
- 监控查询性能
- 优化统计查询

### 数据维护
- 定期清理历史数据
- 备份重要数据
- 监控数据增长

### 系统监控
- 监控系统性能
- 监控业务指标
- 设置预警机制

## 📞 技术支持

如有技术问题，请联系开发团队。

---
**智能生产管理系统 v1.0**  
**开发团队：jshERP**  
**更新时间：2025-06-22**
