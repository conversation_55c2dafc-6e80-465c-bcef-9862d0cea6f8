# jshERP 本地开发环境 Nginx 配置
# 代理前端和后端服务到宿主机

upstream frontend {
    server host.docker.internal:3000;  # 前端开发服务器
}

upstream backend {
    server host.docker.internal:9999;  # 后端服务器
}

server {
    listen       80;
    server_name  localhost;
    
    # 设置客户端请求体大小限制
    client_max_body_size 100m;
    
    # 前端资源代理
    location / {
        proxy_pass http://frontend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket 支持（用于热重载）
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 后端 API 代理
    location /jshERP-boot/ {
        proxy_pass http://backend/jshERP-boot/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 文件上传目录
    location /upload/ {
        alias /opt/jshERP/upload/;
        expires 1d;
        add_header Cache-Control "public, immutable";
    }
    
    # 健康检查端点
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # 错误页面
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
} 