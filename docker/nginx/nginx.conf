# jshERP Nginx 主配置文件

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    # 访问日志
    access_log /var/log/nginx/access.log main;

    # 基础配置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;    # Gzip 压缩配置
    gzip on;
    gzip_vary on;
    gzip_min_length 1k;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # 客户端配置
    client_max_body_size 10M;
    client_body_buffer_size 128k;
    client_header_buffer_size 32k;
    large_client_header_buffers 4 32k;

    # 上游服务器配置
    upstream jsherp_backend {
        server jsherp-backend:9999 weight=1 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    upstream jsherp_frontend {
        server jsherp-frontend:8080 weight=1 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    # 包含虚拟主机配置
    include /etc/nginx/conf.d/*.conf;
}