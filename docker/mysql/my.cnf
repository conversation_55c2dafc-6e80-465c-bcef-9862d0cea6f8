# jshERP MySQL 配置文件
# 针对中文支持和性能优化

[mysqld]
# 基础配置
default-storage-engine=INNODB
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci
init_connect='SET NAMES utf8mb4'

# 时区配置
default-time-zone='+08:00'

# 连接配置
max_connections=1000
max_connect_errors=1000
max_allowed_packet=64M
interactive_timeout=3600
wait_timeout=3600

# InnoDB 配置
innodb_buffer_pool_size=512M
innodb_log_file_size=128M
innodb_log_buffer_size=32M
innodb_flush_log_at_trx_commit=1
innodb_lock_wait_timeout=120
innodb_flush_method=O_DIRECT
innodb_file_per_table=1

# 查询缓存
query_cache_type=1
query_cache_size=32M
query_cache_limit=2M

# 慢查询日志
slow_query_log=1
slow_query_log_file=/var/log/mysql/slow.log
long_query_time=2

# 二进制日志
server-id=1
log-bin=mysql-bin
binlog_format=ROW
expire_logs_days=7

# 错误日志
log-error=/var/log/mysql/error.log

[mysql]
default-character-set=utf8mb4

[client]
default-character-set=utf8mb4